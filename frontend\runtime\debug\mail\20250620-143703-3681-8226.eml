Message-ID: <<EMAIL>>
Date: Fri, 20 Jun 2025 14:37:01 +0800
Subject: http://dev.eln.integle.com/ 2025-06-20 14:37:00
 =?UTF-8?Q?=E7=B3=BB=E7=BB=9F=E6=8A=A5=E9=94=99?=
From: Integle message <<EMAIL>>
To: <EMAIL>, <EMAIL>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: quoted-printable

2025-06-20 14:37:01
[::1][-][0vd1scddjptlgvq6v4umqol4j7][error][yii\db\Exception]
exception 'PDOException' with message 'SQLSTATE[22007]: Invalid
datetime format: 1292 Incorrect datetime value: '2025-06-21 24:00:00'
for column 'end_time' at row 1' in
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php:798
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(798):
PDOStatement->execute()
#1 D:\integle2025\eln_trunk\common\components\Command.php(29):
yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('instruments_boo...', Array)
#4
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal(NULL)
#5
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593):
yii\db\ActiveRecord->insert(true, NULL)
#6
D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854=
):
frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0,
Array, Array, '')
#8 [internal function]:
frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55):
call_user_func_array(Array, Array)
#10
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('handle-instrume...', Array)
#12
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('instrument/hand...', Array)
#13
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}

Next exception 'yii\db\Exception' with message 'SQLSTATE[22007]:
Invalid datetime format: 1292 Incorrect datetime value: '2025-06-21
24:00:00' for column 'end_time' at row 1
The SQL being executed was: INSERT INTO `instruments_book`
(`instrument_id`, `start_time`, `end_time`, `related_experiment`,
`remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21
23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)'
in D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php:628
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(808):
yii\db\Schema->convertException(Object(PDOException), 'INSERT INTO
`in...')
#1 D:\integle2025\eln_trunk\common\components\Command.php(29):
yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('instruments_boo...', Array)
#4
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal(NULL)
#5
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593):
yii\db\ActiveRecord->insert(true, NULL)
#6
D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854=
):
frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0,
Array, Array, '')
#8 [internal function]:
frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55):
call_user_func_array(Array, Array)
#10
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('handle-instrume...', Array)
#12
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('instrument/hand...', Array)
#13
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}
Additional Information:
Array
(
    [0] =3D> 22007
    [1] =3D> 1292
    [2] =3D> Incorrect datetime value: '2025-06-21 24:00:00' for column
'end_time' at row 1
)

2025-06-20 14:37:00
[::1][-][0vd1scddjptlgvq6v4umqol4j7][info][application] $_GET =3D [
    'r' =3D> 'instrument/handle-instrument-booking'
]

$_COOKIE =3D [
    'eln_page_limit' =3D> '15'
    'ldap_check' =3D> '0'
    'integle_session' =3D> '0vd1scddjptlgvq6v4umqol4j7'
    'sims_u' =3D> '38828f261ee60584144cf546b2ff9ece'
    'lock_interval' =3D> '180'
    'center_language' =3D> 'CN'
    'dataview_id' =3D> '101'
    'page_type' =3D> '1'
    'last_active_time' =3D> '1750401420407'
]

$_SESSION =3D [
    '__flash' =3D> []
    'userinfo' =3D> [
        'user_id' =3D> '1135'
        'email' =3D> null
        'name' =3D> 'chenqi'
        'phone' =3D> null
        'ticket' =3D> '38828f261ee60584144cf546b2ff9ece'
        'reg_time' =3D> '1744077856'
        'Token' =3D> '7eb44480540d6e80df79fce77c791828'
        'register_from' =3D> ''
        'from_ldap' =3D> '0'
        'gender' =3D> '0'
        'nick_name' =3D> ''
        'contact_phone' =3D> ''
        'real_name' =3D> '=E9=99=88=E5=A5=87'
        'point' =3D> '0'
        'company_name' =3D> ''
        'job' =3D> ''
        'office_phone' =3D> ''
        'qq' =3D> ''
        'country' =3D> ''
        'province' =3D> ''
        'city' =3D> ''
        'detail_address' =3D> ''
        'post_code' =3D> ''
        'id_card' =3D> ''
        'big_img' =3D> ''
        'small_img' =3D> ''
        'unread_message' =3D> '2'
        'default_group' =3D> '0'
        'contact_email' =3D> ''
        'role_ids' =3D> '1,84'
        'department' =3D> []
        'id' =3D> '1135'
        'groups' =3D> [
            0 =3D> [
                'id' =3D> '1'
                'name' =3D> '=E5=85=AC=E5=8F=B8=E7=BE=A4'
                'role' =3D> '1'
            ]
            1 =3D> [
                'id' =3D> '598'
                'name' =3D> 'cq1'
                'role' =3D> '3'
            ]
        ]
        'current_company_id' =3D> '1'
        'app_access' =3D> 1
    ]
    'eln_lang' =3D> 'zh-CN'
]

$_SERVER =3D [
    'MIBDIRS' =3D> 'D:/xampp/php/extras/mibs'
    'MYSQL_HOME' =3D> '\\xampp\\mysql\\bin'
    'OPENSSL_CONF' =3D> 'D:/xampp/apache/bin/openssl.cnf'
    'PHP_PEAR_SYSCONF_DIR' =3D> '\\xampp\\php'
    'PHPRC' =3D> '\\xampp\\php'
    'TMP' =3D> '\\xampp\\tmp'
    'HTTP_HOST' =3D> 'dev.eln.integle.com'
    'HTTP_CONNECTION' =3D> 'keep-alive'
    'CONTENT_LENGTH' =3D> '237'
    'HTTP_X_REQUESTED_WITH' =3D> 'XMLHttpRequest'
    'HTTP_USER_AGENT' =3D> 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)
AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' =3D> 'application/json'
    'CONTENT_TYPE' =3D> 'application/json'
    'HTTP_ORIGIN' =3D> 'http://dev.eln.integle.com'
    'HTTP_REFERER' =3D> 'http://dev.eln.integle.com/'
    'HTTP_ACCEPT_ENCODING' =3D> 'gzip, deflate'
    'HTTP_ACCEPT_LANGUAGE' =3D> 'zh-CN,zh;q=3D0.9'
    'HTTP_COOKIE' =3D> 'eln_page_limit=3D15; ldap_check=3D0;
integle_session=3D0vd1scddjptlgvq6v4umqol4j7;
sims_u=3D38828f261ee60584144cf546b2ff9ece; lock_interval=3D180;
center_language=3DCN; dataview_id=3D101; page_type=3D1;
last_active_time=3D1750401420407'
    'PATH' =3D> 'C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\javapath;C:\\Program Files
(x86)\\PerkinElmerInformatics\\ChemOffice2017\\ChemScript\\Lib;C:\\WINDOWS\=
\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\=
\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program
Files\\dotnet\\;C:\\Program Files
(x86)\\DSOC\\ExtractContent;C:\\Program Files
(x86)\\DSOC\\ExtractContent64\\OCR;D:\\Program
Files\\TortoiseSVN\\bin;D:\\Program
Files\\Java\\jdk-1.8\\bin;D:\\Program
Files\\php\\php-5.6.40-Win32-VC11-x64;D:\\composer;D:\\Program
Files\\Git\\cmd;D:\\Program
Files\\nodejs\\node_global\\node_modules;D:\\nvm;D:\\nvm4w\\nodejs;D:\\Pro=
gram
Files\\nodejs\\node_global;D:\\Program
Files\\wget-1.21.4-win64;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users=
\\chenc\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program
Files\\JetBrains\\IntelliJ IDEA 2024.1.4\\bin;;D:\\Program
Files\\JetBrains\\PhpStorm
2024.1.4\\bin;;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:=
\\Program
Files\\JetBrains\\WebStorm
2024.1.5\\bin;;D:\\Users\\chenc\\AppData\\Local\\Programs\\Microsoft
VS Code\\bin;D:\\Program Files\\cursor\\resources\\app\\bin'
    'SystemRoot' =3D> 'C:\\WINDOWS'
    'COMSPEC' =3D> 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' =3D>
'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'WINDIR' =3D> 'C:\\WINDOWS'
    'SERVER_SIGNATURE' =3D> '<address>Apache/2.4.38 (Win64)
OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port
80</address>
'
    'SERVER_SOFTWARE' =3D> 'Apache/2.4.38 (Win64) OpenSSL/1.0.2q
PHP/5.6.40'
    'SERVER_NAME' =3D> 'dev.eln.integle.com'
    'SERVER_ADDR' =3D> '::1'
    'SERVER_PORT' =3D> '80'
    'REMOTE_ADDR' =3D> '::1'
    'DOCUMENT_ROOT' =3D> 'D:/integle2025/eln_trunk/frontend/web'
    'REQUEST_SCHEME' =3D> 'http'
    'CONTEXT_PREFIX' =3D> ''
    'CONTEXT_DOCUMENT_ROOT' =3D> 'D:/integle2025/eln_trunk/frontend/web'
    'SERVER_ADMIN' =3D> 'postmaster@localhost'
    'SCRIPT_FILENAME' =3D>
'D:/integle2025/eln_trunk/frontend/web/index.php'
    'REMOTE_PORT' =3D> '62901'
    'GATEWAY_INTERFACE' =3D> 'CGI/1.1'
    'SERVER_PROTOCOL' =3D> 'HTTP/1.1'
    'REQUEST_METHOD' =3D> 'POST'
    'QUERY_STRING' =3D> 'r=3Dinstrument/handle-instrument-booking'
    'REQUEST_URI' =3D> '/?r=3Dinstrument/handle-instrument-booking'
    'SCRIPT_NAME' =3D> '/index.php'
    'PHP_SELF' =3D> '/index.php'
    'REQUEST_TIME_FLOAT' =3D> 1750401420.507
    'REQUEST_TIME' =3D> 1750401420
]