<?php
namespace frontend\controllers;

use common\components\CommonCurl;
use common\components\Download;
use common\components\Encryption;
use common\components\helpers\ElnVersionHelper;
use common\components\Picture;
use common\components\Upload;
use frontend\core\CommonInterface;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\interfaces\PMInterface;
use frontend\models\CompanyAuthSetting;
use frontend\models\ExpBookRecordModel;
use frontend\models\ExperimentModel;
use frontend\models\GlobalExpExportModel;
use frontend\models\InstrumentBindingFileServerModel;
use frontend\models\InstrumentBindingModel;
use frontend\models\InstrumentDataFileModel;
use frontend\models\InstrumentDataLogModel;
use frontend\models\InstrumentDataNumericalModel;
use frontend\models\InstrumentsModel;
use frontend\models\UploadModel;
use frontend\models\UserCompanyRole;
use frontend\models\UserGroupRole;
use frontend\models\UseStatic;
use frontend\models\UseStaticLogin;
use frontend\services\BookServer;
use frontend\services\CollaborationServer;
use frontend\services\CompanyAuthServer;
use frontend\services\ExperimentServer;
use frontend\services\ExportServer;
use frontend\services\GroupSettingServer;
use frontend\services\InsequenceServer;
use frontend\services\InstrumentServer;
use frontend\services\SignServer;
use frontend\services\TempleServer;
use frontend\tools\MqThreadPoolExecutor;
use yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\helpers\ArrayHelper;

/**
 * eln鹰群的设置
 *
 * <AUTHOR> @copyright 2016-11-12
 */
class ElnInterfaceController extends CommonInterface {
    public $enableCsrfValidation = false ; //局部关掉 csrf 验证

    public function beforeAction($action) {
        // 部分接口暂不进行api_secret验证，主要因为从浏览器上直接发出请求，不好携带api_secret
        $allowActions = [
            'upload-x-sheet-image',
            'upload-x-sheet-file',
            'excel-preview',
            'get-exp-upload-file',
            'check-file-status',
            'get-exp-upload-file',
            'update-file-content',
            'unlock-file',
            'get-comment-data',
            'save-comment-data',
            'update-comment-data',
            'read-excel',
            'read-excel-new',
            'get-xs-temp',
            'get-xs-temp-by-id',
            'save-xs-temp',
            'save-print-config',
            'print-pdf',
            'print-html',
            'instrument-file-connection-check',
            'sync-instrument-file',
            'save-instrument-file-index',
            'get-instrument-info',
            'instrument-data-connection-check',
            'save-instrument-command',
            'sync-instrument-data',
            'save-instrument-connection-error',
            'check-file-server-config',
            'save-file-sever-config',
            'get-sequence-image', // 动态获取sequence的svg，不通过鉴权
        ];

        if (!in_array($action->id, $allowActions)) {
            // 对请求进行api_secret验证
            $reqApiSecret = \Yii::$app->request->get('api_secret', '');
            if (empty($reqApiSecret)) {
                $reqApiSecret = \Yii::$app->request->post('api_secret', '');
            }

            $apiSecret = defined('API_SECRET') ? API_SECRET : '';
            if ($reqApiSecret !== $apiSecret) {
                Yii::$app->end(0, $this->fail('Invalid API SECRET'));
            }
        }

        return parent::beforeAction($action);
    }

    /**
     * 微信获取记录本列表
     * ?r=eln-interface/wechat-book-list
     *
     * <AUTHOR> @copyright 2017-8-28
     *
     * @param uid 用户id 必须
     * @param search_word 搜索的内容
     *
     * @return arary
     */
    public function actionWechatBookList(){
    	$postData = \Yii::$app->getRequest()->post();

    	if(empty($postData['uid'])){
    		return $this->fail(\Yii::t('base', 'lost_params').': uid');
    	}

    	$uid = $postData['uid'];
    	$companyId = isset($postData['company_id']) ? $postData['company_id'] : 0 ;

    	$search = !empty($postData['search_word']) ? $postData['search_word'] : '';

    	$groupList = (new CenterInterface())->elnGroupListByUserId($uid, $companyId);

    	$bookList = (new BookServer())->listBookByUser([
    	        'user_id' => $uid,
    	        'group_list' => array_column($groupList, 'group_id'),
    	        'search' => $search
    	        ]);

    	if(empty($bookList['status'])){
    		return $this->fail($bookList['info']);
    	}

    	return $this->success($bookList['data']);
    }

    /**
     * 微信端实验列表
     * ?r=eln-interface/wechat-exp-list
     *
     * <AUTHOR> @copyright 2017-8-28
     *
     * @param uid 用户id 必须
     * @param book_id 记录本id 必须
     * @param search_word 搜索的实验编号或者关键字或者标题
     * @param page
     * @param limit
     *
     * @return ['count','expList']
     */
    public function actionWechatExpList(){
        $postData = \Yii::$app->getRequest()->post();

        if(empty($postData['uid']) || empty($postData['book_id'])){
            return $this->fail(\Yii::t('base', 'lost_params').': uid,book_id');
        }

        $uid = $postData['uid'];
        $companyId = isset($postData['company_id']) ? $postData['company_id'] : 0 ;
        $search = !empty($postData['search_word']) ? $postData['search_word'] : '';
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];

        $groupList = (new CenterInterface())->elnGroupListByUserId($uid, $companyId);
        $postData['group_id'] = array_column($groupList, 'group_id');

        $expList = (new ExperimentServer())->wechatExpList($postData, $page, $limit);

        \Yii::info('获取的微信的实验列表数据为：'.json_encode($expList));

        if(empty($expList['status'])){
            return $this->fail($expList['info']);
        }

        return $this->success($expList['data']);
    }

    /**
     * 保存微信端上传的图片
     * ?r=eln-interface/wechat-save-img
     *
     * $postData = [
            'uid' => 13234,
            'exp_id' => 22152,
            'img_data' => [
                [
                    'dep_path' => '20170828',
                    'save_name' => '59a3cccf2becf.jpg',
                    'file_name' => '2.jpg',
                    'remark' => '图片111'
                ],
                [
                    'dep_path' => '20170828',
                    'save_name' => '59a3cccf2becf.jpg',
                    'file_name' => '2.jpg',
                    'remark' => '图片222'
                ]
            ]
        ];

     * <AUTHOR> @copyright 2017-8-28
     * @return array
     */
    public function actionWechatSaveImg() {
        $postData = \Yii::$app->getRequest()->post();

        if(empty($postData['uid']) || empty($postData['exp_id']) || empty($postData['img_data'])){
            return $this->fail(\Yii::t('base', 'lost_params').': uid,exp_id,img_data');
        }

        $uid = $postData['uid'];
        $expId = $postData['exp_id'];
        $imgData = json_decode($postData['img_data'], TRUE);

        $componentId = \Yii::$app->params['component']['wechat_pic'];
        $picData['name'] = '微信上传图片';

        $picData['img_data'] = $imgData;
        $data = (new ExperimentServer())->wechatSaveImg($expId, $uid, $componentId, $picData);

        if(empty($data['status'])){
        	return $this->fail($data['message']);
        }

        return $this->success($data['data'], '上传成功');
    }

///////////////////////////以下内容貌似作废////////////////////////////////////////////////
///////////////////////////以下内容貌似作废////////////////////////////////////////////////

    /**
     * 获取鹰群的eln的管理员
     * <AUTHOR> @copyright 2016-11-12
     */
    public function actionGetElnAdmin(){
        $postData = \Yii::$app->getRequest()->post();

        return (new GroupSettingServer())->getElnAdmin($postData);
    }

    /**
     * 设置eln的管理员
     *
     * <AUTHOR> @copyright 2016-11-12
     * @return \common\controllers\json
     */
    public function actionSetElnAdmin(){
    	$postData = \Yii::$app->getRequest()->post();
    	return (new GroupSettingServer())->setElnAdmin($postData);
    }

    /**
     * 获取鹰群复核设置
     */
    public function actionGetGroupSignSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }
        $groupId = $postData['group_id'];
        $data = (new SignServer())->getSignGroup($groupId);

        $file = $this->renderPartial('/setting/group_sign_temp.php', ['sign_data'=>$data]);
        return $this->ajaxSuccess($file);
    }

   /**
    * 设置鹰群复核设置
    *
    * <AUTHOR> @copyright 2016-11-12
    * @return \common\controllers\json
    */
    public function actionSetGroupSignSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_group'));
        }

        $data = [
            'un_allow_edit' => empty($postData['un_allow_edit']) ? 0 : intval($postData['un_allow_edit']),
            'force_sign' => empty($postData['force_sign']) ? 0 : intval($postData['force_sign']),
            'plan_day' => empty($postData['plan_day']) ? 0 : intval($postData['plan_day']),
        ];

        $result = (new SignServer())->addSignGroup($postData['group_id'], $data);

        if(isset($result['status']) && (1 == $result['status'])){
        	return $this->success($result['data']);
        }

        return $this->fail($result['info']);
    }


    /**
     * 获取打印设置
     *
     * <AUTHOR> @copyright 2016-10-29
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionGetPrintSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_group'));
        }

        if (empty($postData['user_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_user'));
        }

        $pro = new P\FindPrintSetting();
        $pro->group_id = $postData['group_id'];
        $pro->user_id = $postData['user_id'];
        $printSetData = $pro->queryOne();

        $file = $this->renderPartial('/group/eln/print_temp.php', ['printSetData'=>$printSetData]);
        return $this->ajaxSuccess($file);
    }

    /**
     * 设置打印设置
     *
     * <AUTHOR> @copyright 2016-10-29
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionSetPrintSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_group'));
        }

        if (empty($postData['user_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_user'));
        }

        $pro = new P\AddPrintSetting();
        $pro->user_id = $postData['user_id'];
        $pro->group_id = $postData['group_id'];
        $pro->pdf_book = isset($postData['pdf_book']) ? $postData['pdf_book'] : 0;
        $pro->pdf_single = isset($postData['pdf_single']) ? $postData['pdf_single'] : 0;
        $pro->word_book = isset($postData['word_book']) ? $postData['word_book'] : 0;
        $pro->word_single = isset($postData['word_single']) ? $postData['word_single'] : 0;

        if($pro->execute()){
        	return $this->ajaxSuccess(\Yii::t('base', 'success'));
        }

        return $this->ajaxFail(\Yii::t('base', 'failed'));
    }

    /**
     * 获取成员复核设置
     *
     * <AUTHOR> @copyright 2016-10-29
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionGetMemberSignSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_group'));
        }

        if (empty($postData['user_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_user'));
        }

        // 获取成员的设置
        $pro = new P\GetGroupMemberSignSet();
        $pro->group_id = $postData['group_id'];
        $pro->user_id = $postData['user_id'];

        $signData = $pro->queryOne();

        // 获取当前鹰群下的用户
        $groupMember = (new GroupServer())->listMember($postData['group_id']);

        if(empty($groupMember['data'])){
        	$groupMember = [];
        }

        if(!empty($groupMember['data'])){
        	$groupMember = $groupMember['data'];
        }

        $file = $this->renderPartial('/group/eln/sign_temp.php', [
            'curr_user_id' => $postData['user_id'],
            'signData' => $signData,
            'groupMember' => $groupMember
        ]);

        return $this->ajaxSuccess($file);
    }

    /**
     * 设置成员复核设置
     *
     * <AUTHOR> @copyright 2016-10-29
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionSetMemberSignSet() {
        $pro = new P\AddGroupMemberSign();

        $postData = \Yii::$app->getRequest()->post();


        if (empty($postData['group_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_group'));
        }

        if (empty($postData['user_id'])) {
            return $this->ajaxFail(\Yii::t('base', 'select_user'));
        }


        $pro = new P\AddGroupMemberSign();
        $pro->group_id = intval($postData['group_id']);
        $pro->user_id = intval($postData['user_id']);
        $pro->plan_day = !empty($postData['plan_day']) ? intval($postData['plan_day']) : 0;

        $emails = '';
        $errInfo = [];

        if(!empty($postData['emails'])){
        	$signUsers = explode(',', str_replace('，', ',', $postData['emails']));

        	foreach ($signUsers as $user) {
        	    if (!empty($user)) {
        	        // 获取用户id
        	        $userinfo = (new UserServer())->getUserInfoByInput(trim($user));

        	        if (isset($userinfo['status']) && !$userinfo['status']) {
        	            $errInfo .= $user . $userinfo['message'] . '<br/>';
        	            continue;
        	        }

        	        $emails[] = !empty($userinfo['data']['email']) ? $userinfo['data']['email'] : ($userinfo['data']['phone']);
        	    }
        	}
        }

        $pro->emails = !empty($emails) ? implode(',', $emails) : '';
        $pro->sign_user_ids = isset($postData['sign_user_ids']) ? implode(',', $postData['sign_user_ids']) : '';

        if($pro->execute() && empty($errInfo)){
        	return $this->ajaxSuccess(\Yii::t('base', 'success'));
        }

        if(!empty($errInfo)){
        	return $this->ajaxFail($errInfo);
        }

        return $this->ajaxFail(\Yii::t('base', 'failed'));
    }
    /*
     * 未使用
     * */
    public function actionGetSmilesInfo(){

        $expId = \Yii::$app->getRequest()->post();

        $experiment = new ExperimentServer();

        $userId = $experiment -> getUserByExperimentId($expId);

        $groupId = (new ExperimentServer())->getGroupIdByExperimentIds($expId,$userId);
        $saltType = '';
        $saltNum = '';

        return [
            'group_id' => empty( $groupId['data'][0]) ? '' : $groupId['data'][0],
            'salt_type' => empty($saltType) ? '' : $saltType,
            'salt_num' => empty($saltNum) ? '' : $saltNum
        ];

    }

    /**
     * 项目管理（获取项目下实验数据）
     * /?r=eln-interface/exp-count-by-project-id
     * <AUTHOR>
     * @copyright 2017-11-23
     */
    public function actionExpCountByProjectId(){
        $projectId = \Yii::$app->getRequest()->post('project_id');
        $taskId = \Yii::$app->getRequest()->post('task_id');
        $userIds = \Yii::$app->getRequest()->post('user_ids');

        if(empty($projectId) ){
            return $this->fail(\Yii::t('base', 'lost_params').': project_id');
        }

        $data = (new ExperimentServer())->expCountByTaskId($projectId, $taskId, $userIds);

        return $this->success($data['data']);
    }

    /**
     * 项目管理（获取项目实验时间）
     * /?r=eln-interface/exp-time-by-project-id
     * <AUTHOR>
     * @copyright 2022/1/17
     */
    public function actionExpTimeByProjectId(){
        $projectId = \Yii::$app->getRequest()->post('project_id');
        $bindType = \Yii::$app->getRequest()->post('bind_type');

        if(empty($projectId) ){
            return $this->fail(\Yii::t('base', 'lost_params').': project_id');
        }

        $data = (new ExperimentServer())->expTimeByProjectId($projectId, $bindType);

        return $this->success($data['data']);
    }


    /*
     * 平台待办事项（待复核实验列表）
     * ?r=eln-interface/sign-exp-list
     * <AUTHOR>
     * @copyright 2017-12-13
     * */
    public function actionSignExpList(){
        $postData = \Yii::$app->request->post();
        if(empty($postData['user_id'])){
            return $this->fail(\Yii::t('base', 'lost_params').': user_id');
        }

        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];

        $result = (new ExperimentServer())->signExpList($postData['user_id'], $page, $limit);

        return $this->success($result['data']);
    }

    /**
     * 项目管理（获取项目下实验数据）
     * /?r=eln-interface/exp-count-by-project-id
     * <AUTHOR>
     * @copyright 2017-11-23
     */
    public function actionSaveXsTemp(){
        header("Access-Control-Allow-Origin:*");

       $postData =  Yii::$app->request->getRawBody();

       $postArr = json_decode($postData,true);

//        $needApproval = isset($postArr['uid']) && TempleServer::needApproval($postArr['uid']);
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[$postArr['uid']],1);//模板审批逻辑修改，先弃用原来的审批方式
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息

        $data['insertData']['type'] = 4;
        $data['insertData']['tfrom'] = 2;
        $data['insertData']['user_id'] = isset($postArr['uid'])?$postArr['uid']:0;
        $data['insertData']['email'] = '';
        $data['insertData']['edit_user_id'] = '';
        $data['insertData']['title'] = !empty($postArr['templateName']) ? $postArr['templateName'] : '';
        $data['insertData']['name'] = !empty($postArr['templateName']) ? $postArr['templateName'] : '';
        $data['insertData']['keywords'] =  '';
        $data['insertData']['descript'] = !empty($postArr['templateDesc']) ? $postArr['templateDesc'] : '';
        $data['insertData']['content'] = !empty($postArr['templateContent']) ? $postArr['templateContent'] : '';
        $data['insertData']['img'] = !empty($postArr['imgData']) ? $postArr['imgData'] : '';
        $data['insertData']['step'] = 3; //$needApproval ? 1 : 3;//bug#30748,表格模板目前还没有审批类型，先视作不审批，22.12.20 mod dx

        $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', 'normal_function', $userDetail, 0);
        if ($needApproval) {
            $data['insertData']['step'] = 1;
        }

        $saveResult = (new TempleServer())->addXsTemp($data, 'add');

        return $this->success([
            'message' => \Yii::t('base', 'save_success'),
            'id' => $saveResult['data'],
            'need_approval' => $needApproval,
        ]);
    }

    /**
     * 项目管理（获取项目下实验数据）
     * /?r=eln-interface/exp-count-by-project-id
     * <AUTHOR>
     * @copyright 2017-11-23
     */
    public function actionGetXsTemp(){
        header("Access-Control-Allow-Origin:*");

        $postData =  Yii::$app->request->getRawBody();

        $postArr = json_decode($postData,true);

        $data['type'] = 4;
        $data['user_id'] = isset($postArr['uid']) ? $postArr['uid'] : 0;
        $saveResult = (new TempleServer())->xsListTemp($data);
        if($saveResult){
            return $this->success($saveResult['data']);
        } else {
            return $this->fail('');
        }
    }

    /**
     * 项目管理（获取项目下实验数据）
     * /?r=eln-interface/exp-count-by-project-id
     * <AUTHOR>
     * @copyright 2017-11-23
     */
    public function actionGetXsTempById(){
        header("Access-Control-Allow-Origin:*");

       $postData =  Yii::$app->request->getRawBody();

       $postArr = json_decode($postData,true);

        $data['type'] = 4;
        $data['user_id'] = isset($postArr['uid'])?$postArr['uid']:0;
        $data['id'] = isset($postArr['templateId'])?$postArr['templateId']:0;
        $saveResult = (new TempleServer())->getXsTempById($data['id']);
        if($saveResult){
            return $this->success($saveResult['data']);
        } else {
            return $this->fail('');
        }
    }

    /**
     * Notes: 用于xsheet图片跨域上传
     * Author: hkk
     * Date: 2020/3/31 10:35
     * @return array
     */
    public function actionUploadXSheetImage() {
        header("Access-Control-Allow-Origin:*");

        $postData = Yii::$app->request->getRawBody();
        $postData = json_decode($postData, true);
        $base64 = $postData['upfile'];

        $type = 1;
        $imgData = (new Picture())->base64ToImageNew($base64, $type, [
            'save_name' => @getVar($postData['name']),
            'deep_path' => @getVar($postData['path']),
        ]);

        // 图片路径
        $imgSrc = PIC_URL . (new Picture())->getSourcePicture($imgData['deepPath'], $imgData['saveName']);
        $imgSrc .= '?savename=' . $imgData['saveName'] . '&dep_path=' . $imgData['deepPath'];
        $imgData['imgSrc'] = $imgSrc;

        return [
            'state' => 'SUCCESS',
            'url' => $imgSrc,
            'title' => $imgData['saveName'],
            'original' => $imgData['saveName'],
            'name' => $imgData['saveName'],
            'path' => $imgData['deepPath'],
        ];
    }

    /**
     * @Notes: 用于xsheet文件跨域上传
     * @author: tianyang
     * @Time: 2023/1/4 14:36
     */
    public function actionUploadXSheetFile()
    {
        header("Access-Control-Allow-Origin:*");

        $upload = new \common\components\Upload([
            'mimes'    => [
                'image/png',
                'image/gif',
                'image/jpeg',
                'image/bmp',
                'image/x-ms-bmp',
                'text/x-php',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/octet-stream',
                'application/vnd.ms-office',
                'application/vnd.ms-excel',
                'application/excel',
                'application/msexcel',
                'text/plain',
                'application/pdf',
                'application/zip',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/html',
                'image/tiff'
            ],
            //'exts' => 'gif,jpeg,jpg,png,bmp,txt,xls,xlsx,pdf,zip,doc,docx,ppt,pptx,tif',
            'exts'     => '', //限制输入的格式
            'maxSize'  => UPLOAD_MAX_SIZE,
            'rootPath' => '@filepath'
        ]);

        $result = $upload->upload($_FILES);

        if (!isset($result['status'])) {
            return [
                'state' => 'FAIL',
                'message' => $result,
            ];
        }
        $fileInfo = $result['data']['file'];

        $url = ELN_URL
            . '?r=download/file&type=2&path=' . $fileInfo['savepath']
            . '&name=' . $fileInfo['savename']
            . '&file_name=' . urlencode($fileInfo['name']);

        return [
            'state'    => 'SUCCESS',
            'url'      => $url,
            'title'    => $fileInfo['name'],
            'original' => $fileInfo['name'],
            'name'     => $fileInfo['savename'],
            'path'     => $fileInfo['savepath'],
        ];
    }

    /**
     * Notes: 老的XSHEET转HTML方法(废弃)
     * Author: lcy
     * Date: 2020/3/30 10:35
     * @return string
     */
    public function actionPrint() {
        header("Access-Control-Allow-Origin:*");

       $postData =  Yii::$app->request->getRawBody();
       echo $postData;exit;
        $data = $postData;

        $html = $this->renderAjax('/file/pdf_test.php', [
            'integleTableData' => $data,
        ]);

        return $this->success($html);

}

    /**
     * Notes: 打开pdf
     * Author: lcy
     * Date: 2020/3/30 10:35
     * @return string
     */
    public function actionPrintHtml() {
        $file_name =  \Yii::$app->request->get('filename');
        $filePath = \Yii::getAlias('@svgpath') . DS . $file_name;
        //echo $filePath;exit;
        header('Content-type: application/pdf');

        if(!empty($filePath)){
            echo @file_get_contents($filePath);
        }
        exit;

    }

    /**
     * Notes: intable 转 pdf
     * url: print-pdf
     * Author: lcy
     * Date: 2020/3/30 10:35
     * @return array|string
     */
    public function actionPrintPdf() {
        header("Access-Control-Allow-Origin:*");

        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);

        $config = $postDataArr['printSetting'];
        $pageSize = isset($config['paperSpecs']) ? $config['paperSpecs'] : 'A4';
        $direction = isset($config['paperDirection']) ? $config['paperDirection'] : 'vertical';

        $html = $this->renderAjax('/file/pdf_pdf.php', [
            'integleTableData' => $postDataArr,
        ]);
        //echo $html;exit;
        $htmlName = \Yii::getAlias('@svgpath') . DS .  date('YmdHis',time()) . '.html';
        $fp = fopen($htmlName, 'a+');
        fwrite($fp, $html);
        fclose($fp);

        $headerHtml='';
        $foot='';
        $pdfName = date('YmdHis',time()).'.pdf';
        $pdf = \Yii::getAlias('@svgpath') . DS .$pdfName ;

        /*
         * --orientation Landscape 横向
         * --orientation Portrait 纵向
         */
        $landscape = 0;
        if($direction == 'lateral'){
            $landscape = 1;
        }

        // margin 同 intable 内切页设置
        $html2pdfRes = (new ExportServer())->html2pdfIndex([
            'input_path' => $htmlName,
            'output_path' => $pdf,
            'format' => $pageSize,
            'landscape' => $landscape,
            'headerTemplate' => $headerHtml,
            'footerTemplate' => $foot,
            'margin' => [
                'top' => '19.1mm',
                'right' => '17.8mm',
                'bottom' => '19.1mm',
                'left' => '17.8mm',
            ]
        ]);

        if (!empty($html2pdfRes['status'])) {
            return $this->success($pdfName);
        }

        return $this->fail('打印失败');

    }


    /**
     * Notes: 保存打印设置的接口
     * Author: lcy
     * Date: 2020/3/30 10:35
     * @return string
     */
    public function actionSavePrintConfig() {
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        if($postDataArr['relayId']){
            $result = (new ExperimentServer())->setPrintConfig($postDataArr['typeId'], $postDataArr['relayId'], $postDataArr['printConfig']);
        }
        return $this->success($result);
    }

    /**
     * Notes:更新InTable评论数据新接口
     * Author: hkk
     * Date: 2020/6/12 14:36
     * @return array
     */
    public function actionUpdateCommentData() {
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        if (empty($postDataArr['typeId']) || $postDataArr['typeId'] != 1) {
            return $this->fail('非实验不可以批注');
        }
        if($postDataArr['relayId']){
            $result = (new \frontend\services\modules\XSheetServer())->updateCommentData($postDataArr);
        }
        return $this->success($result);
    }

    public function actionUpdateCommentsData(){
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        if($postDataArr['relayId']){
            $result = (new \frontend\services\modules\XSheetServer())->updateCommentsData($postDataArr);
        }
        return $this->success($result);
    }


    /**
     * Notes: 保存评论到数据库
     * Author: lcy
     * Date: 2020/5/15 10:35
     * @return string
     */
    public function actionSaveCommentData() {
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        // changed by xyx 2023.8.16 cms访问接口没有relayId会报错
        $result = [];
        if(isset($postDataArr['relayId'])){
            $result = (new \frontend\services\modules\XSheetServer())->saveCommentData($postDataArr['relayId'],$postDataArr['typeId'],  $postDataArr['commentsData']);
        }
        return $this->success($result);
    }

    /**
     * Notes: 获取评论的数据
     * Author: lcy
     * Date: 2020/5/15 10:35
     * @return string
     */
    public function actionGetCommentData() {
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        if($postDataArr['relayId']){
            $result = (new \frontend\services\modules\XSheetServer())->getCommentData($postDataArr['relayId'],$postDataArr['typeId']);
        }
        return $this->success($result);
    }

    /**
     * Notes: 初始化群主权限
     * Author: lcy
     * Date: 2020/6/16 10:35
     * @return string
     */
    public function actionInitCompanyAuth() {
        header("Access-Control-Allow-Origin:*");
        $postData = \Yii::$app->request->post();
        (new CompanyAuthServer())->initDefaultCompanyAuth($postData['company_id'],$postData['group_id'],$postData['master_id'],$postData['manage_id']);

        return $this->success([]);
    }

    /**
     * Notes: 取消群主权限
     * Author: lcy
     * Date: 2020/6/16 10:35
     * @return string
     */
    public function actionCancelCompanyAuth() {
        header("Access-Control-Allow-Origin:*");
        $postData = \Yii::$app->request->post();
        $result = (new CompanyAuthServer())->cancelCompanyAuth($postData['company_id'],$postData['group_id'],$postData['manage_id']);

        return $this->success($result);
    }

    /**
     * Notes: 移除本群处理群主权限
     * Author: lcy
     * Date: 2020/6/16 10:35
     * @return string
     */
    public function actionRemoveCompanyAuth() {
        header("Access-Control-Allow-Origin:*");
        $postData = \Yii::$app->request->post();
        $result = (new CompanyAuthServer())->removeCompanyAuth($postData['company_id'],$postData['group_id'],$postData['manage_id']);

        return $this->success($result);
    }


    /**
     * Notes: 化合物发送请求到此更新物料表产物的编号
     * Author: hkk
     * Date: 2021/7/22 17:21
     * @return array/
     */
    public function actionSetProductCmsCode() {
      //  header("Access-Control-Allow-Origin:*");
        $postData = \Yii::$app->request->post();

        // 根据id 查询对应产物表

        $result = (new ExperimentServer())->setProductCmsCode($postData['cms_code'],$postData['cms_id'],$postData['eln_id'],$postData['batch_no']);
        return $this->success($result);
    }

    /**
     * Notes: 保存intable产生的链接到实验
     * Author: jiangdm
     * Date: 2022/6/20
     */
    public function actionSaveExpLink() {
        header("Access-Control-Allow-Origin:*");
        $postData =  Yii::$app->request->getRawBody();
        $postDataArr = json_decode($postData,true);
        (new ExperimentServer())->saveLink($postDataArr['links']);
        return $this->success([]);
    }

    /**
     * Notes: 保存同步过来的链接信息
     * Author: jiangdm
     * Date: 2022/6/10
     */
    public function actionReceiveLink() {
        $links = \Yii::$app->request->post('links');
        (new ExperimentServer())->saveLink($links);
        return $this->success([]);
    }
    /**
     * Notes: 根据实验Id获取实验名称
     * Author: jiangdm
     */
    public function actionGetExpNameByIds() {
        $expIds = \Yii::$app->request->post('exp_ids');
        $expCodes =( new ExperimentServer())->getExpPageByIds($expIds);
        return $this->success($expCodes);
    }

    /**
     * @note 项目管理获取项目实验统计数据
     * <AUTHOR>
     * @date : 2022年1月12日15点32分
     * @return array
     */
    public function actionGetProjectExpStatisticList() {
        $params = \Yii::$app->request->post();
        $userIdList = \Yii::$app->request->post('user_id_list');
        $userIdList = !is_array($userIdList)?explode(',',$userIdList):  $userIdList;
        $projectId = \Yii::$app->request->post('project_id');
        $taskId = \Yii::$app->request->post('task_id');
        $expList = [];
        foreach ($userIdList as $userId){
             $userExpList = ExperimentModel::find()
                ->select('step, result')
                ->from(ExperimentModel::tableName())
                ->where([
                    'user_id' => $userId,
                    'project_id' => $projectId
                ])
                 ->andFilterWhere(['task_id' => $taskId])
                 ->asArray()->all();
             $expList[$userId] = $userExpList;
        }
        return $this->success($expList);
    }

    /**
     * @note 返回加密Excel原始数据
     * <AUTHOR>
     * @date : 2022/8/18
     */
    public function actionReadExcel() {
        header("Access-Control-Allow-Origin:*");
        header("Content-Type: application/octect-stream");
        $data = \Yii::$app->request->post();
        if(empty($data)){
            $data = \Yii::$app->request->get();
        }
        if (isset($data['type']) && $data['type'] == 1) {
            $filePath = (new Encryption())->decryptFileApi($_FILES['file']['tmp_name']);
        } else {
            $filePath = \Yii::getAlias('@filepath') . DS . $data['file_path'] . DS . $data['file_name'];
        }
        if (file_exists($filePath)) {
            $file = file_get_contents($filePath);
            echo $file;
        }
        die;
    }

    /**
     * @note 弹窗预览Excel文件
     * <AUTHOR>
     * @date : 2022/8/18
     */
    public function actionExcelPreview() {
        $fileInfo = Yii::$app->getRequest()->post();
        // 预览的文件需要是数据库中保存最新版本的路径 jiangdm 2023/5/24
        if(isset($fileInfo['file_id'])){
            $fileNewInfo = (new UploadModel())->find()->where(['id' => $fileInfo['file_id']])->asArray()->one();
            $fileInfo['save_name'] = $fileNewInfo['save_name'];
        }
        if (isset($fileInfo['view_type']) && $fileInfo['view_type'] == 'exp') {
            $logInfo = [
                'expId' => $fileInfo['exp_id'],
                'dl_usr_info' => (Object)$fileInfo['user_info'],
                'file_name' => $fileInfo['name'],
                'expType' => $fileInfo['exp_type'],
            ];
            $logResult = (new \frontend\services\LogServer())->logsToTables($logInfo, 'check');
        }
        $file = $this->renderAjax('/popup/preview_file', ['file_info' => $fileInfo]);
        return $file;
    }

    /**
     * @note 预览文件失败提醒是否下载
     * <AUTHOR>
     * @date : 2023/4/3
     */
    public function actionPreviewFileFailed() {
        $fileInfo = Yii::$app->getRequest()->post();

        $file = $this->renderAjax('/popup/preview_file_failed', ['file_info' => $fileInfo]);
        return $file;
    }

    /**
     * Notes: 根据实验编号获取实验本的标题和关键字
     *
     * ?r=eln-interface/get-exp-base-data-by-exp-num
     *
     * Author: xie yuxiang
     * Date : 2022/10/11 15:19
     */
    public function actionGetExpBaseDataByExpNum(){
        $expNum = \Yii::$app->request->post('exp_num');
        $expIdResult = (new ExperimentServer())->getExpIdByExpNum($expNum);
        if($expIdResult['status']!=1){
            return $this->fail($expIdResult['info']);
        }
        $expId=$expIdResult['data'];
        $result = (new ExperimentServer())->getExpBaseData($expId);
        return $this->success($result);
    }

    /**
     * Notes: indraw同步文件连接检查
     * jiangdm 2022/10/18
     * @throws Exception
     */
    public function actionInstrumentFileConnectionCheck() {
        $type = \Yii::$app->request->post('type');

        $elnVersion = \Yii::$app->params['app_version'];
        // eln当前版本是否低于5.3.8
        $elnVersionLower_5_3_8 = ElnVersionHelper::compareVersion($elnVersion, '5.3.8') < 0;

        //<editor-fold desc="兼容没有type参数的老版客户端 和 老版inDraw内置scada的情况(康弘eln5.3.3)">
        if (!isset($type) || $type === 'v1') {
            // eln版本低于5.3.8的, 仪器文件夹地址配置从plug获取
            if ($elnVersionLower_5_3_8) {
                // 校验文件服务器地址
                $url = CENTER_URL . '/api/inst-file-config';
                $config = (new CommonCurl())->sendPostCurl($url);
                $uploadPath = isset($config['file_path']) ? $config['file_path'] : '';
                if ($uploadPath != $_POST['upload_path']) {
                    return $this->success(['check_status' => 0, 'type' => 1]);
                }
                $insBatchNum = $_POST['ins_batch_num'];
                $insBatchList = InstrumentsModel::find()->select('batch_number')->asArray()->all();
                $insBatchList = array_column($insBatchList, 'batch_number');
                $insBatchList = array_values(array_unique(array_filter($insBatchList)));
                if (!in_array($insBatchNum, $insBatchList)) {
                    return $this->success(['check_status' => 0, 'type' => 2]);
                }
                return $this->success(['check_status' => 1, 'config' => $config]);
            }
            // eln版本>=5.3.8, 仪器文件夹地址配置从eln获取
            $elnBindingFileServer = InstrumentBindingFileServerModel::findOne(['status' => 1]);
            if (null == $elnBindingFileServer) {
                return $this->success(['check_status' => 0, 'type' => 1, 'message' => '没有对应的仪器配置']);
            }
            // 同下方校验文件服务器地址, 文件服务器未初始化完成时scada不能上传文件
            if ($elnBindingFileServer->init_done != 1) {
                return $this->success(['check_status' => 0, 'type' => 3, 'message' => '正在初始化文件索引，请在初始化完成后重试']);
            }
            // 将eln5.3.8的文件服务器配置转化为老版本scada客户端需要的格式
            $oldScadaBindingConfig = [
                'file_path' => $elnBindingFileServer->server_path,
                'file_path_username' => $elnBindingFileServer->username,
                'file_path_password' => $elnBindingFileServer->password,
            ];
            return $this->success(['check_status' => 1, 'config' => $oldScadaBindingConfig]);
        }
        //</editor-fold>

        // 校验文件服务器地址
        $bindingInfo = InstrumentBindingFileServerModel::find()->where(['status' => 1])->asArray()->one();
        if ($bindingInfo['init_done'] != 1) {
            return $this->success(['check_status' => 0, 'type' => 3, 'message' => '正在初始化文件索引，请在初始化完成后重试']);
        }
        if ($type === 'v1') {
            // 兼容旧版inscada
            $uploadPath = isset($config['file_path']) ? $config['file_path'] : '';
            if ($uploadPath != $_POST['upload_path']) {
                return $this->success(['check_status' => 0, 'type' => 1]);
            }
        }
        if ($type === 'id_check') {
            $insBatchNum = $_POST['ins_batch_num'];
            $insBatchList = InstrumentsModel::find()->select('batch_number')->asArray()->all();
            $insBatchList = array_column($insBatchList, 'batch_number');
            $insBatchList = array_values(array_unique(array_filter($insBatchList)));
            if (!in_array($insBatchNum, $insBatchList)) {
                return $this->success(['check_status' => 0, 'type' => 2, 'message' => \Yii::t('base', 'instrument_id_empty_tip')]);
            }
        }
        return $this->success(['check_status' => 1, 'config' => $bindingInfo]);
    }

    /**
     * Notes: indraw同步文件保存
     * jiangdm 2022/10/18
     */
    public function actionSyncInstrumentFile() {
        $post = $_POST;
        $postSaveFolder = \Yii::$app->request->post('save_folder');
        $instrumentId = \Yii::$app->request->post('ins_batch_num');
        /// 发送的save_folder=integle_transfer, 认为是老版scada, 拼接上仪器id路径
        if ($postSaveFolder === 'integle_transfer') {
            $postSaveFolder .= DS;
            $postSaveFolder .= $instrumentId;
        }

        $insFileModel = new InstrumentDataFileModel();
        $insFileModel->setAttributes([
            'instrument_id' => '',
            'batch_number' => $post['ins_batch_num'],
            'filename' => $post['file_name'],
            'save_name' => $post['save_name'],
            'file_host' => $post['server_path'],
            'filepath' => $postSaveFolder,
            'exp_pages' => '',
            'remark' => '',
            'status' => 1,
            'operate_users' => '',
        ]);
        if(!$insFileModel->save()){
            return $this->fail($insFileModel->getFirstErrors());
        }
        return $this->success(\Yii::t('base', 'upload_success'));
    }

    public function actionCheckFileServerConfig(){
        $bindingInfo = [
            'server_path' => $_POST['server_path'],
            'username' => $_POST['username'],
            'password' => $_POST['password']
        ];
        $mountResult = (new CenterInterface())->mountFileFolder(null, $bindingInfo);

        if ($mountResult['code'] !== 0) {
            return $this->fail($mountResult['code'] );
        }

        return $this->success([]);
    }

    public function actionSaveFileServerConfig() {
        $serverPath = $_POST['server_path'];
        $userName = $_POST['username'];
        $password = $_POST['password'];
        $bindingModel = new InstrumentBindingFileServerModel();
        $bindingModel->updateAll([ 'status' => 0 ], ['status' => 1]);
        $binding = InstrumentBindingFileServerModel::findOne(['server_path' => $serverPath]);
        if ($binding === null) {
            $bindingModel->setAttributes([
                'server_path' => $serverPath,
                'username' => $userName,
                'password' => $password,
                'init_done' => 0,
                'status' => 1
            ]);
            $bindingModel->save();
            $bindingId = $bindingModel->id;
        } else {
            $binding->username = $userName;
            $binding->password = $password;
            $binding->status = 1;
            $binding->save();
            $bindingId = $binding->id;
        }
        CommonServer::startBackendTask('yii instrument-file-scan/init-server-data ' . $bindingId);
        return $this->success([]);
    }

    public function actionGetFileServerConfig() {
        $currentBinding = InstrumentBindingFileServerModel::find()->where(['status' => 1])->asArray()->one();
        return $this->success($currentBinding);
    }

    /**
     * Notes: indraw获取仪器信息
     * jiangdm 2022/10/18
     */
    public function actionGetInstrumentInfo() {
        $insBatchNum = $_POST['batch_number'];
        if ($insBatchNum == '') return $this->fail(\Yii::t('base', 'instrument_number_empty_error'));
        $result = InstrumentsModel::find()->where(['batch_number' => $insBatchNum])->asArray()->one();
        if (empty($result)) {
            return $this->fail(\Yii::t('base', 'instrument_number_not_exist'));
        }
        return $this->success(['instrument_info' => $result]);
    }

    /**
     * Notes: indraw检查仪器ID合法性，保存配对信息
     * jiangdm 2022/10/18
     */
    public function actionInstrumentDataConnectionCheck() {
        $postType = \Yii::$app->request->post('type');
        $post = $_POST;
        $insBatchNum = $post['batch_number'];
        if ($insBatchNum == '') return $this->success(['check_status' => 0, 'type' => 1]);
        $insBatchList = InstrumentsModel::find()->where(['batch_number' => $insBatchNum])->asArray()->one();
        if (empty($insBatchList)) return $this->success(['check_status' => 0, 'type' => 1]);
        // 取消检验联通性时服务端向客户端发送请求, 避免sass环境无法运行. 2024/10/18
        //// 校验数据服务器是否连通
        //$instrumentSite = $post['ip'] . ':' . $post['port'];
        //if (!isset($postType) || $postType === 'v1') {
        //    $connectionResult = (new CommonCurl())->sendPostCurl($instrumentSite, [
        //        'type' => 'test',
        //    ]);
        //    if (!$connectionResult) return $this->success(['check_status' => 0, 'type' => 2]);
        //} else {
        //    try {
        //        $socket = stream_socket_client("tcp://" . $instrumentSite, $errno, $errstr, 2);
        //    } catch (\Exception $e) {
        //        return $this->success(['check_status' => 0, 'type' => 2]);
        //    }
        //    $socket && fclose($socket);
        //}
        // 检查请求的仪器ID是否有已经绑定的实体设备
        $bindingModel = new InstrumentBindingModel();
        // 检查仪器ID是否已经有IP和端口绑定的记录
        $virtualBinding = $bindingModel::find()->where(['batch_number' => $insBatchNum, 'status' => 1])->asArray()->one();
        if (!empty($virtualBinding)) {
            if ($virtualBinding['ip'] == $post['ip'] && $virtualBinding['port'] == $post['port']) {
                // 如果有记录且与本次连接的相同，直接读取当前的binding_id
                $bindingId = $virtualBinding['id'];
            } else {
                // 否则之前的记录均标记为无效，仅供数据追溯用
                $bindingModel->updateAll([
                    'status' => 0,
                ],[
                    'batch_number' => $insBatchNum,
                ]);
            }
        }
        // 没有有效的绑定记录，新建一条绑定信息，并记录ID
        if (!isset($bindingId)) {
            $post['status'] = 1;
            $bindingModel->setAttributes($post);
            $bindingModel->save();
            $bindingId = $bindingModel->id;
        }
        return $this->success(['check_status' => 1, 'binding_id' => $bindingId]);
    }

    /**
     * Notes: 保存仪器读数指令
     * jiangdm 2022/11/18
     */
    public function actionSaveInstrumentCommand() {
        $post = $_POST;
        $bindingModel = new InstrumentBindingModel();
        $bindingModel->updateAll(['read_command' => $post['read_command']], ['id' => $post['binding_id']]);
        return $this->success([]);
    }

    /**
     * Notes: indraw同步数据保存
     * jiangdm 2022/10/18
     */
    public function actionSyncInstrumentData() {
        $insData = array_merge($_POST, [
            'instrument_id' => '',
            'exp_pages' => '',
            'remark' => '',
            'status' => 1,
            'operate_users' => '',
        ]);
        $insDataModel = new InstrumentDataNumericalModel();
        $insDataModel->setAttributes($insData);
        if (!$insDataModel->save()) {
            Yii::info($insDataModel->getFirstErrors());
        }
        return $this->success([]);
    }

    public function actionSaveInstrumentConnectionError() {
        $logModel = new InstrumentDataLogModel();
        $logModel->setAttributes($_POST);
        if (!$logModel->save()) {
            Yii::info($logModel->getFirstErrors());
        }
        return $this->success([]);
    }

    // 用户本地下载文件 jiangdm 2023/5/24
    public function actionGetExpUploadFile($file_id, $mode, $user_id) {
        \Yii::info('用户本地下载文件->actionGetExpUploadFile: ' . json_encode([
                'file_id' => $file_id,
                'user_id' => $user_id,
                'mode' => $mode,
            ]), 'BugInvestigation');

        $userInfo = (new CenterInterface())->userDetailsByUserIds($user_id);
        $downloadStatus = (new Download)->downloadFileById($file_id, (object)$userInfo[0]);
        if ($downloadStatus === TRUE && $mode == 'edit') {
            (new Upload())->lockFile($file_id, $user_id);
        }
    }

    // 查看当前用户是否有权限本地编辑指定文件 Jiangdm 2023/5/24
    public function actionCheckFileStatus($file_id, $user_id) {
        // $f = '/home/<USER>/logs/a.txt';
        // file_put_contents($f, 'actionCheckFileStatus' . PHP_EOL, FILE_APPEND);
        $fileStatus = (new Upload())->getFileStatus($file_id, $user_id);
        // file_put_contents($f, 'actionCheckFileStatus res' . $fileStatus, FILE_APPEND);
        return $this->success(['status' => $fileStatus]);
    }

    // 本地编辑保存后，更新ELN中的文件内容 jiangdm 2023/5/24
    public function actionUpdateFileContent($file_id, $user_id, $module_id, $language, $file_type = null) {
        \Yii::info('本地编辑文件->actionUpdateFileContent: ' . json_encode([
                'file_id' => $file_id,
                'user_id' => $user_id,
                'module_id' => $module_id,
                'file_type' => $file_type
            ]), 'BugInvestigation');
        Yii::$app->language = $language === 'en' ? 'en-US' : 'zh-CN';
        // 校验实验和模块的编辑权限
        \Yii::info('本地编辑文件->校验实验和模块的编辑权限', 'BugInvestigation');
        if (!(new ExperimentServer())->getModuleEditAuth($module_id, $user_id) && $file_type != 'intablePrismImage' && $file_type != 'intablePrismFile') return;
        // 校验文件的编辑权限
        \Yii::info('本地编辑文件->校验文件的编辑权限', 'BugInvestigation');
        if (!(new Upload())->getFileStatus($file_id, $user_id) && $file_type != 'intablePrismImage' && $file_type != 'intablePrismFile') return;

        if ($file_type != null && $file_type == 'intablePrismImage') {
            // 保存文件,覆盖源文件，prism图片更新推送
            $file_options = ['type' => 'prism', 'baseFilePath' => 'picpath', 'moduleId' => $module_id];
            (new Upload())->saveUpdatedCoverFile($file_id, $_FILES['file'], $user_id, $file_options);
        } else {
            // 保存文件
            \Yii::info('本地编辑文件->saveUpdatedFile', 'BugInvestigation');
            (new Upload())->saveUpdatedFile($file_id, $_FILES['file'], $user_id);
        }
    }

    // 本地文件关闭后，释放文件锁，允许其他人编辑 jiangdm 2023/5/24
    public function actionUnlockFile($file_id, $user_id) {
        (new Upload())->unlockFile($file_id, $user_id);
    }

    /**
     * Note: 获取指定用户名下的实验数据
     * @return array
     * <AUTHOR>
     * @date 2023/6/13 20:12
     */
    public function actionListExpByUser() {
        $username = \Yii::$app->request->get('username');
        if (empty($username)) {
            return $this->fail('缺少参数username');
        }

        $userId = (new CenterInterface())->getUserIdByUsername($username);
        if ($userId == 0) {
            return $this->fail('无效的username');
        }

        $expList = ExperimentModel::find()->select('id')->where([
            'user_id' => $userId
        ])->asArray()->all();
        $expIds = array_column($expList, 'id');

        $data = (new ExperimentServer())->listExpBaseDataByIds($expIds);

        // 获取项目信息
        $projectIds = array_column($data, 'project_id');
        $projects = [];
        if (!empty($projectIds)) {
            $projectTaskRes = (new PMInterface())->getProjectsAndTasksByIds($projectIds);
            $projects = @getVar($projectTaskRes['projects'], []);
            $projects = yii\helpers\ArrayHelper::index($projects, 'id');
        }

        $returnData = [];
        foreach ($data as $item) {
            $pInfo = @getVar($projects[$item['project_id']], []);

            $returnData[] = [
                'exp_all_code' => $item['exp_all_code'],
                'exp_link' => ELN_URL . '?exp_id=' . $item['id'],
                'book_code' => $item['book_code'],
                'book_name' => $item['book_name'],
                'title' => $item['title'],
                'keywords' => $item['keywords'],
                'define_item' => $item['define_item'],
                'creator' => $item['user'],
                'creator_email' => $item['user_email'],
                'create_time' => $item['create_time'],
                'step' => $item['step'],
                'submit_time' => $item['submit_time'],
                'sign_time' => $item['sign_time'],
                'result' => !empty($item['result']) ? $item['result'] : 0,
                'reopen_status' => !empty($item['reopen_status']) ? $item['reopen_status'] : 0,
                'template_name' => $item['template_name'],
                'project' => [
                    'id' => !empty($item['project_id']) ? $item['project_id'] : '',
                    'name' => @getVar($pInfo['name']),
                    'code' => @getVar($pInfo['code'])
                ]
            ];
        }

        return $this->success($returnData);
    }

    /**
     * Note: 根据实验编号获取实验数据
     * @return void
     * <AUTHOR>
     * @date 2022/12/7 17:38
     */
    public function actionGetExpDataByCode() {
        $expCode = \Yii::$app->request->get('exp_code');
        if (empty($expCode)) {
            return $this->fail('缺少参数exp_code');
        }

        $expServer = new ExperimentServer();
        $expResult = $expServer->getExpIdByExpNum($expCode);
        if (empty($expResult['data'])) {
            return $this->fail('无效的实验页码');
        } else {
            $expId = $expResult['data'];
        }

        // 基础数据
        $baseDataArr = $expServer->listExpBaseDataByIds([$expId]);
        if (empty($baseDataArr)) {
            return $this->fail('无效的实验页码');
        }

        $baseData = $baseDataArr[0];

        // 获取项目信息
        $projectInfo = [];
        if (!empty($baseData['project_id'])) {
            $projectRes = (new PMInterface())->getProjectById($baseData['project_id']);
            $projectInfo = @getVar($projectRes['project'], []);
        }

        $returnBaseData = [
            'exp_all_code' => $baseData['exp_all_code'],
            'exp_link' => ELN_URL . '?exp_id=' . $expId,
            'book_code' => $baseData['book_code'],
            'book_name' => $baseData['book_name'],
            'title' => $baseData['title'],
            'keywords' => $baseData['keywords'],
            'define_item' => $baseData['define_item'],
            'creator' => $baseData['user'],
            'creator_email' => $baseData['user_email'],
            'create_time' => $baseData['create_time'],
            'step' => $baseData['step'],
            'submit_time' => $baseData['submit_time'],
            'sign_time' => $baseData['sign_time'],
            'result' => !empty($baseData['result']) ? $baseData['result'] : 0,
            'reopen_status' => !empty($baseData['reopen_status']) ? $baseData['reopen_status'] : 0,
            'template_name' => $baseData['template_name'],
            'project' => [
                'id' => !empty($baseData['project_id']) ? $baseData['project_id'] : '',
                'name' => @getVar($projectInfo['name']),
                'code' => @getVar($projectInfo['code'])
            ]
        ];

        // indraw模块
        $indrawData = $expServer->getIndrawData($expId);

        // 构造RXN,规则是: Reactant_SMILES>Reagent_SMILES>Product_SMILES
        $reactant_smiles = '';
        if (!empty($indrawData['reactants'])) {
            $reactant_smiles = implode('.', array_column($indrawData['reactants'], 'smiles'));
        }
        $reagent_smiles = '';
        if (!empty($indrawData['reagents'])) {
            $reagent_smiles = implode('.', array_column($indrawData['reagents'], 'smiles'));
        }
        $product_smiles = '';
        if (!empty($indrawData['products'])) {
            $product_smiles = implode('.', array_column($indrawData['products'], 'smiles'));
        }
        $indrawData['rxn'] = implode('>', [$reactant_smiles, $reagent_smiles, $product_smiles]);

        // 上传文件模块
        $fileData = $expServer->getFileData($expId);
        $returnFileData = [];
        foreach ($fileData as $file) {
            $downloadUrl = ELN_URL . '?r=eln-interface/file&type=2&path=' . $file['dep_path']
                . '&name=' . $file['save_name']
                . '&id=' . $file['id'] . '&file_name='
                . urlencode($file['file_name']) . '&reload=1' . '&exp_id=' . $expId;
            $returnFileData[] = [
                'name' => $file['file_name'],
                'url' => $downloadUrl,
            ];
        }

        // 上传图片模块
        $imgData = $expServer->getImgData($expId);
        $returnImgData = [];
        foreach ($imgData as $img) {
            $downloadUrl = ELN_URL . '?r=download/file&type=1&path=' . $img['dep_path']
                . '&name=' . $img['save_name'] . '&file_name=' . urlencode($img['file_name']) . '&exp_id=' . $expId;
            $returnImgData[] = [
                'name' => $img['file_name'],
                'url' => $downloadUrl,
            ];
        }

        // 文本编辑器模块
        $editorData = $expServer->getEditorData($expId);

        return $this->success([
            'base_data' => $returnBaseData,
            'indraw' => $indrawData,
            'file' => $returnFileData,
            'img' => $returnImgData,
            'editor' => $editorData,
        ]);
    }

    /**
     * Note: 更新绩效统计的登陆次数
     * @return void
     * <AUTHOR>
     * @date 2022/12/7 17:38
     */
    public function actionUpdateStaticLogin() {
        $userId = \Yii::$app->request->post('user_id');
        $staticDay =  \Yii::$app->request->post('static_day',date('Y-m-d'));
        $loginTime =  \Yii::$app->request->post('login_time');

        $useStaticLogin = new UseStaticLogin();
        $useStaticLoginInfo = $useStaticLogin->find()->where(['user_id'=>$userId,'static_day'=>$staticDay])->asArray()->one();

        //存在则修改 不存在则添加
        if(!empty($useStaticLoginInfo)){
            $useStaticLogin::updateAllCounters( ['exp_login_num' => 1], ['id' => $useStaticLoginInfo['id']] );
        }
        else
        {
            $useStaticLogin = new UseStaticLogin();
            $useStaticLogin->user_id = $userId;
            $useStaticLogin->static_day =$staticDay;
            $useStaticLogin->exp_login_num = 1;
            $useStaticLogin->save();
        }
        //更新最后登陆时间
        $useStatic = new UseStatic();
        $useStatic->updateAll(['last_login_time'=>$loginTime],['user_id'=>$userId]);


        return $this->success([]);
    }

    /*
     *
     * 新增更新用户绩效信息接口(如果用户信息变更时调用)
     *
     *
     * */
    public function actionFixUserData(){
        $userIds = \Yii::$app->request->post('user_ids');

        $userInfoArr = (new CenterInterface())->userDetailsByUserIds($userIds);
        $userInfoArr = ArrayHelper::index($userInfoArr, 'id');
        foreach ($userInfoArr as $userId => $userInfo) {
            $useStatic = new UseStatic();
            $useStaticInfo = $useStatic->find()->where(['user_id'=>$userId])->asArray()->one();
            if(empty($useStaticInfo)){
                $useStatic->user_id=$userId;
                $useStatic->user_name=strlen(trim($userInfo['nick_name']))>0?$userInfo['nick_name']:$userInfo['real_name'];
                $useStatic->last_login_time = $userInfo['login_time'];
                $useStatic->insert();

            }
            else{
                $useStatic->updateAll(['user_name'=>strlen(trim($userInfo['nick_name']))>0?$userInfo['nick_name']:$userInfo['real_name'],
                    'last_login_time'=>$userInfo['login_time']],['id'=>$useStaticInfo['id']]);
            }

            $group_list = (new CenterInterface())->elnGroupListByUserId($userId);
            if($group_list){
                $group_ids_arr=[];
                foreach($group_list as $group){
                    $group_ids_arr[] = $group['group_id'];
                }
                $group_ids = join(',',$group_ids_arr);
                $group_num = count($group_list);

                $useStatic->updateAll(['group_ids'=>$group_ids,'group_num'=>$group_num],['id'=>$useStaticInfo['id']]);

            }


            $user_info = (new CenterInterface())->getUserByUserId($userId);
            if($user_info['department_info']){
                $dep_ids_arr = array_column($user_info['department_info'],'department_id', 'department_id'); //去重
                $dep_ids = implode(',',$dep_ids_arr);

                $dep_names_arr = array_column($user_info['department_info'],'department_name', 'department_id'); //去重
                $dep_names = implode(',',$dep_names_arr);

                $useStatic->updateAll(['dep_ids'=>$dep_ids,'dep_names'=>$dep_names],['id'=>$useStaticInfo['id']]);

            }
            else {

                $useStatic->updateAll(['dep_ids'=>'','dep_names'=>''],['id'=>$useStaticInfo['id']]);

            }

            if($user_info['role_info']){
                $role_ids_arr = array_column($user_info['role_info'],'role_id' );
                $role_ids = implode(',',$role_ids_arr);

                $role_names_arr = array_column($user_info['role_info'],'role_name' );
                $role_names = implode(',',$role_names_arr);
                $useStatic->updateAll(['role_ids'=>$role_ids,'role_names'=>$role_names],['id'=>$useStaticInfo['id']]);

            }

            $user_LoginInfo = (new CenterInterface())->getUserLoginById($userId);
            if($user_LoginInfo){
                $useStatic->updateAll(['last_login_time'=>$user_LoginInfo['login_time']],['id'=>$useStaticInfo['id']]);
            }
        }

    }

    /**
     * 获取项目下实验
     * <AUTHOR>
     * @date 220214
     */
    public function actionListExpByProjectOrTask() {
        $projectId = \Yii::$app->request->get('project_id');
        $taskId = \Yii::$app->request->get('task_id');
        if (empty($projectId) && empty($taskId)) {
            return $this->fail('缺少参数project_id 或 task_id');
        }

        try {
            $expList = ExperimentModel::find()->select('id')->filterWhere([
                'project_id' => $projectId
            ])->andFilterWhere(['task_id' => $taskId])->asArray()->all();
            $expIds = array_column($expList, 'id');

            $data = (new ExperimentServer())->listExpBaseDataByIds($expIds);
            $returnData = [];
            $taskIds = array_column($data, 'task_id');
            $taskIds = array_filter($taskIds, function ($a) {
                return !!$a;
            });
            $taskIds = array_unique($taskIds);
            $projectIds = array_column($data, 'project_id');
            $projectIds = array_filter($projectIds, function ($a) {
                return !!$a;
            });
            $projectIds = array_unique($projectIds);
            $projectsAndTasks = (new PMInterface())->getProjectsAndTasksByIds($projectIds, $taskIds);
            $projects = \yii\helpers\ArrayHelper::index($projectsAndTasks['projects'], 'id');
            $tasks = \yii\helpers\ArrayHelper::index($projectsAndTasks['tasks'], 'id');
            foreach ($data as $item) {
                $returnData[] = [
                    'exp_all_code' => $item['exp_all_code'],
                    'exp_link' => ELN_URL . '?exp_id=' . $item['id'],
                    'book_code' => $item['book_code'],
                    'book_name' => $item['book_name'],
                    'title' => $item['title'],
                    'keywords' => $item['keywords'],
//                    'define_item' => $item['define_item'],
                    'creator' => $item['user'],
                    'create_time' => $item['create_time'],
                    'sign_time' => $item['sign_time'],
                    'step' => $item['step'],
                    'result' => intval($item['result']),
                    'pretrial_status' => intval($item['pretrial_status']),
                    'reopen_status' => intval($item['reopen_status']),
                    'project' => $item['project_id'] ? $projects[$item['project_id']]  : NULL,
                    'task' => $item['task_id'] ? $tasks[$item['task_id']]  : NULL,
                ];
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        return $this->success($returnData, Yii::t('base', 'success_number'));
    }

    /**
     * 离职继承
     * <AUTHOR>
     * @return array
     * @date 20230510
     */
    public function actionInherit() {
        $userId = \Yii::$app->request->post('user_id');
        $inheritorId = \Yii::$app->request->post('inheritor_id');
        $inheritCompanyRole = \Yii::$app->request->post('company_role');
        $inheritGroupRole = \Yii::$app->request->post('group_role');
        $groupIds = \Yii::$app->request->post('group_ids');
        $groupIds = $groupIds ? explode(',', $groupIds) : [];

        if ($inheritCompanyRole) {
            // 企业角色
            $userCompanyRole = UserCompanyRole::findOne(['user_id' => $userId]);
            if ($userCompanyRole) {
                $userRoleIds = explode(',', @getVar($userCompanyRole['role_id']));
                $userRoleNames = explode(',', @getVar($userCompanyRole['role_name']));
                $inheritorGroupRole = UserCompanyRole::findOne(['user_id' => $inheritorId]);
                if (!$inheritorGroupRole) {
                    $inheritorGroupRole = new UserCompanyRole();
                    $inheritorGroupRole->setAttributes(['user_id' => $inheritorId, 'company_id' => 1,]);
                }
                $inheritorRoleIds = explode(',', @getVar($userCompanyRole['role_id']));
                $inheritorRoleNames = explode(',', @getVar($userCompanyRole['role_name']));
                for ($inheritorValue = 0; $inheritorValue < sizeof($userRoleIds); $inheritorValue++) {
                    if (!in_array($userRoleIds[$inheritorValue], $inheritorRoleIds)) {
                        $inheritorRoleIds [] = $userRoleIds[$inheritorValue];
                        $inheritorRoleNames [] = $userRoleNames[$inheritorValue];
                    }
                }
                $inheritorGroupRole->setAttributes([
                    'role_id' => join(',', $inheritorRoleIds),
                    'role_name' => join(',', $inheritorRoleNames),
                ]);
                $inheritorGroupRole->save();
            }
            // 直接分配企业权限
            $userCompanyAuth = CompanyAuthSetting::findOne(['user_id' => $userId]);
            if ($userCompanyAuth){
                $inheritorCompanyAuth = CompanyAuthSetting::findOne(['user_id' => $inheritorId]);
                if (!$inheritorCompanyAuth) {
                    $inheritorCompanyAuth = new CompanyAuthSetting();
                    $inheritorCompanyAuth->setAttribute('user_id', $inheritorId);
                    $inheritorCompanyAuth->setAttributes($userCompanyAuth->getAttributes(['company_id', 'company_auth', 'company_log', 'company_feature']));
                } else {
                    foreach (['company_auth', 'company_log', 'company_feature'] as $fieldName) {
                        $userValue = json_decode($userCompanyAuth[$fieldName], true);
                        $inheritorValue = json_decode($inheritorCompanyAuth[$fieldName], true);

                        if (is_array($userValue)) {
                            foreach ($userValue as $key => $value) {
                                $inheritorValue[$key] = $value || $inheritorValue[$key] ? '1' : '0'; // 取并集
                            }
                        }
                        $inheritorCompanyAuth->setAttribute($fieldName, json_encode($inheritorValue));
                    }
                }
                $inheritorCompanyAuth->save();
            }
        }

        if ($inheritGroupRole && !empty($groupIds)) {
            // 群内角色
            $userGroupRoles = @getVar(UserGroupRole::findAll(['user_id' => $userId, 'group_id' => $groupIds]), []);
            foreach ($userGroupRoles as $userGroupRole) {
                $userRoleIds = explode(',', @getVar($userGroupRole['role_id']));
                $userRoleNames = explode(',', @getVar($userGroupRole['role_name']));

                $inheritorGroupRole = UserGroupRole::findOne(['user_id' => $inheritorId, 'group_id' => $userGroupRole['group_id']]);
                if (!$inheritorGroupRole) {
                    $inheritorGroupRole = new UserGroupRole();
                    $inheritorGroupRole->setAttributes([
                        'company_id' => 1,
                        'user_id' => $inheritorId,
                        'group_id' => $userGroupRole['group_id'],
                    ]);
                }
                $inheritorRoleIds = empty($inheritorGroupRole['role_id']) ? [] : explode(',', $inheritorGroupRole['role_id']);
                $inheritorRoleNames = empty($inheritorGroupRole['role_name']) ? [] : explode(',', $inheritorGroupRole['role_name']);

                for ($inheritorValue = 0; $inheritorValue < sizeof($userRoleIds); $inheritorValue++) {
                    if (!in_array($userRoleIds[$inheritorValue], $inheritorRoleIds)) {
                        $inheritorRoleIds [] = $userRoleIds[$inheritorValue];
                        $inheritorRoleNames [] = $userRoleNames[$inheritorValue];
                    }
                }
                $inheritorGroupRole->setAttributes([
                    'role_id' => join(',', $inheritorRoleIds),
                    'role_name' => join(',', $inheritorRoleNames),
                ]);
                $inheritorGroupRole->save();
            }
        }
        return $this->success([]);
    }

    /**
     * @Notes: 文件下载接口
     * @Author: ysj
     * @Date: 2023/10/10
     */
    public function actionFile() {
        $type = \Yii::$app->request->get('type', 2);
        $path = \Yii::$app->request->get('path', '');
        $name = \Yii::$app->request->get('name', '');
        $id = \Yii::$app->request->get('id', '');
        $file_name = \Yii::$app->request->get('file_name', '');
        $reload = \Yii::$app->request->get('reload', false);
        $exp_id = \Yii::$app->request->get('exp_id', '');
        if (!$path || !$name || !$id || !$file_name || !$exp_id) {
            return $this->fail('缺少必要参数');
        }
        //对$file_name里的回车进行去除
        $pattern='/\n|\r/';
        $file_name=preg_replace($pattern,'',$file_name);

        if(1 == $type){
            $filePath = \Yii::getAlias('@picpath') . DS . $path . DS . $name;
        }else{
            $filePath = \Yii::getAlias('@filepath') . DS . $path . DS . $name;
        }

        $fileSize = null;
        if(!empty($id)){
            $uploadModel = new UploadModel();
            $fileInfo = $uploadModel->find()->where(['id'=>$id])->asArray()->one();
            $fileSize = $fileInfo['size'];
        }

        if (file_exists($filePath)) {
            return (new Download)->download($filePath, !empty($file_name) ? $file_name : $name, $reload, $fileSize);
        } else {
            return $this->fail(\Yii::t('file', 'no_file'));
        }
    }

    /**
     * @Notes: sequence图片预览
     * @Author:
     * @Date: 2024/01/30*/
    public function actionGetSequenceImage(){
        header("Access-Control-Allow-Origin:*");
        $id = \Yii::$app->request->get('id', '');

        // 查询图片base64
        $result = (new InsequenceServer())->getSequenceImage($id);

        if(empty($result['data'])){ // 示例的src
            $result['data'] = 'data:image/svg+xml;base64,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';
        }


        // 设置响应头，告诉浏览器返回的是图片数据
        Yii::$app->response->format = yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'image/svg+xml');

        // 直接输出图片数据
        $imageData = base64_decode(explode('base64,', $result['data'])[1]);
        Yii::$app->response->data = $imageData;

        return Yii::$app->response->send();

    }

    /**
     * Note: 获取Java返回，更新数据库信息
     * Author: hesiliang
     * Date: 2024/7/18 15:22
     * @param $message
     * @return void
     * @throws UserException
     */
    public function actionReceiveJavaFeedback()
    {
        // 获取原始的 JSON 数据
        $message = Yii::$app->request->rawBody;
        $message = json_decode($message, true);
        // -1说明是其他功能的处理，这个数据没有入global数据库。
        if ($message['id'] == '-1') {
            // 错误处理
            if ($message['status'] == 400) {
                return;
            }
            foreach ($message['messageBodyEntityList'] as $messageBodyEntity) {
                if ($messageBodyEntity['pdfName'] != '') {
                    $copy_cmd = 'cp -f ' . '"' . $messageBodyEntity['tmpName'] . '"' . ' "' . $messageBodyEntity['pdfName'] . '"';
                    system($copy_cmd);
                }
            }
            return;
        }
        $taskData = GlobalExpExportModel::findone(['id' => $message['id']]);
        if (!empty($taskData)) {
            (new MqThreadPoolExecutor)->runThread($taskData->customer);
            $taskData->exp_status = ($message['status'] == 200) ? 2 : 4;
            // 错误处理
            if ($message['status'] == 400) {
                $taskData->save();
                $bookList = ExpBookRecordModel::find()->where(['id' => $taskData['task_id']])->all();
                foreach ($bookList as $book) {
                    // 修改结束时间
                    $book->end_time = time();
                    $book->exp_status = 3;
                    if (!$book->save()) {
                        throw new UserException(current($book->getFirstErrors()));
                    }
                }
            } else {
                foreach ($message['messageBodyEntityList'] as $messageBodyEntity) {
                    if ($messageBodyEntity['pdfName'] != '') {
                        $taskData->pdf_name = $messageBodyEntity['pdfName'];
                        $copy_cmd = 'cp -f ' . '"' . $messageBodyEntity['tmpName'] . '"' . ' "' . $messageBodyEntity['pdfName'] . '"';
                        system($copy_cmd);
                    }
                }
                $taskData->save();
            }
            $unfinishedData = GlobalExpExportModel::find()
                ->where(['task_id' => $taskData['task_id'], 'customer' => $taskData['customer']])
                ->andWhere(['not in', 'exp_status', [2, 3, 4]])
                ->asArray()
                ->all();
            if (empty($unfinishedData)) {
                // 增加一个小延迟
                usleep(10000);
                $bookList = ExpBookRecordModel::find()->where(['id' => $taskData['task_id']])->all();
                foreach ($bookList as $book) {
                    $allData = GlobalExpExportModel::find()
                        ->where(['task_id' => $taskData['task_id'], 'customer' => $taskData['customer']])
                        ->andWhere(['in', 'exp_status', [2, 3, 4]])
                        ->andWhere(['not', ['pdf_name' => null]])
                        ->asArray()
                        ->all();
                    if (!$book['book_id'] && $book['marge_pdf'] == 2 && $book['type'] == 5) {
                        // 修改结束时间
                        $book->end_time = time();
                        $book->exp_status = 2;
                        $pdfInfo = $taskData['pdf_name'];
                        $pathInfoArr = explode('\\', $pdfInfo);
                        $localName = end($pathInfoArr);
                        $pathInfoArr = explode('/', $localName);
                        $encryption = new Encryption();
                        $pdfInfo = $encryption->encryptFileApi($pdfInfo); // 调用加密接口加密
                        // linux
                        $copy_cmd = 'cp ' . '"' . $pdfInfo . '"' . ' ' . \Yii::getAlias('@filepath') . DS . $book['deep_path'] . DS . $book['file_name'];
                        // windows
                        //$copy_cmd = 'copy ' . $pdfInfo . ' ' . \Yii::getAlias('@filepath') . DS . $deepPath . DS ;
                        \Yii::info(PHP_EOL . $copy_cmd . PHP_EOL);
                        system($copy_cmd);
                        if (!$book->save()) {
                            throw new UserException(current($book->getFirstErrors()));
                        }
                        break;
                    }
                    $zip = new \ZipArchive();
                    $zipName = \Yii::getAlias('@filepath') . DS . $book['deep_path'] . DS . $book['file_name'];
                    $zip->open($zipName, \ZipArchive::CREATE);
                    $exportServer = new ExportServer();
                    foreach ($allData as $data) {
                        if (!isset($data['pdf_name']) || empty($data['pdf_name']) || !file_exists($data['pdf_name'])) {
                            continue;
                        }
                        $pdfInfo = $data['pdf_name'];
                        $pathInfoArr = explode('\\', $pdfInfo);
                        $localName = end($pathInfoArr);
                        $pathInfoArr = explode('/', $localName);
                        $localName = end($pathInfoArr);
                        $encryption = new Encryption();
                        $pdfInfo = $encryption->encryptFileApi($pdfInfo); // 调用加密接口加密
                        $result = $zip->addFile($pdfInfo, $localName);
                        //添加附件
                        $exportServer->exportAttachFiles($zip, [$data['exp_id']], $book['book_id']);
                    }
                    $zip->close();
                    // 修改结束时间
                    $book->end_time = time();
                    $book->exp_status = 2;
                    if (!$book->save()) {
                        throw new UserException(current($book->getFirstErrors()));
                    }
                }
            }
        }
    }


    /**
     * Note: inProject获取仪器列表接口
     * Author: zhouweiming
     * Date: 2024/8/23 下午2:11
     * @return array
     *
     */
    public function actionGetInstrumentData()
    {
        $userId = \Yii::$app->request->post('user_id');

        $companyAuth = CompanyAuthServer::getCompanyAuthByUserId($userId);

        // 获取查看权限
        if (empty($companyAuth) ||
            empty($companyAuth['company_feature']) ||
            empty($companyAuth['company_feature']['view_instruments_manage']) ||
            $companyAuth['company_feature']['view_instruments_manage'] == 0) {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        } else {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        }

        // 用于查询我加入的鹰群或部门
        if ($where['viewAuth'] == '2') {
            // todo 获取我所在的鹰群和部门id
        }

        $instrumentList = (new InstrumentServer())->listAllInstrumentsForProject($where);

        return $this->success([
            'instrument_list' => $instrumentList
        ]);
    }

    /**
     * Note: 批量获取任务关联的项目和工单信息
     * Author: zhhj
     * Date: 2024/9/13 17:49
     * @return array
     */
    public function actionGetTaskData() {
        $taskIds = \Yii::$app->request->post('task_ids', []);
        if (empty($taskIds)) {
            return $this->fail(\Yii::t('base', 'task_ids').': uid');
        }

        // 获取实验信息
        $taskExpData = (new ExperimentServer())->getProjectExpData('task', $taskIds);

        // 获取工单信息
        $taskWoData = (new CollaborationServer())->getProjectWoData('task',$taskIds);

        // 遍历合并
        $taskData = [];
        foreach ($taskExpData as $key => $value) {
            if (isset($taskWoData[$key])) {
                // 合并相同键的值
                $taskData[$key] = array_merge($value, $taskWoData[$key]);
            } else {
                $taskData[$key] = $value;
            }
        }
        foreach ($taskWoData as $key => $value) {
            if (!isset($taskData[$key])) {
                $taskData[$key] = $value;
            }
        }

        return $this->success($taskData);
    }

    /**
     * Note: 批量获取项目关联的项目和工单信息
     * Author: zhhj
     * Date: 2024/9/13 17:49
     * @return array
     */
    public function actionGetProjectData() {
        $projectIds = \Yii::$app->request->post('project_ids', []);
        if (empty($projectIds)) {
            return $this->fail(\Yii::t('base', 'project_ids').': uid');
        }

        // 获取实验信息
        $projectExpData = (new ExperimentServer())->getProjectExpData('project', $projectIds);

        // 获取工单信息
        $projectWoData = (new CollaborationServer())->getProjectWoData('project', $projectIds);

        // 遍历合并
        $projectData = [];
        foreach ($projectExpData as $key => $value) {
            if (isset($projectWoData[$key])) {
                // 合并相同键的值
                $projectData[$key] = array_merge($value, $projectWoData[$key]);
            } else {
                $projectData[$key] = $value;
            }
        }
        foreach ($projectWoData as $key => $value) {
            if (!isset($projectData[$key])) {
                $projectData[$key] = $value;
            }
        }

        return $this->success($projectData);
    }

    /**
     * Note: 根据关键字搜索实验，目前仅支持实验页码和标题
     * Author: zhhj
     * Date: 2024/9/26 14:08
     * @return array
     */
    public function actionSearchExp() {
        $keyword = \Yii::$app->request->get('keyword', '');

        // 获取实验信息
        $expData = (new ExperimentServer())->searchExpByKeyword($keyword);

        return $this->success($expData);
    }

    /**
     * Note: 根据关键字搜索工单，目前仅支持工单编号和标题
     * Author: zhhj
     * Date: 2024/9/26 14:08
     * @return array
     */
    public function actionSearchWo() {
        $keyword = \Yii::$app->request->get('keyword', '');

        // 获取实验信息
        $woData = (new CollaborationServer())->searchWoByKeyword($keyword);

        return $this->success($woData);
    }
}
