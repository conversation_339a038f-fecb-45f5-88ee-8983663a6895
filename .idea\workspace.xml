<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c93863f7-c7bb-4893-8fcd-630ab702c451" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/common/components/PDFMerge/TCPDF/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\Program Files\php\php-5.6.40-Win32-VC11-x64\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-mongodb" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-faker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-codeception" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-debug" />
      <path value="$PROJECT_DIR$/vendor/phpspec/php-diff" />
      <path value="$PROJECT_DIR$/vendor/spout-2.7.3/src" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/common" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpword" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Writer" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Style" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Shared" />
      <path value="$PROJECT_DIR$/vendor/Excel/PHPExcel" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/_staticDocParts" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Section" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/punycode" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/RichText" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/textalk/websocket" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Writer" />
      <path value="$PROJECT_DIR$/vendor/fzaninotto/faker" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CachedObjectStorage" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Style" />
      <path value="$PROJECT_DIR$/vendor/cebe/markdown" />
      <path value="$PROJECT_DIR$/vendor/php-amqplib/php-amqplib" />
      <path value="$PROJECT_DIR$/vendor/react/dns" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/examples" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CalcEngine" />
      <path value="$PROJECT_DIR$/vendor/react/cache" />
      <path value="$PROJECT_DIR$/vendor/react/socket" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/tools" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Worksheet" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/include" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Calculation" />
      <path value="$PROJECT_DIR$/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/config" />
      <path value="$PROJECT_DIR$/vendor/react/stream" />
      <path value="$PROJECT_DIR$/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Reader" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/fonts" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Shared" />
      <path value="$PROJECT_DIR$/vendor/cboden/ratchet" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Chart" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Cell" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/locale" />
      <path value="$PROJECT_DIR$/vendor/pclzip/pclzip" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-zendframework-bridge" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-escaper" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/ratchet/rfc6455" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower/punycode" />
      <path value="$PROJECT_DIR$/vendor/bower/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-composer" />
      <path value="$PROJECT_DIR$/vendor/bower/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/phpdiff/Diff" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-bootstrap" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-gii" />
    </include_path>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2yhz3k6CgFkzu9Adzv7jJ0zgYhD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/integle2025/eln_trunk",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="ComposerRunConfigurationType" factoryName="Composer Script">
      <option name="pathToComposerJson" value="$PROJECT_DIR$/composer.json" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PS-241.18034.69" />
        <option value="bundled-php-predefined-ba97393d7c68-48a1a656d44e-com.jetbrains.php.sharedIndexes-PS-241.18034.69" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c93863f7-c7bb-4893-8fcd-630ab702c451" name="Changes" comment="" />
      <created>1750301396055</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750301396055</updated>
      <workItem from="1750301397319" duration="5800000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>