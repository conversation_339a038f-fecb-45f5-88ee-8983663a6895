import { defineComponent as h, computed as C, createBlock as b, openBlock as c, unref as u, withCtx as t, createElementVNode as o, createVNode as l, createTextVNode as m, toDisplayString as E, normalizeStyle as F, createElementBlock as k, createCommentVNode as A } from "vue";
import { ElDialog as v, ElButton as D, ElIcon as _ } from "element-plus";
import { d as x } from "./index2.js";
import { u as w } from "./vue-i18n.js";
import { _ as y } from "./_plugin-vue_export-helper.js";
const I = { class: "header-left" }, T = { class: "delete-dialog-icon" }, V = { class: "header-right" }, P = {
  key: 0,
  class: "delete-dialog-text"
}, z = { class: "delete-dialog-footer" }, L = /* @__PURE__ */ h({
  __name: "DeleteBookingDialog",
  props: {
    visible: { type: Boolean, default: !1 },
    bookingInfo: { default: null },
    loading: { type: <PERSON><PERSON>an, default: !1 }
  },
  emits: ["update:visible", "confirm", "cancel", "confirmBook"],
  setup(p, { emit: B }) {
    const r = p, { t: n } = w(), a = B, i = C({
      get: () => r.visible,
      set: (e) => {
        a("update:visible", e);
      }
    }), f = () => {
      r.bookingInfo ? a("confirm", r.bookingInfo) : a("confirmBook");
    }, s = () => {
      a("cancel"), i.value = !1;
    };
    return (e, d) => (c(), b(u(v), {
      modelValue: i.value,
      "onUpdate:modelValue": d[0] || (d[0] = (g) => i.value = g),
      title: "",
      width: "430px",
      "show-close": !0,
      "close-on-click-modal": !1,
      "close-on-press-escape": !0,
      modal: !1,
      "append-to-body": !0,
      "modal-class": "delete-booking-dialog",
      "align-center": "",
      onClose: s
    }, {
      header: t(() => [
        o("div", {
          class: "delete-dialog-content",
          style: F({ "align-items": e.bookingInfo ? "flex-start" : "center" })
        }, [
          o("div", I, [
            o("div", T, [
              l(u(_), { class: "warning-icon" }, {
                default: t(() => [
                  l(u(x))
                ]),
                _: 1
              })
            ])
          ]),
          o("div", V, [
            o("div", {
              class: "delete-dialog-title",
              style: F({ "margin-bottom": e.bookingInfo ? "10px" : "0" })
            }, E(u(n)("bookInstruments.deleteConfirmTitle")), 5),
            e.bookingInfo ? (c(), k("div", P, E(u(n)("bookInstruments.deleteConfirmText", { instrument: e.bookingInfo.instrument, time: e.bookingInfo.timeShow })), 1)) : A("", !0)
          ])
        ], 4)
      ]),
      footer: t(() => [
        o("div", z, [
          l(u(D), { onClick: s }, {
            default: t(() => [
              m(E(u(n)("bookInstruments.cancel")), 1)
            ]),
            _: 1
          }),
          l(u(D), {
            type: "primary",
            onClick: f,
            loading: e.loading
          }, {
            default: t(() => [
              m(E(u(n)("bookInstruments.confirm")), 1)
            ]),
            _: 1
          }, 8, ["loading"])
        ])
      ]),
      _: 1
    }, 8, ["modelValue"]));
  }
}), Y = /* @__PURE__ */ y(L, [["__scopeId", "data-v-6df82be0"]]);
var H = {
  name: "zh-cn",
  el: {
    breadcrumb: {
      label: "面包屑"
    },
    colorpicker: {
      confirm: "确定",
      clear: "清空",
      defaultLabel: "颜色选择器",
      description: "当前颜色 {color}，按 Enter 键选择新颜色",
      alphaLabel: "选择透明度的值"
    },
    datepicker: {
      now: "此刻",
      today: "今天",
      cancel: "取消",
      clear: "清空",
      confirm: "确定",
      dateTablePrompt: "使用方向键与 Enter 键可选择日期",
      monthTablePrompt: "使用方向键与 Enter 键可选择月份",
      yearTablePrompt: "使用方向键与 Enter 键可选择年份",
      selectedDate: "已选日期",
      selectDate: "选择日期",
      selectTime: "选择时间",
      startDate: "开始日期",
      startTime: "开始时间",
      endDate: "结束日期",
      endTime: "结束时间",
      prevYear: "前一年",
      nextYear: "后一年",
      prevMonth: "上个月",
      nextMonth: "下个月",
      year: "年",
      month1: "1 月",
      month2: "2 月",
      month3: "3 月",
      month4: "4 月",
      month5: "5 月",
      month6: "6 月",
      month7: "7 月",
      month8: "8 月",
      month9: "9 月",
      month10: "10 月",
      month11: "11 月",
      month12: "12 月",
      weeks: {
        sun: "日",
        mon: "一",
        tue: "二",
        wed: "三",
        thu: "四",
        fri: "五",
        sat: "六"
      },
      weeksFull: {
        sun: "星期日",
        mon: "星期一",
        tue: "星期二",
        wed: "星期三",
        thu: "星期四",
        fri: "星期五",
        sat: "星期六"
      },
      months: {
        jan: "一月",
        feb: "二月",
        mar: "三月",
        apr: "四月",
        may: "五月",
        jun: "六月",
        jul: "七月",
        aug: "八月",
        sep: "九月",
        oct: "十月",
        nov: "十一月",
        dec: "十二月"
      }
    },
    inputNumber: {
      decrease: "减少数值",
      increase: "增加数值"
    },
    select: {
      loading: "加载中",
      noMatch: "无匹配数据",
      noData: "无数据",
      placeholder: "请选择"
    },
    dropdown: {
      toggleDropdown: "切换下拉选项"
    },
    mention: {
      loading: "加载中"
    },
    cascader: {
      noMatch: "无匹配数据",
      loading: "加载中",
      placeholder: "请选择",
      noData: "暂无数据"
    },
    pagination: {
      goto: "前往",
      pagesize: "条/页",
      total: "共 {total} 条",
      pageClassifier: "页",
      page: "页",
      prev: "上一页",
      next: "下一页",
      currentPage: "第 {pager} 页",
      prevPages: "向前 {pager} 页",
      nextPages: "向后 {pager} 页",
      deprecationWarning: "你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"
    },
    dialog: {
      close: "关闭此对话框"
    },
    drawer: {
      close: "关闭此对话框"
    },
    messagebox: {
      title: "提示",
      confirm: "确定",
      cancel: "取消",
      error: "输入的数据不合法!",
      close: "关闭此对话框"
    },
    upload: {
      deleteTip: "按 Delete 键可删除",
      delete: "删除",
      preview: "查看图片",
      continue: "继续上传"
    },
    slider: {
      defaultLabel: "滑块介于 {min} 至 {max}",
      defaultRangeStartLabel: "选择起始值",
      defaultRangeEndLabel: "选择结束值"
    },
    table: {
      emptyText: "暂无数据",
      confirmFilter: "筛选",
      resetFilter: "重置",
      clearFilter: "全部",
      sumText: "合计"
    },
    tour: {
      next: "下一步",
      previous: "上一步",
      finish: "结束导览"
    },
    tree: {
      emptyText: "暂无数据"
    },
    transfer: {
      noMatch: "无匹配数据",
      noData: "无数据",
      titles: ["列表 1", "列表 2"],
      filterPlaceholder: "请输入搜索内容",
      noCheckedFormat: "共 {total} 项",
      hasCheckedFormat: "已选 {checked}/{total} 项"
    },
    image: {
      error: "加载失败"
    },
    pageHeader: {
      title: "返回"
    },
    popconfirm: {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    },
    carousel: {
      leftArrow: "上一张幻灯片",
      rightArrow: "下一张幻灯片",
      indicator: "幻灯片切换至索引 {index}"
    }
  }
};
export {
  Y as D,
  H as z
};
