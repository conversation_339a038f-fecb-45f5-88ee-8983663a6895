<?php
use yii\helpers\Html;
use frontend\models\CompanyDictValue;
use frontend\models\CompanyDictCategory;
use frontend\models\TemplateHistoryNewModel;
?>

<!-- 在页面头部添加与template.php相同的CSS/JS引入 -->
<script>
require(['exp_module', 'file']);
</script>

<div class="tool_data_box">
    <div class="tool_nav nav-bar-height47">
        <?php if (in_array($baseData['type'], [1, 3])): ?>
        <a href="<?= '/?r=/file/preview-template&template_id=' . $baseData['id'] . '&paper_size=A4' ?>" target="_blank" class="click_btn tool_btn"><i
                    class="ico print"></i><?php echo Yii::t('base', 'print') ?></a>                 
        <?php endif; ?>
    </div>
</div>

<div class="exp_data_box">
    <!-- 调试信息：打印baseData和tempData -->
    
    <!-- 添加JavaScript调试输出 -->
    <script>
        console.group('模板历史页面数据');
        console.log('baseData:', <?php echo json_encode(empty($baseData) ? null : $baseData); ?>);
        console.log('tempData:', <?php echo json_encode(empty($tempData) ? null : $tempData); ?>);
        console.log('historyData:', <?php echo json_encode(empty($historyData) ? null : $historyData); ?>);
        console.groupEnd();
    </script>
    
    <!-- 实验信息 -->
    <div class="relative">
        <?= $this->renderFile(Yii::$app->getViewPath().'/experiment/layouts/exp_info.php', [
                'baseData' => empty($baseData) ? NULL : $baseData,
                'weather_json' => [],
                'groupList' => isset($groupList) ? $groupList : [],
                'edit' => 0, // 设置为只读模式
                'isHistory' => true,
                'type' => 'module',
                'temppower' => 0,
                'is_system' => isset($baseData['is_system']) ? $baseData['is_system'] : 0,
                'display_weather' => 0,
                'pageTools' => isset($pageTools) ? $pageTools : [],
                'templateSubtypeList' => isset($templateSubtypeList) ? $templateSubtypeList : [],
            ]);
        ?>
    </div>

    <!-- 实验模块 -->
    <div class="exp_content drag_box" >
        <?php
        if (isset($data['module_data_arr'])) {
            foreach($data['module_data_arr'] as $module) {
                $viewData = [
                    'module' => $module,
                    'experimentId' => $baseData['id'],
                    'type' => 'module', // 强制指定为module类型，以便正确显示组件
                    'is_template_page' => 1,
                    'tmpPower' => isset($tempPower) ? $tempPower : 0,
                    'module_edit' => 0 // 设置为只读模式
                ];
                $viewFile = '';
                switch($module['info']['component_id']) {
                    case \Yii::$app->params['component']['chem']: // InDraw
                        $viewData['saltList'] = isset($saltList) ? $saltList : []; 
                        $viewData['expUserId'] = isset($baseData['user_id']) ? $baseData['user_id'] : 0;
                        $viewFile = '/experiment/module/indraw_v2.php';
                        break;
                    case \Yii::$app->params['component']['operation']:
                    case \Yii::$app->params['component']['discuss']:
                    case \Yii::$app->params['component']['lite']:
                    case \Yii::$app->params['component']['abstract']:
                        $viewFile = '/experiment/module/editor.php';
                        break;
                    case \Yii::$app->params['component']['tlc']:
                        $viewFile = '/experiment/module/tlc.php';
                        break;
                    case \Yii::$app->params['component']['reference']:
                        $viewFile = '/experiment/module/reference.php';
                        break;
                    case \Yii::$app->params['component']['biology']:
                        $viewFile = '/experiment/module/biology.php';
                        break;
                    case \Yii::$app->params['component']['upload']:
                        $viewFile = '/experiment/module/file.php';
                        break;
                    case \Yii::$app->params['component']['picture']:
                        $viewFile = '/experiment/module/image.php';
                        break;
                    case \Yii::$app->params['component']['comment']:
                        $viewFile = '/experiment/module/comment.php';
                        break;
                    case \Yii::$app->params['component']['define_table']:
                        $viewFile = '/experiment/module/define.php';
                        break;
                    case \Yii::$app->params['component']['custom_table']:
                        $viewFile = '/experiment/module/custom_table.php';
                        break;
                    case \Yii::$app->params['component']['xsheet']:
                        $viewFile = '/experiment/module/xsheet.php';
                        break;
                }
                if(!empty($viewFile)) {
                    echo $this->renderFile(Yii::$app->getViewPath() . $viewFile, $viewData);
                }
            }
        }
        ?>
    </div>

    <!-- 结构化数据展示 -->
    <?php if (!empty($tempData) && isset($tempData['structure_data'])): ?>
    <div class="structure_data_box">
        <h3><?php echo Yii::t('base', 'structure_data'); ?></h3>
        <div class="structure_data_content">
            <?php 
                // 展示结构化数据
                if (is_string($tempData['structure_data'])) {
                    $structureData = json_decode($tempData['structure_data'], true);
                } else {
                    $structureData = $tempData['structure_data'];
                }
                
                if (!empty($structureData) && is_array($structureData)) {
                    echo '<table class="structure-data-table">';
                    foreach ($structureData as $key => $value) {
                        echo '<tr>';
                        echo '<td>' . Html::encode($key) . '</td>';
                        echo '<td>' . Html::encode(is_array($value) ? json_encode($value) : $value) . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                } else {
                    echo Yii::t('base', 'no_structure_data');
                }
            ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 添加隐藏的模板ID字段 -->
<input type="hidden" id="temp_id" value="<?php echo isset($historyData['template_id']) ? $historyData['template_id'] : ''; ?>">
<input type="hidden" name="template_id" value="<?php echo isset($historyData['template_id']) ? $historyData['template_id'] : ''; ?>">
<div data-template-id="<?php echo isset($historyData['template_id']) ? $historyData['template_id'] : ''; ?>"></div>

<script>
// 只读模式标记
$(document).ready(function() {
    $('.exp_data_box').addClass('readonly');
    $('.modul_part').addClass('readonly-module');
    $('.drag_box').removeClass('drag_box'); // 移除拖拽功能
    console.log('Template history view loaded - Version: <?php echo isset($historyData["version"]) ? $historyData["version"] : "unknown"; ?>');
    console.log('Template ID: <?php echo isset($historyData["template_id"]) ? $historyData["template_id"] : "unknown"; ?>');
});
</script>
















































