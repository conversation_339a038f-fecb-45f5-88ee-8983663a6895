a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:54:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:48:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:52:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:12:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:4:"5645";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750387954831";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:20:"template/update-temp";s:6:"action";s:59:"frontend\controllers\TemplateController::actionUpdateTemp()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:5645:"insertData%5Bname%5D=dx-g1-temp-0415-1&insertData%5Btfrom%5D=2&insertData%5Btype%5D=1&insertData%5Bdescript%5D=&insertData%5Bstatus%5D=1&insertData%5Bdefine_item%5D=%5B%5D&insertData%5Bsubtype_id%5D=0&insertData%5Bgroup_ids%5D%5B%5D=529&temp_id=4364&auto_save=1&base_data%5Btemplate_id%5D=4364&base_data%5Bkeywords%5D=&base_data%5Btitle%5D=&base_data%5Bweather_json%5D=%7B%22create_weather%22%3A%22%22%2C%22create_temperature%22%3A%22%22%2C%22create_humidity%22%3A%22%22%7D&base_data%5Bdefine_item%5D=%5B%5D&module_data=H4sIAAAAAAAAA%2B1bWY%2FjxhH%2BKwMBeZrdEW9SftN9jO5b2l0IFA%2BJEg%2BJh67FAjGSeBE7PgJvbBjGBk7ixPZDbAM27GRtJ39mZ8b7L9KkKIkSNZQ0IwNZw%2FMyVHWz6qvqYnd1V9dDnyBrnKpHaJ32vfLQJ9MS53vFx07uduC7OicN7kIYjN%2BFfXd8Oq8qEmhDzOfpAHSD7%2FhYTmNUYaADOiBrOq0bmt3ACzLXEgAP0HbvgdlqtM33WgILKBAgdFTFGICf4I17PhwJ%2BB48AqyB0HkXDCUw0Is2dKWl0aO5wDatcS3WRmv2FWndZmn373PTsaKaTE1IuqCLpkbgcczRepdTWz1NkQHl4X0fowIS17Ib7vteue%2B777uzpJvsORWopHKutq4hCaygT%2B2GR4A%2Fy22oDLSRFNYQF4DvPQTW5hXTzowiDRSZk3VLVaDXijC3rQnYHoykHFHpMfgN0Ipz08AwhqOAwk0GLVuEyon0dM7NbAQcRVqbj0WXEzpdMEQ4ggNIC%2BMJcksC2qsCLZqI1gyrdZVxi%2BV0WhABCwjoYfdsMYpoSDL4J%2FNCZ25FMK6aDszEaWGLCkxyD5BN9JZ15g%2FArCZXQNFVgwO%2FeJGbWO0wcQbjJIHiGEEGrI5zS4JGnhY1s6%2FJojXrWt0v3nnz6pMvrX4WmZMtcnYhxSLOObCgRTZEEXQdC6w%2Bf39N3K%2Fu%2Bx7dcYJdKdMKB0tO2AssS9zQbqwLFk6gazQPnJAXthCtM91WVvG0K3SGYzBFwgEEhndjvfz9vy%2Fe%2FtZlV0vSiWxIbU5dNXrZ1ynWS4fo0As9fhaAYRSFICyA7AZ%2F8cO7Lx6%2F7QJvi9jlEk5ZXoizXoCJs0AAh0iUCsDEbsBzVmsOvJd1nVK8oJZoUb%2Bt9159%2BI4L5ZLvLpt6uq%2FJpbU%2B%2Fjf5vADAawbeFHCy5%2Bh7Is3UjjfmNi%2FnoDtJHt%2FUvqOeAVP%2B8fD%2B%2BPWn2z6qpZBdLrAv6ogXZPKMoDASCUAEtvurmnNyGthB8bCvQ4jXR1U9GtA5JydQB%2BX2QMPeTkAGKAgJ4Dixx0Q1Z7W2gO05Ua2keNk0b6hWGHV9fHDYxHr17IuLZ%2F9wgXaIOZbblhRDZTwjm8OQXz79%2B%2BWzLTPuSsytkDNppZM%2FZEmw4zRWYMwo1Yqd5iGPMxpbcXW6yAZ15dAgtl7CWIVk7hlYz5eCR8e6ZOqEuk50Il22eAF1xIuGLOh5VVh3iV3L2l5GvnjzT8%2B%2Fc4dmK2EbjgGC5NUbe6If%2FCTIL3%2F93YvHf3zx16euD7Ks6LR44qHC%2BqsbWjwwpwtFHIEd1M9h0zHX5P9wx2EDe3m3G7YCt481b7fD2BJj2siKtC54buPIM5JCERxGUBjfHQFdfvHk%2BX%2FecH1tKym71hCnOPe6bYP2DIQOW%2FVuGgl5Bpdy%2B5DFY9spw033Z%2B6BlhRxbB3A3BbSzbcP13vfy7RhsH3Pc7dwmO%2FddLvgvbGxcbYH3ruFQ75qm5czbnGSPDcMKznXfs67I1mEOqMIYn7AsccZ3e1D2TWBLuC%2FxLI%2FSSxre%2B7LGMja0F%2B%2BKBacpIM8gqDIL2UcC8JwQStyQ0NQrdN2ayldP1FfKthay6d45AMOPLf%2B12fbNvxlZ%2FJmsWm98bG1QwnBO5txWLh28fabF8%2BeXL7%2FzYv3v3YHbRzNmJ5xUl7IvFXwttJhoHKaZmW1jnXqcvHWGxevf%2Bg%2BdXEK2gXec1Vdge%2FQRzzifP7fp5evf3z55ZPnP7y7Bb2ic%2FMRiNtCj6RDF%2BQdBRlkyjy%2BAuyMXC7du%2BP%2Bi9c%2FuvrNP10qJByCdmGHnRLdS%2B5ingK5P0kCu1Uv8AeGCxcfP7786lP3QaND0C7wrnjBPCEYqCD9ytzihIBEcRKC4AC5xwAcIS25FOe2%2FrrT7zpV2rajOW4u0jZta9%2BTARxBCArBsH22sEdMRC7Fuiy6UMAzpwcm8wAUQEgIgfdwgZvuGZ1SrsW5mdDbzKKTZ5ajIihF7GHjW6X2nKI88W6k%2BG4N%2BQjJvr2weyf9DrP0zXfte0EtdzmltWsXf5gbX73z2o%2BfP7sm%2BWfKO9k7A7iXZwcZ3QAXW46rxMXnf37xwe9um8HcC39D4EQQ%2B16%2FlB%2FmMM%2BffXL11mPXYriSsmsp3MtvdqfdDkN9%2B7TbXrBLNLjxxbWSR7T35UffXrz7atJ9FjSXdeJs8TxoWc2%2F106KIVplFHYt7r7JUn759C9XH73qchEn911O4j4SZCStxXQ5CcR3R0D41acXr%2F3h6vv3tl0pCpuX3wyZPckqZ3vlkk2w%2B%2Bw0f8lwLtKmR8xwLgKll%2FBUaAH95ToVWruPuFBBAjd7WtPNlWbX5LFXdnm%2BSF4%2B%2BWbbsmOuyifXrj33fa6XtyRq5xdJf67nW%2FY12c3kreuW62GnWrfZrcGeNzAXeIuC1m8FNQ0cBe3azq%2BdDOzeCr%2F421svPvjsxy9%2B%2B%2Fx79z1dU%2BzJhthdi5X3ycRCIe6I51k2L%2Bc06iTd9KreAur6ArvpKoclsWxeTqhO0m2hgsV6p3vgZ%2BAqGQ4hGIHu9o4jHPbADoGuWGth45ggcp4nhQh8BlEoiaEUhuyB%2B%2FK9x8%2B%2F%2B8YVdK3E7PLiNXm%2FpLRcV8mOlNJ6YNZiLOsV7OqIRd3FWvWEOWCghGFZX7G1nGRV4WJVEmxUbvjMyhizosKqvLB7zAsvrKZFRYz1Q9Ba6jxVYhfALKooNjnbJSBmjQyo%2BWB0WgbVG4viHBjUcCwqOkDVDA4KGFbq6nQbbI8WfB%2F6VMZ606y8mBdegM5qB5S%2FbNDAXSoXbaAClTb6Ac4uGqu73zXVHNGieanJLCwB%2BoJrEJwJdZF7aJnxpFWeIrftZ8RUBIQ5y5aRWWzCrVrnwRDXAmU8yqrXgrr%2B7oLq5G7fGGtto62%2Fvei5iQDYDhTcABObGMF2iZY7HLDSPA4zLQtqamwNtrY6%2BLnagUwP3lbrtbzNVi%2FeYCw9eJutS5u6cFnvXiPZdCfTxe49sP3KfLCcyXywPMh8sNzGfLB85Z5Z52VtNRe1SJpkTtbzqidBZkG5k91kG3Ygd2yCVWa2WZ3kKHA6vFRp61VCk7glM7s1qWASN0NcMAmZOoKJQZxqumaDt41j3fWbq25RlvVTDuIi7l91W2XAVrRlPGEzewRSr9fVmAV2FJmVzaljo8oMxwLXV5mBxmWVGfh0nSVh8wEB%2BWACgixLLCv2VJoBs4GpNg%2BGPEFrYesbsqvC7H6%2BBKYlg4u%2FU7heqVHtYjFymvaPtBDFFrJtCYWTHaQixoodI9gdDmc62pyMGnVYJTk0psca%2FVC0q8OFVKxYrcT4QlRMV0vhojqGhuc9PDOTE%2F6gcYqUDT49xmP4QEwK9SSZ8%2Bf1WW6KGrA%2FnhykcoIUItPR816vWAtFw0hxmOxGoTiWoqGGUIvLvTCZx5PnUDzKVKYwQpZ750SBEJgcrtdqw%2FMJq8wCpX5FjzLZRGLQTgwFJKiFpDJZCKEjTuxmg7n8KKp2uEZnqBYxtImPwnGtmGj2ygWk2Y5k6rVzjemxuJCOncvtaXfY5IoDPQJnVCkSq8bbKpIj0dqAl2G2FCknqgKENJok2ckMjHihmstQveBM0vn%2BLBtu9%2FvtCZll2vEYPsLFcdYoGEkpelprsFWsWa%2FVGTxJkOFmSSnx7fi5qvHtag8Nsoww7grDcnYo1BKRSbqeKPWMaZUWxRw0LIezKYZA%2B7NUSC8TpQ7CChFVHQWJant0Oi3k8SJdIjmunVEywyGl0bWipgzaubFQl2ZZta7KUUku5kqkLPbikXKjn6SCpXREKM86YW46SY6N2qTKVktS1493%2B1kZT1a1yhBPhs%2BTRJ9JRlNaqlI0QvykQldK%2FGm%2FKZd7KpdTigQt93oTNMYjNSEUinVz8Uk%2FNBGy46jS66Uz5%2F1sYMSUFTYl%2B8tDuQq3u1wUGuKh6owaQNV%2BVhPqs2KTzUeMEEePyyE0xlYj0EilI7rYkAIxMqrOujwzZGaVXL2GDuW8PBkXkGG9qtNhdqbrqXSnxzJYgYxpXBpTsQaG9AowNS7yXTJYD%2BVyHJ2io%2BhoNMBODRGmOLY41Bs4PyqpCD%2BS1YDOyKgfogw%2BUxKl9Oj0NN1BTvMRfIp2cw1GJKh8HgqxwBmjCdo%2F6oW1iIClRgKRNMozfyBYEKIIM50IaKguZIbV%2FqDRKGb1aL0UjHYjNYopNqd4fJTWQ0YZwsI8FUuM8oLCkyUizoXFTnCkB6OjkUEgWcrP8V0ox5ZpbhwtleleOj8jKXqUxop8ot%2FIpxSjQDDhoRDS6kyap1GUZkVmCkPVbKnY0IJhXMvC6WDJH5%2FlIng0PJ42GJodNELqQEj0CGoa85%2FW24xMTPwElx6Vc34kEcdnJRyppOMEEo9p9Vw4i5waTIPjS42SPKCyNNOo98RZL5mZ9JjJLDeKS810NoMNO4zY17lJpReCstkqXI8zEpOh2Gb2PD%2FtwxJ6Hi1oSqYuT0f53kw7TTfztcIkzw%2B6p0Y%2BgFFZnqoilD%2BNYiRP9medfFfgZ3Eqw4yQvByC0ABaw%2Fhyip8gWKPNNFN0sxJNkG0G6odIvzCNC0qDnXRyhbKswQScScVrpxlRTqFTHSFUqZZKIOP2JDmIpIMoOxZwuDzU62S8JMHlojbQuFLdaFalPjboaINSDE%2By7DiAdMXMcJpT8u0clxCqbA1u0P2U%2F7zUPZ9Nx6FM%2B7RYkCc43wCTxqzdwOtUriDl4lAYylRqSE2kguS5mC8gnKa0EUIS0xUUIfCKH4Mr5%2FqUTuSG9eGkqI%2FiuD%2BI1GgB04ORQjDoe%2FTowaP%2FAa7gJI4cPgAA&encode_type=gzip";s:17:"Decoded to Params";a:6:{s:10:"insertData";a:8:{s:4:"name";s:17:"dx-g1-temp-0415-1";s:5:"tfrom";s:1:"2";s:4:"type";s:1:"1";s:8:"descript";s:0:"";s:6:"status";s:1:"1";s:11:"define_item";s:2:"[]";s:10:"subtype_id";s:1:"0";s:9:"group_ids";a:1:{i:0;s:3:"529";}}s:7:"temp_id";s:4:"4364";s:9:"auto_save";s:1:"1";s:9:"base_data";a:5:{s:11:"template_id";s:4:"4364";s:8:"keywords";s:0:"";s:5:"title";s:0:"";s:12:"weather_json";s:66:"{"create_weather":"","create_temperature":"","create_humidity":""}";s:11:"define_item";s:2:"[]";}s:11:"module_data";s:4796:"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";s:11:"encode_type";s:4:"gzip";}}s:6:"SERVER";a:45:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:4:"5645";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750387954831";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:47:"D:/integle2025/eln_trunk/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"50983";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:22:"r=template/update-temp";s:11:"REQUEST_URI";s:24:"/?r=template/update-temp";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:17********.4330001;s:12:"REQUEST_TIME";i:17********;}s:3:"GET";a:1:{s:1:"r";s:20:"template/update-temp";}s:4:"POST";a:6:{s:10:"insertData";a:8:{s:4:"name";s:17:"dx-g1-temp-0415-1";s:5:"tfrom";s:1:"2";s:4:"type";s:1:"1";s:8:"descript";s:0:"";s:6:"status";s:1:"1";s:11:"define_item";s:2:"[]";s:10:"subtype_id";s:1:"0";s:9:"group_ids";a:1:{i:0;s:3:"529";}}s:7:"temp_id";s:4:"4364";s:9:"auto_save";s:1:"1";s:9:"base_data";a:5:{s:11:"template_id";s:4:"4364";s:8:"keywords";s:0:"";s:5:"title";s:0:"";s:12:"weather_json";s:66:"{"create_weather":"","create_temperature":"","create_humidity":""}";s:11:"define_item";s:2:"[]";}s:11:"module_data";s:4796:"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";s:11:"encode_type";s:4:"gzip";}s:6:"COOKIE";a:9:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:15:"center_language";s:2:"CN";s:11:"dataview_id";s:3:"101";s:9:"page_type";s:1:"1";s:16:"last_active_time";s:13:"1750387954831";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1135";s:5:"email";N;s:4:"name";s:6:"chenqi";s:5:"phone";N;s:6:"ticket";s:32:"38828f261ee60584144cf546b2ff9ece";s:8:"reg_time";s:10:"1744077856";s:5:"Token";s:32:"7eb44480540d6e80df79fce77c791828";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:6:"陈奇";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"2";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,84";s:10:"department";a:0:{}s:2:"id";s:4:"1135";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"3";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:101:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:17********.5081811;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:17********.5370779;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:17********.7471409;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:17********.7489171;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:17********.751107;i:4;a:0:{}}i:5;a:5:{i:0;s:39:"Route requested: 'template/update-temp'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:17********.7511351;i:4;a:0:{}}i:6;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.4465699;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:7;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4475751;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4479561;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.448324;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.4487679;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.4488349;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.4500151;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.4531281;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.472904;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.473716;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.474376;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:34:"Route to run: template/update-temp";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.47701;i:4;a:0:{}}i:18;a:5:{i:0;s:75:"Running action: frontend\controllers\TemplateController::actionUpdateTemp()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.4780591;i:4;a:0:{}}i:19;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.862745;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:694;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.8856411;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897788;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.900717;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:704;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4364'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9087961;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.913765;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9247389;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:36;a:5:{i:0;s:75:"Failed to set unsafe attribute 'status' in 'frontend\models\TemplateModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9465821;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:729;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:78:"Failed to set unsafe attribute 'group_ids' in 'frontend\models\TemplateModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9466181;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:729;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=0 WHERE `id`=4364";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.949163;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11453') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.966234;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.967288;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.970289;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:50;a:5:{i:0;s:81:"Failed to set unsafe attribute 'real_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9757209;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:51;a:5:{i:0;s:93:"Failed to set unsafe attribute 'exp_module_relay_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9757831;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:52;a:5:{i:0;s:80:"Failed to set unsafe attribute 'height' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.975832;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:53;a:5:{i:0;s:78:"SELECT * FROM `chem` WHERE (`parent_id`=11453) AND (`type`=2) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9814169;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:56;a:5:{i:0;s:29:"SHOW FULL COLUMNS FROM `chem`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9829099;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:595:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'chem' AND kcu.table_name = 'chem'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9864781;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:62;a:5:{i:0;s:71:"Failed to set unsafe attribute 'height' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.987664;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:85:"Failed to set unsafe attribute 'material_module_info' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9876821;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:86:"Failed to set unsafe attribute 'rgn_mole_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9877031;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:86:"Failed to set unsafe attribute 'rgn_mass_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9877219;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:88:"Failed to set unsafe attribute 'rgn_volume_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.987736;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:86:"Failed to set unsafe attribute 'sol_mole_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9877501;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:86:"Failed to set unsafe attribute 'sol_mass_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9877629;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:88:"Failed to set unsafe attribute 'sol_volume_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.987776;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:86:"Failed to set unsafe attribute 'prd_mole_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9877889;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:86:"Failed to set unsafe attribute 'prd_theo_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.987802;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:86:"Failed to set unsafe attribute 'prd_mass_unit_changed' in 'frontend\models\ChemModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.9878161;i:4;a:3:{i:0;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:83;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:67:"UPDATE `chem` SET `indraw_data`='', `height`='525' WHERE `id`=79201";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.992661;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:104;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:76;a:5:{i:0;s:77:"SELECT * FROM `experiment_substrate` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0032451;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:112;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:79;a:5:{i:0;s:75:"SELECT * FROM `experiment_product` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0082891;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:113;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:82;a:5:{i:0;s:73:"SELECT * FROM `reaction_details` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0107729;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:83;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750387959.0108049;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:88;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11549') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.046092;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:91;a:5:{i:0;s:81:"Failed to set unsafe attribute 'real_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750387959.047745;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:92;a:5:{i:0;s:93:"Failed to set unsafe attribute 'exp_module_relay_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750387959.0477979;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:93;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11549) AND (`type`=2) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0544839;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:96;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.055444;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:99;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0580389;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:102;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.062916;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:105;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.064184;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:108;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.066653;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:111;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.067662;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:114;a:5:{i:0;s:15:"Set savepoint 1";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1750387959.0686171;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:115;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0686359;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:118;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0701261;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:121;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.072475;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:124;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4364) AND (`status`=1)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0733621;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:127;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4364) AND (`group_id`='529')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.081615;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:130;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4436'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.087069;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:133;a:5:{i:0;s:19:"Release savepoint 1";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1750387959.0879409;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:134;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0880311;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:137;a:5:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1750387959.0886741;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:810;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:138;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4364) AND (`status`=1) ORDER BY `class`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1550961;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:141;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.178412;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1059;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:144;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11549) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1800661;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1067;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:147;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.18415;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:150;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.18734;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:153;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4364";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.188643;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:156;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B19')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.254967;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:159;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1129'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.2574091;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:162;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C19')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.32143;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:165;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1130'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.323818;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:168;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D19')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3884139;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:171;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1131'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.391068;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:174;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E19')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.4554529;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:177;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1132'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.4578941;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:180;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F19')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.522084;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:183;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1133'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.524461;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:186;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B20')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.588671;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:189;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1134'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.590924;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:192;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C20')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.65557;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:195;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1135'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.65816;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:198;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D20')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7223921;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:201;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1136'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.724786;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:204;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E20')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7892699;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:207;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1137'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7917731;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:210;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F20')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.847769;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:213;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1138'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.850152;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:216;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750387959.91311;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:18239376;s:4:"time";d:2.4613499641418457;s:8:"messages";a:116:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.8856919;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.897682;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897836;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898653;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4364'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908942;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4364'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9104559;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9138999;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.916894;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.924819;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9262011;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=0 WHERE `id`=4364";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.9492731;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=0 WHERE `id`=4364";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.94998;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11453') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.966291;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11453') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9671021;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9673879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.969872;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.970319;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9712119;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:78:"SELECT * FROM `chem` WHERE (`parent_id`=11453) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981492;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:78:"SELECT * FROM `chem` WHERE (`parent_id`=11453) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982806;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:29:"SHOW FULL COLUMNS FROM `chem`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982954;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:29:"SHOW FULL COLUMNS FROM `chem`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.985847;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:595:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'chem' AND kcu.table_name = 'chem'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.986515;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:61;a:5:{i:0;s:595:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'chem' AND kcu.table_name = 'chem'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9873741;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:67:"UPDATE `chem` SET `indraw_data`='', `height`='525' WHERE `id`=79201";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.9927011;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:104;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:67:"UPDATE `chem` SET `indraw_data`='', `height`='525' WHERE `id`=79201";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.99335;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:104;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:77:"SELECT * FROM `experiment_substrate` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.00332;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:112;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:77:"SELECT * FROM `experiment_substrate` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0042889;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:112;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:80;a:5:{i:0;s:75:"SELECT * FROM `experiment_product` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.008337;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:113;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:81;a:5:{i:0;s:75:"SELECT * FROM `experiment_product` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.009186;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:113;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:84;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750387959.010828;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:85;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750387959.0358851;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:86;a:5:{i:0;s:73:"SELECT * FROM `reaction_details` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0360489;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:87;a:5:{i:0;s:73:"SELECT * FROM `reaction_details` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0371671;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:89;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11549') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0462101;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:90;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11549') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0471351;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:94;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11549) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0545621;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:95;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11549) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.055357;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:97;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.055481;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:98;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.057766;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:100;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0580699;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:101;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.058907;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:103;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0630119;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:104;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0640621;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:106;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0642641;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:107;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0664859;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:109;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0666771;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:110;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0674231;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:112;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0676811;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:113;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.068475;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:116;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.068651;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:117;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0689881;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:119;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.070154;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:120;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.072288;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:122;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.072505;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:123;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0732861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:125;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4364) AND (`status`=1)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0733831;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:126;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4364) AND (`status`=1)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.081265;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:128;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4364) AND (`group_id`='529')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.081666;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:129;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4364) AND (`group_id`='529')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0863061;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:131;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4436'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.087173;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:132;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4436'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0878179;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:135;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.088105;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:136;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.088577;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:139;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4364) AND (`status`=1) ORDER BY `class`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.155211;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:140;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4364) AND (`status`=1) ORDER BY `class`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1561911;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:142;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.178539;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1059;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:143;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.179698;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1059;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:145;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11549) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1801341;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1067;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:146;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11549) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.180861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1067;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:148;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.184247;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:149;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.18681;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:151;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.187413;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:152;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1884251;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:154;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4364";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.188693;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:155;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4364";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.2545829;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:157;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.2550521;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:158;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.257194;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:160;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1129'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.2574561;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:161;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1129'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.32128;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:163;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3214569;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:164;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.323561;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:166;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1130'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.323879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:167;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1130'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.3881359;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:169;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3884709;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:170;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3906729;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:172;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1131'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.391139;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:173;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1131'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.455014;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:175;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.4555531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:176;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.457669;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:178;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1132'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.4579599;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:179;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1132'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.5217521;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:181;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5221679;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:182;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.524199;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:184;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1133'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.524518;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:185;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1133'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.588393;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:187;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5887301;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:188;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5907421;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:190;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1134'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.5909629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:191;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1134'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.6552711;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:193;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.6556499;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:194;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.6579239;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:196;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1135'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.6582041;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:197;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1135'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7220039;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:199;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7224729;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:200;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.724546;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:202;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1136'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7248349;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:203;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1136'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.788856;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:205;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.789361;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:206;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7915339;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:208;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1137'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7918329;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:209;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1137'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.8473599;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:211;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.8478639;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:212;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.849956;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:214;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1138'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.850203;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:215;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1138'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.9058239;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:112:{i:24;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897836;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898653;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4364'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908942;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4364'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9104559;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9138999;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.916894;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.924819;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9262011;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=0 WHERE `id`=4364";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.9492731;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=0 WHERE `id`=4364";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.94998;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11453') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.966291;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11453') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9671021;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9673879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.969872;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.970319;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9712119;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:36;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:78:"SELECT * FROM `chem` WHERE (`parent_id`=11453) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981492;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:78:"SELECT * FROM `chem` WHERE (`parent_id`=11453) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982806;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:29:"SHOW FULL COLUMNS FROM `chem`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982954;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:29:"SHOW FULL COLUMNS FROM `chem`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.985847;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:595:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'chem' AND kcu.table_name = 'chem'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.986515;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:61;a:5:{i:0;s:595:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'chem' AND kcu.table_name = 'chem'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9873741;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:55;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:67:"UPDATE `chem` SET `indraw_data`='', `height`='525' WHERE `id`=79201";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.9927011;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:104;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:67:"UPDATE `chem` SET `indraw_data`='', `height`='525' WHERE `id`=79201";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.99335;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\services\modules\IndrawModuleServer.php";s:4:"line";i:104;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:758;s:8:"function";s:4:"save";s:5:"class";s:44:"frontend\services\modules\IndrawModuleServer";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:77:"SELECT * FROM `experiment_substrate` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.00332;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:112;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:77:"SELECT * FROM `experiment_substrate` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0042889;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:112;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:80;a:5:{i:0;s:75:"SELECT * FROM `experiment_product` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.008337;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:113;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:81;a:5:{i:0;s:75:"SELECT * FROM `experiment_product` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.009186;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:113;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:86;a:5:{i:0;s:73:"SELECT * FROM `reaction_details` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0360489;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:87;a:5:{i:0;s:73:"SELECT * FROM `reaction_details` WHERE (`chem_id`=79201) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0371671;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:145;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:83:"D:\integle2025\eln_trunk\frontend\services\modules\in_material\InMaterialServer.php";s:4:"line";i:114;s:8:"function";s:9:"_saveRows";s:5:"class";s:54:"frontend\services\modules\in_material\InMaterialServer";s:4:"type";s:2:"->";}}}i:89;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11549') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0462101;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:90;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11549') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0471351;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:94;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11549) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0545621;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:95;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11549) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.055357;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:97;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.055481;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:98;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.057766;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:100;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0580699;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:101;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.058907;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:103;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0630119;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:104;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0640621;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:106;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0642641;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:107;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0664859;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:109;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0666771;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:110;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0674231;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:112;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0676811;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:113;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.068475;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_trunk\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:116;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.068651;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:117;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0689881;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:119;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.070154;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:120;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.072288;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:122;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.072505;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:123;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0732861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:125;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4364) AND (`status`=1)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0733831;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:126;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4364) AND (`status`=1)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.081265;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:128;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4364) AND (`group_id`='529')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.081666;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:129;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4364) AND (`group_id`='529')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.0863061;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:131;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4436'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.087173;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:132;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4436'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.0878179;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:135;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.088105;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:136;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.088577;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:139;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4364) AND (`status`=1) ORDER BY `class`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.155211;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:140;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4364) AND (`status`=1) ORDER BY `class`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1561911;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:142;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.178539;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1059;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:143;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4364";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.179698;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1059;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:145;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11549) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1801341;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1067;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:146;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11549) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.180861;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\StructDataServer.php";s:4:"line";i:1067;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:148;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.184247;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:149;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.18681;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:151;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.187413;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:152;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.1884251;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:154;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4364";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.188693;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:155;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4364";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.2545829;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:157;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.2550521;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:158;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.257194;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:160;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1129'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.2574561;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:161;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1129'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.32128;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:163;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3214569;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:164;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.323561;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:166;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1130'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.323879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:167;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1130'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.3881359;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:169;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3884709;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:170;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.3906729;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:172;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1131'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.391139;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:173;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1131'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.455014;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:175;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.4555531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:176;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.457669;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:178;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1132'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.4579599;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:179;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1132'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.5217521;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:181;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F19')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5221679;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:182;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F19')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.524199;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:184;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1133'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.524518;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:185;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1133'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.588393;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:187;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5887301;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:188;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_B20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.5907421;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:190;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1134'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.5909629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:191;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1134'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.6552711;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:193;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.6556499;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:194;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_C20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.6579239;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:196;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1135'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.6582041;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:197;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1135'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7220039;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:199;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7224729;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:200;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_D20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.724546;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:202;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1136'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7248349;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:203;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1136'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.788856;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:205;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.789361;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:206;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_E20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.7915339;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:208;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1137'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.7918329;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:209;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1137'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.8473599;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:211;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F20')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.8478639;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:212;a:5:{i:0;s:108:"SELECT * FROM `structdata_key` WHERE (`template_id`=4364) AND (`relay_id`=11549) AND (`field_key`='0_0_F20')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750387959.849956;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:214;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1138'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.850203;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:215;a:5:{i:0;s:56:"UPDATE `structdata_key` SET `status`=1 WHERE `id`='1138'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750387959.9058239;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:836;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_trunk\frontend\controllers\TemplateController.php";s:4:"line";i:168;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"6854ccf5b5f77";s:3:"url";s:50:"http://dev.eln.integle.com/?r=template/update-temp";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750387959;s:10:"statusCode";i:200;s:8:"sqlCount";i:56;s:9:"mailCount";i:0;}}