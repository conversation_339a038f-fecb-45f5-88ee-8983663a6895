import { defineComponent as rt, ref as w, shallowRef as kt, reactive as st, computed as Ce, watch as ht, onMounted as it, onUnmounted as bt, createElementBlock as K, openBlock as $, withDirectives as gt, createVNode as v, createBlock as W, unref as i, withCtx as m, createElementVNode as C, toDisplayString as x, createTextVNode as L, with<PERSON><PERSON><PERSON> as yt, Fragment as Re, renderList as We, createCommentVNode as R, normalizeStyle as ge, nextTick as _t } from "vue";
import { ElTable as wt, ElTableColumn as Ae, ElPopover as Fe, ElDivider as qe, ElDescriptions as He, ElDescriptionsItem as V, ElInput as xt, ElIcon as X, ElPagination as Oe, ElTooltip as Ct, ElMessage as he, dayjs as zt, ElConfigProvider as Nt, ElDialog as Pt, ElRow as ct, ElRadioGroup as Yt, ElRadioButton as pt, ElDatePicker as vt, ElButton as nt, ElSwitch as Vt } from "element-plus";
import { s as Et, e as St, d as It, c as Mt, a as Dt, l as $t, b as Rt, f as Wt, p as At, g as Ft } from "./index2.js";
import { r as qt, d as Ee } from "./dayjs.min.js";
import { g as Ht } from "./_commonjsHelpers.js";
import { u as dt } from "./vue-i18n.js";
import { v as Tt, a as je, E as Ot } from "./index3.js";
import { D as Lt, z as jt } from "./zh-cn.js";
import { _ as ut } from "./_plugin-vue_export-helper.js";
import Jt from "./InstrumentBookingCreateDialog.js";
var Ye = { exports: {} }, Xt = Ye.exports, mt;
function Zt() {
  return mt || (mt = 1, function(fe, be) {
    (function(ee, T) {
      fe.exports = T(qt());
    })(Xt, function(ee) {
      function T(f) {
        return f && typeof f == "object" && "default" in f ? f : { default: f };
      }
      var ae = T(ee), _ = { name: "zh-cn", weekdays: "星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"), weekdaysShort: "周日_周一_周二_周三_周四_周五_周六".split("_"), weekdaysMin: "日_一_二_三_四_五_六".split("_"), months: "一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"), monthsShort: "1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"), ordinal: function(f, N) {
        return N === "W" ? f + "周" : f + "日";
      }, weekStart: 1, yearStart: 4, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "YYYY/MM/DD", LL: "YYYY年M月D日", LLL: "YYYY年M月D日Ah点mm分", LLLL: "YYYY年M月D日ddddAh点mm分", l: "YYYY/M/D", ll: "YYYY年M月D日", lll: "YYYY年M月D日 HH:mm", llll: "YYYY年M月D日dddd HH:mm" }, relativeTime: { future: "%s内", past: "%s前", s: "几秒", m: "1 分钟", mm: "%d 分钟", h: "1 小时", hh: "%d 小时", d: "1 天", dd: "%d 天", M: "1 个月", MM: "%d 个月", y: "1 年", yy: "%d 年" }, meridiem: function(f, N) {
        var P = 100 * f + N;
        return P < 600 ? "凌晨" : P < 900 ? "早上" : P < 1100 ? "上午" : P < 1300 ? "中午" : P < 1800 ? "下午" : "晚上";
      } };
      return ae.default.locale(_, null, !0), _;
    });
  }(Ye)), Ye.exports;
}
Zt();
var Ve = { exports: {} }, Ut = Ve.exports, ft;
function Kt() {
  return ft || (ft = 1, function(fe, be) {
    (function(ee, T) {
      fe.exports = T();
    })(Ut, function() {
      var ee = "day";
      return function(T, ae, _) {
        var f = function(B) {
          return B.add(4 - B.isoWeekday(), ee);
        }, N = ae.prototype;
        N.isoWeekYear = function() {
          return f(this).year();
        }, N.isoWeek = function(B) {
          if (!this.$utils().u(B)) return this.add(7 * (B - this.isoWeek()), ee);
          var A, Y, F, Z, S = f(this), H = (A = this.isoWeekYear(), Y = this.$u, F = (Y ? _.utc : _)().year(A).startOf("year"), Z = 4 - F.isoWeekday(), F.isoWeekday() > 4 && (Z += 7), F.add(Z, ee));
          return S.diff(H, "week") + 1;
        }, N.isoWeekday = function(B) {
          return this.$utils().u(B) ? this.day() || 7 : this.day(this.day() % 7 ? B : B - 7);
        };
        var P = N.startOf;
        N.startOf = function(B, A) {
          var Y = this.$utils(), F = !!Y.u(A) || A;
          return Y.p(B) === "isoweek" ? F ? this.date(this.date() - (this.isoWeekday() - 1)).startOf("day") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf("day") : P.bind(this)(B, A);
        };
      };
    });
  }(Ve)), Ve.exports;
}
var Gt = Kt();
const Qt = /* @__PURE__ */ Ht(Gt), el = { class: "booked-details" }, tl = { style: { "margin-left": "10px" } }, ll = { class: "instrument-name disable-text-select" }, al = { class: "instrument-code disable-text-select" }, ol = { class: "instruments-divider" }, nl = { class: "instrument-name-pop" }, sl = { class: "instrument-code-pop" }, rl = ["src"], il = ["onClick"], dl = { style: { color: "#7366FF" } }, ul = ["innerHTML"], cl = {
  key: 0,
  class: "instruments-divider booking-info"
}, pl = { class: "flex-space" }, vl = { class: "instrument-name-pop" }, ml = { class: "top-icon" }, fl = { class: "instrument-code-pop" }, kl = { style: { display: "inline-block" } }, hl = ["onClick"], bl = { style: { color: "#7366FF" } }, gl = /* @__PURE__ */ rt({
  __name: "bookedDayDetailsTable",
  props: {
    bookTime: {
      type: Date,
      required: !0
    },
    onlyBooked: {
      type: Boolean,
      required: !0
    }
  },
  emits: ["bookedLoadingChange", "openEdit", "openCreate"],
  setup(fe, { expose: be, emit: ee }) {
    const T = w(!1), ae = w(), _ = w({}), f = w(1), N = w(0), P = w(!1), B = w(null), A = w(!1), Y = w(!1), F = w(), Z = w(), { t: S } = dt(), H = fe, ce = ee, se = w(!1), G = w(), pe = w(1), oe = w(/* @__PURE__ */ new Date()), ne = w(15), O = w(0), re = w(), te = w(/* @__PURE__ */ new Set());
    let ie = -1;
    const de = kt(/* @__PURE__ */ new Set());
    let ue = !1;
    const I = w({}), D = st({
      forbid: "rgba(242, 242, 245, 0.5)",
      otherBooked: ["rgba(223, 223, 230, 0.5)", "rgb(223, 223, 230)"],
      myBooked: ["rgba(224, 224, 255, 0.5)", "rgb(224, 224, 255)"],
      white: ["#FFFFFF", "rgba(115, 102, 255, 0.15)"]
    }), z = [
      {
        prop: "data0",
        label: "",
        key: 0
      },
      {
        prop: "data1",
        label: "1:00",
        key: 1
      },
      {
        prop: "data2",
        label: "",
        key: 2
      },
      {
        prop: "data3",
        label: "2:00",
        key: 3
      },
      {
        prop: "data4",
        label: "",
        key: 4
      },
      {
        prop: "data5",
        label: "3:00",
        key: 5
      },
      {
        prop: "data6",
        label: "",
        key: 6
      },
      {
        prop: "data7",
        label: "4:00",
        key: 7
      },
      {
        prop: "data8",
        label: "",
        key: 8
      },
      {
        prop: "data9",
        label: "5:00",
        key: 9
      },
      {
        prop: "data10",
        label: "",
        key: 10
      },
      {
        prop: "data11",
        label: "6:00",
        key: 11
      },
      {
        prop: "data12",
        label: "",
        key: 12
      },
      {
        prop: "data13",
        label: "7:00",
        key: 13
      },
      {
        prop: "data14",
        label: "",
        key: 14
      },
      {
        prop: "data15",
        label: "8:00",
        key: 15
      },
      {
        prop: "data16",
        label: "",
        key: 16
      },
      {
        prop: "data17",
        label: "9:00",
        key: 17
      },
      {
        prop: "data18",
        label: "",
        key: 18
      },
      {
        prop: "data19",
        label: "10:00",
        key: 19
      },
      {
        prop: "data20",
        label: "",
        key: 20
      },
      {
        prop: "data21",
        label: "11:00",
        key: 21
      },
      {
        prop: "data22",
        label: "",
        key: 22
      },
      {
        prop: "data23",
        label: "12:00",
        key: 23
      },
      {
        prop: "data24",
        label: "",
        key: 24
      },
      {
        prop: "data25",
        label: "13:00",
        key: 25
      },
      {
        prop: "data26",
        label: "",
        key: 26
      },
      {
        prop: "data27",
        label: "14:00",
        key: 27
      },
      {
        prop: "data28",
        label: "",
        key: 28
      },
      {
        prop: "data29",
        label: "15:00",
        key: 29
      },
      {
        prop: "data30",
        label: "",
        key: 30
      },
      {
        prop: "data31",
        label: "16:00",
        key: 31
      },
      {
        prop: "data32",
        label: "",
        key: 32
      },
      {
        prop: "data33",
        label: "17:00",
        key: 33
      },
      {
        prop: "data34",
        label: "",
        key: 34
      },
      {
        prop: "data35",
        label: "18:00",
        key: 35
      },
      {
        prop: "data36",
        label: "",
        key: 36
      },
      {
        prop: "data37",
        label: "19:00",
        key: 37
      },
      {
        prop: "data38",
        label: "",
        key: 38
      },
      {
        prop: "data39",
        label: "20:00",
        key: 39
      },
      {
        prop: "data40",
        label: "",
        key: 40
      },
      {
        prop: "data41",
        label: "21:00",
        key: 41
      },
      {
        prop: "data42",
        label: "",
        key: 42
      },
      {
        prop: "data43",
        label: "22:00",
        key: 43
      },
      {
        prop: "data44",
        label: "",
        key: 44
      },
      {
        prop: "data45",
        label: "23:00",
        key: 45
      },
      {
        prop: "data46",
        label: "",
        key: 46
      },
      {
        prop: "data47",
        label: "24:00",
        key: 47
      }
    ], j = w([]), E = w(!1), Q = async (r) => {
      se.value = !0, document.querySelectorAll(".el-table__cell").forEach((e) => {
        e.classList.toggle("selected-cell", !1);
      }), ce("bookedLoadingChange", se.value);
      const o = G.value, c = "/?r=instrument-booking/get-instruments-data", n = me(H.bookTime), k = await je.post(c, {
        instrumentParam: o,
        lang: window.lang,
        bookTime: [n, n],
        pageSize: ne.value,
        curPage: pe.value,
        onlyBooked: r ? !H.onlyBooked : H.onlyBooked,
        headers: {
          Accept: "application/json",
          "X-Requested-With": "XMLHttpRequest"
        }
      });
      O.value = k.data.data.totalCount, j.value = k.data.data.instruments;
      let a = 0;
      j.value.forEach((e) => {
        var l;
        if (e.cellMap = /* @__PURE__ */ new Map(), e.cellEvents = {}, e.rowIndex = a++, e.bookableTime && e.bookableTime.length > 0)
          e.bookableTime.forEach((t) => {
            De(t, e, !1, null);
          });
        else
          for (let t = 0; t < 48; t++)
            e.cellEvents["data" + t] = {
              events: /* @__PURE__ */ new Set(),
              includedItems: [],
              bookedCellName: []
            }, e.cellEvents["data" + t].events.add("white"), e.cellMap.set("data" + t, {
              forbid: "0% 100%",
              white: [[0, 100]],
              booked: []
            });
        (l = e.bookedInfo) == null || l.forEach((t) => {
          var b, y;
          const s = me(new Date(t.bookedTime[0])), d = me(new Date(t.bookedTime[1])), g = s < n ? "00:00" : (b = t.bookedTime[0]) == null ? void 0 : b.substring(11, 16), u = d > n ? "23:59" : (y = t.bookedTime[1]) == null ? void 0 : y.substring(11, 16);
          u > g && De(g + "-" + u, e, !0, t);
        }), e.cellMap.forEach((t) => {
          t.booked = Ie(t.booked), t.white = Xe(t.white), Me(t);
        });
      }), se.value = !1, ce("bookedLoadingChange", se.value), E.value || (E.value = !0, at("data15"));
    }, le = (r, o, c) => {
      var k, a;
      const n = (a = (k = r.cellEvents) == null ? void 0 : k[o]) == null ? void 0 : a.includedItems;
      c.stopPropagation(), _.value = n ?? [], N.value = n ? n.length : 0, f.value = 1, n && n.length > 0 ? (I.value = {
        instrumentName: r.instrumentName,
        instrumentId: r.instrumentId,
        available_slots: r.available_slots,
        max_advance_day: r.max_advance_day,
        min_advance: r.min_advance,
        max_booking_duration: r.max_booking_duration
      }, ae.value = c.target, T.value = !0, Y.value = !1) : (I.value = {}, T.value = !1);
    }, Se = (r) => {
      const o = r.target;
      !o.closest(".el-popover") && !o.closest(".cell-content") && !o.closest(".delete-booking-dialog") && (T.value = !1);
    }, Je = (r) => {
      ne.value = r, Q(!1);
    }, ve = (r) => {
      pe.value = r, Q(!1);
    }, ye = (r) => {
      f.value = r;
    }, Xe = (r) => {
      if (r.length <= 1) return r;
      r.sort((c, n) => c[0] - n[0]);
      const o = [r[0]];
      for (let c = 1; c < r.length; c++) {
        const n = r[c], k = o[o.length - 1];
        n[0] <= k[1] ? k[1] = Math.max(k[1], n[1]) : o.push(n);
      }
      return o;
    }, Ie = (r) => {
      if (r.length <= 1) return r;
      r.sort((c, n) => c.size[0] - n.size[0]);
      const o = [r[0]];
      for (let c = 1; c < r.length; c++) {
        const n = r[c], k = o[o.length - 1];
        n.size[0] <= k.size[1] ? k.size[1] = Math.max(k.size[1], n.size[1]) : o.push(n);
      }
      return o;
    }, Me = (r) => {
      var a, e, l, t;
      const o = [], c = ((e = (a = r.white) == null ? void 0 : a[0]) == null ? void 0 : e[0]) ?? 100, n = ((t = (l = r.booked) == null ? void 0 : l[0]) == null ? void 0 : t.size[0]) ?? 100, k = Math.min(c, n);
      for (let s = 0; s < 2; s++) {
        k > 0 && o.push(D.forbid + " 0% " + k + "%"), c < n && o.push(D.white[0] + " " + c + "% " + n + "%");
        for (let d = 0; d < r.booked.length; d++) {
          const g = r.booked[d];
          o.push(D[g.color][s] + " " + g.size.join("% ") + "%"), d < r.booked.length - 1 && o.push(D.white[0] + " " + g.size[1] + "% " + r.booked[d + 1].size[0] + "%");
        }
        r.white.forEach((d) => {
          o.push(D.white[0] + " " + d.join("% ") + "%");
        }), o.push(D.forbid + " " + r.forbid), s === 0 ? (r.final = `linear-gradient(90deg, ${o.join(", ")})`, o.length = 0) : r.hoverFinal = `linear-gradient(90deg, ${o.join(", ")})`;
      }
    }, De = (r, o, c, n) => {
      const k = r.split("-"), a = k[0], e = k[1], l = a.split(":").map((p) => Number(p)), t = e.split(":").map((p) => Number(p)), s = o.cellMap;
      let d, g, u;
      function b(p) {
        s.set(p, {
          forbid: "0% 100%",
          white: [],
          booked: []
        }), o.cellEvents[p] = {
          events: /* @__PURE__ */ new Set(),
          includedItems: [],
          bookedCellName: []
        };
      }
      const y = [];
      if (l[0] === t[0] && (l[1] > 30 || t[1] < 30)) {
        if (l[1] > 30) {
          const p = Math.round((l[1] - 30) / 30 * 100), h = Math.round((t[1] - 30) / 30 * 100);
          if (s.get("data" + (l[0] * 2 + 1)) || b("data" + (l[0] * 2 + 1)), c) {
            o.cellEvents["data" + (l[0] * 2 + 1)].events.add("booked"), o.cellEvents["data" + (l[0] * 2 + 1)].includedItems.push(n), y.push("data" + (l[0] * 2 + 1));
            const M = n.currentBooked ? "myBooked" : "otherBooked";
            s.get("data" + (l[0] * 2 + 1)).booked.push({
              color: M,
              size: [p, h]
            });
          } else
            s.get("data" + (l[0] * 2 + 1)).white.push([p, h]);
        } else if (t[1] < 30) {
          const p = Math.round(l[1] / 30 * 100), h = Math.round(t[1] / 30 * 100);
          if (s.get("data" + l[0] * 2) || b("data" + l[0] * 2), c) {
            o.cellEvents["data" + l[0] * 2].events.add("booked"), o.cellEvents["data" + l[0] * 2].includedItems.push(n), y.push("data" + l[0] * 2);
            const M = n.currentBooked ? "myBooked" : "otherBooked";
            s.get("data" + l[0] * 2).booked.push({
              color: M,
              size: [p, h]
            });
          } else
            s.get("data" + l[0] * 2).white.push([p, h]);
        }
      } else {
        if (l[1] > 30) {
          if (d = l[0] * 2 + 2, s.get("data" + (l[0] * 2 + 1)) || (b("data" + (l[0] * 2 + 1)), b("data" + (l[0] * 2 + 2))), u = (l[1] - 30) / 30, u = Math.round(u * 100), u !== 100)
            if (c) {
              o.cellEvents["data" + (l[0] * 2 + 1)].events.add("booked"), o.cellEvents["data" + (l[0] * 2 + 1)].includedItems.push(n), y.push("data" + (l[0] * 2 + 1));
              const h = n.currentBooked ? "myBooked" : "otherBooked";
              s.get("data" + (l[0] * 2 + 1)).booked.push({
                color: h,
                size: [u, 100]
              });
            } else
              u === 0 && o.cellEvents["data" + (l[0] * 2 + 1)].events.add("white"), s.get("data" + (l[0] * 2 + 1)).white.push([u, 100]);
        } else if (d = l[0] * 2 + 1, s.get("data" + l[0] * 2) || (b("data" + l[0] * 2), b("data" + (l[0] * 2 + 1))), u = l[1] / 30, u = Math.round(u * 100), u !== 100)
          if (c) {
            o.cellEvents["data" + l[0] * 2].events.add("booked"), o.cellEvents["data" + l[0] * 2].includedItems.push(n), y.push("data" + l[0] * 2);
            const h = n.currentBooked ? "myBooked" : "otherBooked";
            s.get("data" + l[0] * 2).booked.push({
              color: h,
              size: [u, 100]
            });
          } else
            u === 0 && o.cellEvents["data" + l[0] * 2].events.add("white"), s.get("data" + l[0] * 2).white.push([u, 100]);
        if (c && (s.get("data" + d).left = 95 - u, s.get("data" + d).width = (100 - u) * 0.6), t[1] >= 30) {
          if (g = t[0] * 2, s.get("data" + (t[0] * 2 + 1)) || b("data" + (t[0] * 2 + 1)), u = (t[1] - 30) / 30, u = Math.round(u * 100), u !== 0)
            if (c) {
              o.cellEvents["data" + (t[0] * 2 + 1)].events.add("booked"), o.cellEvents["data" + (t[0] * 2 + 1)].includedItems.push(n), y.push("data" + (t[0] * 2 + 1));
              const h = n.currentBooked ? "myBooked" : "otherBooked";
              s.get("data" + (t[0] * 2 + 1)).booked.push({
                color: h,
                size: [0, u]
              });
            } else
              u === 100 && o.cellEvents["data" + (t[0] * 2 + 1)].events.add("white"), s.get("data" + (t[0] * 2 + 1)).white.push([0, u]);
        } else if (g = t[0] * 2 - 1, s.get("data" + t[0] * 2) || b("data" + t[0] * 2), u = t[1] / 30, u = Math.round(u * 100), u !== 0)
          if (c) {
            o.cellEvents["data" + t[0] * 2].events.add("booked"), o.cellEvents["data" + t[0] * 2].includedItems.push(n), y.push("data" + t[0] * 2);
            const h = n.currentBooked ? "myBooked" : "otherBooked";
            s.get("data" + t[0] * 2).booked.push({
              color: h,
              size: [0, u]
            });
          } else
            u === 100 && o.cellEvents["data" + t[0] * 2].events.add("white"), s.get("data" + t[0] * 2).white.push([0, u]);
        for (let p = d; p <= g; p++)
          if (s.get("data" + p) || b("data" + p), c) {
            o.cellEvents["data" + p].events.add("booked"), o.cellEvents["data" + p].includedItems.push(n), y.push("data" + p);
            const h = n.currentBooked ? "myBooked" : "otherBooked";
            s.get("data" + p).booked.push({
              color: h,
              size: [0, 100]
            });
          } else
            o.cellEvents["data" + p].events.add("white"), s.get("data" + p).white.push([0, 100]);
        if (c) {
          let p = o.cellEvents["data" + d].includedItems[0];
          !p && l[0] === t[0] && t[1] - l[1] === 30 ? (p = o.cellEvents["data" + (d - 1)].includedItems[0], p.remark ? o["data" + (d - 1)] = p.experimenter + " " + p.timeShow + " " + S("bookInstruments.remark") + p.remark : o["data" + (d - 1)] = p.experimenter + " " + p.timeShow, s.get("data" + (d - 1)).left = 5, s.get("data" + (d - 1)).width = 60, s.get("data" + d).left = 0, s.get("data" + d).width = 0) : (s.get("data" + d).width += u * 0.6, s.get("data" + d).width += (g - d + 1) * 60, s.get("data" + d).width / 0.6 < 100 ? o["data" + d] = "" : p.remark ? o["data" + d] = p.experimenter + " " + p.timeShow + " " + S("bookInstruments.remark") + p.remark : o["data" + d] = p.experimenter + " " + p.timeShow);
        }
      }
      y.length > 0 && y.forEach((p) => {
        o.cellEvents[p].bookedCellName.push(y);
      });
    }, ke = ({ column: r }) => {
      if (r.property === "instrumentName")
        return "clear-header-border";
      {
        const c = r.property.slice(4), n = Number(c);
        let k = "";
        return n === 47 ? k += "header-time-class47" : n % 2 === 1 ? k += "header-time-class" : k += "clear-border-right", k;
      }
    }, Ze = ({
      column: r,
      rowIndex: o
    }) => {
      let c = "";
      if (r.property !== "instrumentName") {
        c += " default-cell-color";
        const k = r.property.slice(4);
        Number(k) % 2 === 0 && (c += " clear-border-right");
      }
      return c;
    }, $e = ({
      row: r,
      column: o,
      rowIndex: c
    }) => {
      var e, l, t;
      const n = de.value, k = (e = r.cellMap) == null ? void 0 : e.get(o.property), a = {};
      return (t = (l = r.cellMap) == null ? void 0 : l.get(o.property)) != null && t.width && (a.zIndex = 1), k && ((c === ie && n.has(o.property) ? 1 : 0) ? a.background = k.hoverFinal : a.background = k.final), a;
    }, Ue = (r, o, c) => {
      var a, e, l, t, s, d, g, u;
      ie = r.rowIndex;
      const n = (e = (a = r.cellEvents) == null ? void 0 : a[o.property]) == null ? void 0 : e.events;
      if (n && n.values().next().value === "white" && n.size === 1) {
        c.style.background = D.white[1];
        return;
      }
      (t = (l = r.cellEvents) == null ? void 0 : l[o.property]) != null && t.bookedCellName && ((d = (s = r.cellEvents) == null ? void 0 : s[o.property]) == null ? void 0 : d.bookedCellName.length) > 0 && (de.value = new Set(r.cellEvents[o.property].bookedCellName.flat(2)));
      const k = (u = (g = r.cellEvents) == null ? void 0 : g[o.property]) == null ? void 0 : u.includedItems;
      if ((k == null ? void 0 : k.length) > 0) {
        let b = "";
        k == null || k.forEach((y) => {
          y.remark ? b += y.experimenter + " " + y.timeShow + " " + S("bookInstruments.remark") + y.remark + "<br/>" : b += y.experimenter + " " + y.timeShow + "<br/>";
        }), Z.value = b, F.value = c, Y.value = !0;
      } else
        Y.value = !1;
    }, Ke = (r, o, c) => {
      ie = -1, c.style.background === D.white[1] && (c.style.background = D.white[0]), de.value.size > 0 && (de.value = /* @__PURE__ */ new Set()), Y.value = !1;
    }, Ge = (r) => {
      var n;
      const o = I.value, c = {
        name: o.instrumentName,
        instrument_id: o.instrumentId,
        id: r.bookingId,
        start_time: r.bookedTime[0],
        end_time: r.bookedTime[1],
        related_experiment: (n = r.ELNPage) == null ? void 0 : n.join(","),
        warn: r.warn,
        source: "bookInstruments",
        remark: r.remark,
        available_slots: Array.isArray(o.available_slots) ? o.available_slots : JSON.parse(o.available_slots || "[]"),
        max_advance_day: o.max_advance_day,
        min_advance: JSON.parse(o.min_advance || '{"value":"","unit":""}'),
        max_booking_duration: JSON.parse(o.max_booking_duration || '{"value":"","unit":""}')
      };
      ce("openEdit", c);
    }, Qe = (r) => {
      window.open("https://idataeln.integle.com/?exp_id=" + r, "_blank");
    }, et = (r, o) => {
      var c, n, k, a;
      if ((n = (c = r.cellMap) == null ? void 0 : c.get(o.prop)) != null && n.width) {
        const e = (k = r.cellMap.get(o.prop)) == null ? void 0 : k.width, l = -((a = r.cellMap.get(o.prop)) == null ? void 0 : a.left);
        return {
          width: e + "px",
          left: l + "%"
        };
      }
      return {};
    }, tt = {
      mounted(r) {
        let o = !1, c = null;
        r.addEventListener("mousedown", (a) => {
          document.querySelectorAll(".el-table__cell").forEach((l) => {
            l.classList.toggle("selected-cell", !1);
          });
          const e = a.target.closest(".el-table__cell");
          if (e) {
            if (c = _e(e), te.value.has(`${c.rowIndex}-${c.colIndex}`)) {
              const t = Array.from(te.value).map((y) => parseInt(y.split("-")[1])), s = Math.min(...t), d = Math.max(...t), g = [Be(s - 1), Be(d)], u = j.value[c.rowIndex], b = {
                name: u.instrumentName,
                id: u.instrumentId,
                time: g,
                related_experiment: "",
                source: "bookInstruments",
                remark: "",
                available_slots: Array.isArray(u.available_slots) ? u.available_slots : JSON.parse(u.available_slots || "[]"),
                max_advance_day: u.max_advance_day,
                min_advance: JSON.parse(u.min_advance || '{"value":"","unit":""}'),
                max_booking_duration: JSON.parse(u.max_booking_duration || '{"value":"","unit":""}')
              };
              ce("openCreate", b), te.value.clear();
              return;
            } else
              te.value.clear();
            o = !0;
            const l = Te(c, c);
            Le(l, c.rowIndex), r.addEventListener("mousemove", n), r.addEventListener("mouseup", k);
          } else
            te.value.clear();
        });
        const n = (a) => {
          var l;
          if (!o) return;
          const e = document.elementFromPoint(a.clientX, a.clientY);
          if (e.closest(".el-table__row") && (e.closest(".el-table__row").style.cursor = "e-resize"), e.closest("tr")) {
            const t = _e(e), s = j.value[c.rowIndex].cellEvents;
            if ((l = s == null ? void 0 : s["data" + (t.colIndex - 1)]) != null && l.events.has("booked") && (he({
              message: S("bookInstruments.bookingConflict"),
              grouping: !0,
              type: "warning"
            }), k()), c && t) {
              const d = Te(c, t);
              Le(d, c.rowIndex);
            }
          }
          a.stopPropagation();
        }, k = () => {
          o = !1, c = null, document.querySelectorAll(".el-table__row").forEach((a) => {
            a.style.cursor = "default";
          }), r.removeEventListener("mousemove", n), r.removeEventListener("mouseup", k);
        };
      }
    }, _e = (r) => {
      const o = r.closest("tr").rowIndex, c = Array.from(r.closest("tr").children).indexOf(r.closest("td"));
      return {
        rowIndex: o,
        colIndex: c
      };
    }, Te = (r, o) => {
      const c = r.rowIndex, n = Math.min(r.colIndex, o.colIndex), k = Math.max(r.colIndex, o.colIndex), a = [];
      for (let e = n; e <= k; e++)
        a.push(`${c}-${e}`);
      return a;
    }, Le = (r, o) => {
      var a, e, l;
      const c = j.value[o].cellEvents, n = (a = re.value) == null ? void 0 : a.$el;
      if (!n) return;
      const k = n.querySelectorAll(".el-table__row")[o];
      for (let t = 0; t < k.querySelectorAll(".el-table__cell").length - 1; t++) {
        const s = k.querySelectorAll(".el-table__cell")[t + 1];
        if (((e = c == null ? void 0 : c["data" + t]) == null ? void 0 : e.events.size) === 1 && ((l = c == null ? void 0 : c["data" + t]) == null ? void 0 : l.events.values().next().value) === "white") {
          const d = _e(s), g = r.includes(`${d.rowIndex}-${d.colIndex}`);
          s.classList.toggle("selected-cell", g), g && te.value.add(`${d.rowIndex}-${d.colIndex}`);
        }
      }
    }, Be = (r) => {
      const c = Math.floor(r / 2).toString().padStart(2, "0"), n = r % 2 === 0 ? "00" : "30";
      return `${me(H.bookTime)} ${c}:${n}:00`;
    }, me = (r) => {
      const o = r.getFullYear(), c = String(r.getMonth() + 1).padStart(2, "0"), n = String(r.getDate()).padStart(2, "0");
      return `${o}-${c}-${n}`;
    }, we = (r, o) => {
      if (me(H.bookTime) !== me(oe.value))
        return !1;
      const c = oe.value.getHours(), n = oe.value.getMinutes(), k = r.slice(4), a = Number(k);
      let e = c * 2;
      return n >= 30 ? e++ : e += o, a === e;
    }, ze = (r, o) => {
      const c = oe.value.getHours(), n = oe.value.getMinutes(), k = n >= 30 ? (n - 30) / 30 : n / 30, a = r.slice(4), e = Number(a);
      let l = c * 2;
      if (n >= 30) {
        if (l++, e === l)
          return {
            left: `calc(-50% + ${k * 100}% - 3.5px)`
          };
      } else if (l += o, e === l)
        return {
          left: `calc(-150% + ${k * 100}% - 3px)`
        };
      return "";
    }, Ne = Ce(() => {
      const r = oe.value.getMinutes();
      return {
        left: `${(r >= 30 ? (r - 30) / 30 : r / 30) * 100}%`
      };
    }), xe = (r) => {
      ue = !1, B.value = r, P.value = !0;
    }, Pe = async (r) => {
      A.value = !0;
      try {
        (await je.post("/?r=instrument-booking/delete-instrument-booking", {
          id: r,
          headers: {
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        })).data.status === 1 ? (he({
          message: S("bookInstruments.deleteSuccess"),
          grouping: !0,
          type: "success"
        }), ue = !0, Q(!1)) : he({
          message: S("bookInstruments.deleteError"),
          grouping: !0,
          type: "error"
        });
      } catch {
        he({
          message: S("bookInstruments.deleteError"),
          grouping: !0,
          type: "error"
        });
      } finally {
        A.value = !1, P.value = !1;
      }
    }, lt = () => {
      T.value = !ue, P.value = !1;
    }, at = (r) => {
      _t(() => {
        if (!re.value) return;
        const o = z.findIndex((a) => a.prop === r);
        if (o === -1) return;
        const k = 270 + o * 60 - 200;
        setTimeout(() => {
          re.value && re.value.setScrollLeft(Math.max(0, k));
        }, 100);
      });
    };
    return ht(
      () => H.bookTime,
      (r, o) => {
        Q(!1);
      },
      {
        deep: !0,
        immediate: !0
      }
    ), be({
      handlePrefixClick: Q
    }), it(() => {
      document.addEventListener("click", Se);
    }), bt(() => {
      document.removeEventListener("click", Se);
    }), (r, o) => {
      const c = Tt;
      return $(), K("div", el, [
        gt(($(), W(i(wt), {
          data: j.value,
          border: "",
          "element-loading-custom-class": "custom-loading",
          "element-loading-background": "rgba(255, 255, 255, 0.8)",
          style: { width: "calc(100vw - 72px)", position: "relative" },
          "header-cell-class-name": ke,
          "cell-class-name": Ze,
          ref_key: "bookingDetailTable",
          ref: re,
          "cell-style": $e,
          "header-row-class-name": "clear-header-border",
          onCellMouseEnter: Ue,
          onCellMouseLeave: Ke,
          "row-height": 100
        }, {
          default: m(() => [
            v(i(Ae), {
              fixed: "",
              prop: "instrumentName",
              width: "270"
            }, {
              header: m(() => [
                v(i(xt), {
                  modelValue: G.value,
                  "onUpdate:modelValue": o[1] || (o[1] = (n) => G.value = n),
                  placeholder: i(S)("bookInstruments.searchInstrument"),
                  clearable: "",
                  class: "search-instruments",
                  onKeyup: o[2] || (o[2] = yt((n) => Q(!1), ["enter"]))
                }, {
                  prefix: m(() => [
                    v(i(X), {
                      onClick: o[0] || (o[0] = (n) => Q(!1))
                    }, {
                      default: m(() => [
                        v(i(Et))
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["modelValue", "placeholder"])
              ]),
              default: m((n) => [
                v(i(Fe), {
                  class: "box-item",
                  placement: "right",
                  transition: "el-zoom-in-left",
                  "show-arrow": !1,
                  width: "352"
                }, {
                  reference: m(() => [
                    C("div", tl, [
                      C("div", ll, x(n.row.instrumentName), 1),
                      C("div", al, x(n.row.instrumentCode), 1)
                    ])
                  ]),
                  default: m(() => [
                    C("div", ol, [
                      C("div", nl, x(n.row.instrumentName), 1),
                      C("div", sl, x(n.row.instrumentCode), 1),
                      v(i(qe)),
                      v(i(He), { column: 1 }, {
                        default: m(() => [
                          v(i(V), {
                            label: i(S)("bookInstruments.state"),
                            "class-name": "popover-state-class"
                          }, {
                            default: m(() => [
                              L(x(n.row.state), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.responsible")
                          }, {
                            default: m(() => [
                              L(x(n.row.responsiblePerson), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.position"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(n.row.position), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.bookableTime"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(n.row.bookableTime.join(",")), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.singleTimeLimit"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(n.row.singleTimeLimit), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.advance"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(n.row.advance), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"])
                        ]),
                        _: 2
                      }, 1024)
                    ]),
                    C("img", {
                      src: n.row.picture
                    }, null, 8, rl)
                  ]),
                  _: 2
                }, 1024)
              ]),
              _: 1
            }),
            ($(), K(Re, null, We(z, (n) => v(i(Ae), {
              key: n.key,
              prop: n.prop,
              label: n.label,
              width: "60",
              resizable: !1
            }, {
              header: m(() => [
                L(x(n.label) + " ", 1),
                we(n.prop, 1) ? ($(), K("div", {
                  key: 0,
                  class: "red-dot",
                  style: ge(ze(n.prop, 1))
                }, null, 4)) : R("", !0)
              ]),
              default: m(({ row: k }) => [
                C("div", {
                  class: "blankCell",
                  onClick: (a) => le(k, n.prop, a)
                }, [
                  C("div", {
                    class: "cell-tips disable-text-select",
                    style: ge(et(k, n))
                  }, x(k[n.prop]), 5),
                  we(n.prop, 0) ? ($(), K("div", {
                    key: 0,
                    class: "red-timeline-offset red-timeline",
                    style: ge(Ne.value)
                  }, null, 4)) : R("", !0)
                ], 8, il)
              ]),
              _: 2
            }, 1032, ["prop", "label"])), 64))
          ]),
          _: 1
        }, 8, ["data"])), [
          [c, se.value],
          [tt]
        ]),
        v(i(Oe), {
          "page-sizes": [10, 15, 25, 50],
          "page-size": ne.value,
          total: O.value,
          layout: "sizes, prev, slot, next, total",
          "pager-count": 2,
          style: { "margin-left": "7px" },
          onSizeChange: Je,
          onPrevClick: ve,
          onNextClick: ve
        }, {
          default: m(() => [
            C("span", dl, x(pe.value), 1),
            L("/" + x(Math.ceil(O.value / ne.value)), 1)
          ]),
          _: 1
        }, 8, ["page-size", "total"]),
        v(i(Ct), {
          class: "box-item",
          effect: "dark",
          visible: Y.value,
          placement: "top",
          trigger: "hover",
          "virtual-triggering": "",
          "virtual-ref": F.value
        }, {
          content: m(() => [
            C("div", { innerHTML: Z.value }, null, 8, ul)
          ]),
          _: 1
        }, 8, ["visible", "virtual-ref"]),
        v(i(Fe), {
          visible: T.value,
          width: 400,
          trigger: "click",
          placement: "right",
          "virtual-ref": ae.value,
          "virtual-triggering": "",
          "show-arrow": !1
        }, {
          default: m(() => [
            _.value[f.value - 1] ? ($(), K("div", cl, [
              C("div", pl, [
                C("div", vl, x(_.value[f.value - 1].experimenter) + x(i(S)("bookInstruments.bookingOf")), 1),
                C("div", ml, [
                  _.value[f.value - 1].updateShow ? ($(), W(i(X), { key: 0 }, {
                    default: m(() => [
                      v(i(St), {
                        onClick: o[3] || (o[3] = (n) => Ge(_.value[f.value - 1]))
                      })
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].deleteShow ? ($(), W(i(X), { key: 1 }, {
                    default: m(() => [
                      v(i(It), {
                        onClick: o[4] || (o[4] = (n) => xe(_.value[f.value - 1]))
                      })
                    ]),
                    _: 1
                  })) : R("", !0)
                ])
              ]),
              C("div", fl, x(_.value[f.value - 1].instrument), 1),
              v(i(qe)),
              v(i(He), { column: 1 }, {
                default: m(() => [
                  _.value[f.value - 1].bookedTime ? ($(), W(i(V), { key: 0 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i(Mt))
                        ]),
                        _: 1
                      }),
                      L(" " + x(_.value[f.value - 1].timeShow), 1)
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].reminder ? ($(), W(i(V), { key: 1 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i(Dt))
                        ]),
                        _: 1
                      }),
                      L(" " + x(_.value[f.value - 1].reminder), 1)
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].ELNPage ? ($(), W(i(V), { key: 2 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i($t))
                        ]),
                        _: 1
                      }),
                      C("div", kl, [
                        ($(!0), K(Re, null, We(_.value[f.value - 1].ELNPage, (n) => ($(), K("div", {
                          class: "eln-page",
                          key: n,
                          onClick: (k) => Qe(n)
                        }, x(n), 9, hl))), 128))
                      ])
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].remark ? ($(), W(i(V), { key: 3 }, {
                    default: m(() => {
                      var n;
                      return [
                        o[6] || (o[6] = C("span", { class: "icon-align" }, [
                          C("svg", {
                            width: "13",
                            height: "13",
                            viewBox: "0 0 13.2 13.2",
                            fill: "none",
                            xmlns: "http://www.w3.org/2000/svg",
                            "xmlns:xlink": "http://www.w3.org/1999/xlink"
                          }, [
                            C("defs"),
                            C("path", {
                              id: "路径（边框）",
                              d: "M6.6 0C9.33 0 11.26 1.93 11.26 1.93C13.2 3.86 13.2 6.59 13.2 6.59C13.2 9.33 11.26 11.26 11.26 11.26C9.33 13.2 6.6 13.2 6.6 13.2C3.86 13.2 1.93 11.26 1.93 11.26C0 9.33 0 6.59 0 6.59C0 3.86 1.93 1.93 1.93 1.93C3.86 0 6.6 0 6.6 0ZM2.78 2.78C2.78 2.78 4.36 1.19 6.6 1.19C6.6 1.19 8.83 1.19 10.41 2.78C10.41 2.78 12 4.36 12 6.59C12 6.59 12 8.83 10.41 10.41C10.41 10.41 8.83 12 6.6 12C6.6 12 4.36 12 2.78 10.41C2.78 10.41 1.19 8.83 1.19 6.59C1.19 6.59 1.19 4.36 2.78 2.78ZM6.6 3.6C6.27 3.6 6 3.86 6 4.2C6 4.53 6.26 4.8 6.6 4.8C6.93 4.8 7.2 4.53 7.2 4.2C7.2 4.04 7.14 3.88 7.03 3.77C6.91 3.66 6.76 3.6 6.6 3.6ZM6 6.59C6 6.26 6.26 6 6.6 6C6.93 6 7.2 6.26 7.2 6.59L7.2 8.99C7.2 9.33 6.93 9.59 6.6 9.59C6.26 9.59 6 9.33 6 8.99L6 6.59Z",
                              fill: "#B6B6BF",
                              "fill-opacity": "1.000000",
                              "fill-rule": "evenodd"
                            })
                          ])
                        ], -1)),
                        L(" " + x((n = _.value[f.value - 1]) == null ? void 0 : n.remark), 1)
                      ];
                    }),
                    _: 1
                  })) : R("", !0)
                ]),
                _: 1
              }),
              v(i(Oe), {
                "page-size": 1,
                total: N.value,
                layout: "prev, slot, next",
                style: { "margin-left": "7px" },
                onPrevClick: ye,
                onNextClick: ye
              }, {
                default: m(() => [
                  C("span", bl, x(f.value), 1),
                  L("/" + x(N.value), 1)
                ]),
                _: 1
              }, 8, ["total"])
            ])) : R("", !0)
          ]),
          _: 1
        }, 8, ["visible", "virtual-ref"]),
        v(Lt, {
          visible: P.value,
          "booking-info": B.value,
          loading: A.value,
          onConfirm: o[5] || (o[5] = (n) => Pe(B.value.bookingId)),
          onCancel: lt
        }, null, 8, ["visible", "booking-info", "loading"])
      ]);
    };
  }
}), yl = /* @__PURE__ */ ut(gl, [["__scopeId", "data-v-4e879401"]]), _l = { class: "booked-details" }, wl = { style: { "margin-left": "10px" } }, xl = { class: "instrument-name disable-text-select" }, Cl = { class: "instrument-code disable-text-select" }, El = { class: "instruments-divider" }, Sl = { class: "instrument-name-pop" }, Il = { class: "instrument-code-pop" }, Ml = ["src"], Dl = ["onClick"], $l = { style: { color: "#7366FF" } }, Tl = ["innerHTML"], Ll = {
  key: 0,
  class: "instruments-divider booking-info"
}, Bl = { class: "flex-space" }, zl = { class: "instrument-name-pop" }, Nl = { class: "top-icon" }, Pl = { class: "instrument-code-pop" }, Yl = { style: { display: "inline-block" } }, Vl = ["onClick"], Rl = { style: { color: "#7366FF" } }, Wl = /* @__PURE__ */ rt({
  __name: "bookedWeekDetailsTable",
  props: {
    weekDate: {
      type: Array,
      required: !0
    },
    onlyBooked: {
      type: Boolean,
      required: !0
    }
  },
  emits: ["bookedLoadingChange", "openEdit", "openCreate"],
  setup(fe, { expose: be, emit: ee }) {
    const T = w(!1), ae = w(), _ = w({}), f = w(1), N = w(0), P = w(!1), B = w(null), A = w(!1), Y = w(!1), F = w(), Z = w(), { t: S } = dt(), H = w(!1), ce = w(), se = w(1), G = w(/* @__PURE__ */ new Date()), pe = w(15), oe = w(0), ne = w(), O = w(/* @__PURE__ */ new Set());
    let re = -1;
    const te = w({});
    let ie = !1;
    const de = kt(/* @__PURE__ */ new Set()), ue = ee, I = st({
      forbid: "rgba(242, 242, 245, 0.5)",
      otherBooked: ["rgba(223, 223, 230, 0.5)", "rgb(223, 223, 230)"],
      myBooked: ["rgba(224, 224, 255, 0.5)", "rgb(224, 224, 255)"],
      white: ["#FFFFFF", "rgba(115, 102, 255, 0.15)"]
    }), D = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"], z = w(!1), j = (a) => ({
      mon: S("bookInstruments.monday"),
      tue: S("bookInstruments.tuesday"),
      wed: S("bookInstruments.wednesday"),
      thu: S("bookInstruments.thursday"),
      fri: S("bookInstruments.friday"),
      sat: S("bookInstruments.saturday"),
      sun: S("bookInstruments.sunday")
    })[a] || "", E = st([
      {
        prop: "mon0",
        label: j("mon"),
        key: 0,
        date: ""
      },
      {
        prop: "mon1",
        label: "",
        key: 0
      },
      {
        prop: "mon2",
        label: "",
        key: 0
      },
      {
        prop: "mon3",
        label: "",
        key: 0
      },
      {
        prop: "mon4",
        label: "",
        key: 0
      },
      {
        prop: "mon5",
        label: "",
        key: 0
      },
      {
        prop: "mon6",
        label: "",
        key: 0
      },
      {
        prop: "mon7",
        label: "",
        key: 0
      },
      {
        prop: "mon8",
        label: "",
        key: 0
      },
      {
        prop: "mon9",
        label: "",
        key: 0
      },
      {
        prop: "mon10",
        label: "",
        key: 0
      },
      {
        prop: "mon11",
        label: "",
        key: 0
      },
      {
        prop: "tue0",
        label: j("tue"),
        key: 0,
        date: ""
      },
      {
        prop: "tue1",
        label: "",
        key: 0
      },
      {
        prop: "tue2",
        label: "",
        key: 0
      },
      {
        prop: "tue3",
        label: "",
        key: 0
      },
      {
        prop: "tue4",
        label: "",
        key: 0
      },
      {
        prop: "tue5",
        label: "",
        key: 0
      },
      {
        prop: "tue6",
        label: "",
        key: 0
      },
      {
        prop: "tue7",
        label: "",
        key: 0
      },
      {
        prop: "tue8",
        label: "",
        key: 0
      },
      {
        prop: "tue9",
        label: "",
        key: 0
      },
      {
        prop: "tue10",
        label: "",
        key: 0
      },
      {
        prop: "tue11",
        label: "",
        key: 0
      },
      {
        prop: "wed0",
        label: j("wed"),
        key: 0,
        date: ""
      },
      {
        prop: "wed1",
        label: "",
        key: 0
      },
      {
        prop: "wed2",
        label: "",
        key: 0
      },
      {
        prop: "wed3",
        label: "",
        key: 0
      },
      {
        prop: "wed4",
        label: "",
        key: 0
      },
      {
        prop: "wed5",
        label: "",
        key: 0
      },
      {
        prop: "wed6",
        label: "",
        key: 0
      },
      {
        prop: "wed7",
        label: "",
        key: 0
      },
      {
        prop: "wed8",
        label: "",
        key: 0
      },
      {
        prop: "wed9",
        label: "",
        key: 0
      },
      {
        prop: "wed10",
        label: "",
        key: 0
      },
      {
        prop: "wed11",
        label: "",
        key: 0
      },
      {
        prop: "thu0",
        label: j("thu"),
        key: 0,
        date: ""
      },
      {
        prop: "thu1",
        label: "",
        key: 0
      },
      {
        prop: "thu2",
        label: "",
        key: 0
      },
      {
        prop: "thu3",
        label: "",
        key: 0
      },
      {
        prop: "thu4",
        label: "",
        key: 0
      },
      {
        prop: "thu5",
        label: "",
        key: 0
      },
      {
        prop: "thu6",
        label: "",
        key: 0
      },
      {
        prop: "thu7",
        label: "",
        key: 0
      },
      {
        prop: "thu8",
        label: "",
        key: 0
      },
      {
        prop: "thu9",
        label: "",
        key: 0
      },
      {
        prop: "thu10",
        label: "",
        key: 0
      },
      {
        prop: "thu11",
        label: "",
        key: 0
      },
      {
        prop: "fri0",
        label: j("fri"),
        key: 0,
        date: ""
      },
      {
        prop: "fri1",
        label: "",
        key: 0
      },
      {
        prop: "fri2",
        label: "",
        key: 0
      },
      {
        prop: "fri3",
        label: "",
        key: 0
      },
      {
        prop: "fri4",
        label: "",
        key: 0
      },
      {
        prop: "fri5",
        label: "",
        key: 0
      },
      {
        prop: "fri6",
        label: "",
        key: 0
      },
      {
        prop: "fri7",
        label: "",
        key: 0
      },
      {
        prop: "fri8",
        label: "",
        key: 0
      },
      {
        prop: "fri9",
        label: "",
        key: 0
      },
      {
        prop: "fri10",
        label: "",
        key: 0
      },
      {
        prop: "fri11",
        label: "",
        key: 0
      },
      {
        prop: "sat0",
        label: j("sat"),
        key: 0,
        date: ""
      },
      {
        prop: "sat1",
        label: "",
        key: 0
      },
      {
        prop: "sat2",
        label: "",
        key: 0
      },
      {
        prop: "sat3",
        label: "",
        key: 0
      },
      {
        prop: "sat4",
        label: "",
        key: 0
      },
      {
        prop: "sat5",
        label: "",
        key: 0
      },
      {
        prop: "sat6",
        label: "",
        key: 0
      },
      {
        prop: "sat7",
        label: "",
        key: 0
      },
      {
        prop: "sat8",
        label: "",
        key: 0
      },
      {
        prop: "sat9",
        label: "",
        key: 0
      },
      {
        prop: "sat10",
        label: "",
        key: 0
      },
      {
        prop: "sat11",
        label: "",
        key: 0
      },
      {
        prop: "sun0",
        label: j("sun"),
        key: 0,
        date: ""
      },
      {
        prop: "sun1",
        label: "",
        key: 0
      },
      {
        prop: "sun2",
        label: "",
        key: 0
      },
      {
        prop: "sun3",
        label: "",
        key: 0
      },
      {
        prop: "sun4",
        label: "",
        key: 0
      },
      {
        prop: "sun5",
        label: "",
        key: 0
      },
      {
        prop: "sun6",
        label: "",
        key: 0
      },
      {
        prop: "sun7",
        label: "",
        key: 0
      },
      {
        prop: "sun8",
        label: "",
        key: 0
      },
      {
        prop: "sun9",
        label: "",
        key: 0
      },
      {
        prop: "sun10",
        label: "",
        key: 0
      },
      {
        prop: "sun11",
        label: "",
        key: 0
      }
    ]), Q = w([]), le = fe, Se = (a) => {
      if (a.length <= 1) return a;
      a.sort((l, t) => l[0] - t[0]);
      const e = [a[0]];
      for (let l = 1; l < a.length; l++) {
        const t = a[l], s = e[e.length - 1];
        t[0] <= s[1] ? s[1] = Math.max(s[1], t[1]) : e.push(t);
      }
      return e;
    }, Je = (a) => {
      if (a.length <= 1) return a;
      a.sort((l, t) => l.size[0] - t.size[0]);
      const e = [a[0]];
      for (let l = 1; l < a.length; l++) {
        const t = a[l], s = e[e.length - 1];
        t.size[0] <= s.size[1] ? s.size[1] = Math.max(s.size[1], t.size[1]) : e.push(t);
      }
      return e;
    }, ve = async (a) => {
      H.value = !0, document.querySelectorAll(".el-table__cell").forEach((d) => {
        d.classList.toggle("selected-cell", !1);
      }), ue("bookedLoadingChange", H.value);
      const e = ce.value, t = await je.post("/?r=instrument-booking/get-instruments-data", {
        instrumentParam: e,
        bookTime: [le.weekDate[0], le.weekDate[6]],
        lang: window.lang,
        pageSize: pe.value,
        curPage: se.value,
        onlyBooked: a ? !le.onlyBooked : le.onlyBooked,
        headers: {
          Accept: "application/json",
          "X-Requested-With": "XMLHttpRequest"
        }
      });
      oe.value = t.data.data.totalCount, Q.value = t.data.data.instruments;
      let s = 0;
      Q.value.forEach((d) => {
        var g;
        if (d.cellMap = /* @__PURE__ */ new Map(), d.cellEvents = {}, d.rowIndex = s++, d.bookableTime && d.bookableTime.length > 0)
          d.bookableTime.forEach((u) => {
            Ze(u, d);
          });
        else
          for (let u = 0; u < 84; u++)
            d.cellEvents[E[u].prop] = {
              events: /* @__PURE__ */ new Set(),
              includedItems: [],
              bookedCellName: []
            }, d.cellEvents[E[u].prop].events.add("white"), d.cellMap.set(E[u].prop, {
              forbid: "0% 100%",
              white: [[0, 100]],
              booked: []
            });
        (g = d.bookedInfo) == null || g.forEach((u) => {
          const b = xe(new Date(u.bookedTime[0])), y = xe(new Date(u.bookedTime[1])), p = b < le.weekDate[0] ? le.weekDate[0] + " 00:00" : u.bookedTime[0], h = y > le.weekDate[6] ? le.weekDate[6] + " 23:59" : u.bookedTime[1];
          h > p && Ue([p, h], d, u);
        }), d.cellMap.forEach((u, b) => {
          u.booked = Je(u.booked), u.white = Se(u.white), De(u);
        });
      }), H.value = !1, ue("bookedLoadingChange", H.value), z.value || (z.value = !0, k("data15"));
    }, ye = (a) => {
      const e = a.target;
      !e.closest(".el-popover") && !e.closest(".cell-content") && !e.closest(".delete-booking-dialog") && (T.value = !1);
    }, Xe = (a) => {
      pe.value = a, ve(!1);
    }, Ie = (a) => {
      se.value = a, ve(!1);
    }, Me = (a) => {
      f.value = a;
    }, De = (a) => {
      var d, g, u, b;
      const e = [], l = ((g = (d = a.white) == null ? void 0 : d[0]) == null ? void 0 : g[0]) ?? 100, t = (b = (u = a.booked) == null ? void 0 : u[0]) == null ? void 0 : b.size[0], s = Math.min(l, t ?? 100);
      for (let y = 0; y < 2; y++) {
        s > 0 && e.push(I.forbid + " 0% " + s + "%"), t && l < t && e.push(I.white[0] + " " + l + "% " + t + "%");
        for (let p = 0; p < a.booked.length; p++) {
          const h = a.booked[p];
          e.push(I[h.color][y] + " " + h.size.join("% ") + "%"), p < a.booked.length - 1 && e.push(I.white[0] + " " + h.size[1] + "% " + a.booked[p + 1].size[0] + "%");
        }
        for (let p = 0; p < a.white.length; p++) {
          const h = a.white[p];
          e.push(I.white[0] + " " + h.join("% ") + "%"), p < a.white.length - 1 && e.push(I.forbid + " " + h[1] + "% " + a.white[p + 1][0] + "%");
        }
        e.push(I.forbid + " " + a.forbid), y === 0 ? (a.final = `linear-gradient(90deg, ${e.join(", ")})`, e.length = 0) : a.hoverFinal = `linear-gradient(90deg, ${e.join(", ")})`;
      }
    }, ke = (a, e) => {
      a.cellMap.set(e, {
        forbid: "0% 100%",
        white: [],
        booked: []
      }), a.cellEvents[e] = {
        events: /* @__PURE__ */ new Set(),
        includedItems: [],
        bookedCellName: []
      };
    }, Ze = (a, e) => {
      const l = a.split("-"), t = l[0], s = l[1], d = t.split(":").map((h) => Number(h)), g = s.split(":").map((h) => Number(h));
      d[0] += d[1] / 60, g[0] += g[1] / 60;
      const u = e.cellMap;
      let b, y, p;
      if (g[0] - d[0] < 2) {
        if (g[0] % 2 > d[0] % 2) {
          u.get("mon" + Math.floor(d[0] / 2)) || D.forEach((q) => {
            ke(e, q + Math.floor(d[0] / 2));
          });
          const M = Math.round(d[0] % 2 / 2 * 100), U = Math.round(g[0] % 2 / 2 * 100);
          D.forEach((q) => {
            e.cellEvents[q + Math.floor(d[0] / 2)].events.add("white"), u.get(q + Math.floor(d[0] / 2)).white.push([M, U]);
          });
        }
      } else {
        b = Math.floor(d[0] / 2), u.get("mon" + b) || D.forEach((h) => {
          ke(e, h + b);
        }), p = Math.round(d[0] % 2 / 2 * 100), p !== 100 && (p === 0 && D.forEach((h) => {
          e.cellEvents[h + b].events.add("white");
        }), D.forEach((h) => {
          u.get(h + b).white.push([p, 100]);
        })), y = Math.floor(g[0] / 2), u.get("mon" + y) || D.forEach((h) => {
          ke(e, h + y);
        }), p = Math.round(g[0] % 2 / 2 * 100), p !== 0 && (p === 100 && D.forEach((h) => {
          e.cellEvents[h + y].events.add("white");
        }), D.forEach((h) => {
          u.get(h + y).white.push([0, p]);
        }));
        for (let h = b + 1; h < y; h++)
          u.get("mon" + h) || D.forEach((M) => {
            ke(e, M + h);
          }), D.forEach((M) => {
            e.cellEvents[M + h].events.add("white"), u.get(M + h).white.push([0, 100]);
          });
      }
    }, $e = (a) => {
      let e = a.day();
      return e === 0 ? e = 6 : e--, e;
    }, Ue = (a, e, l) => {
      const t = Ee(a[0]), s = Ee(a[1]), d = s.diff(t, "minute"), g = $e(t), u = $e(s), b = e.cellMap, y = [], p = (t.hour() * 60 + t.minute()) / 60, h = (s.hour() * 60 + s.minute()) / 60, M = g * 12 + Math.floor(p / 2), U = u * 12 + Math.floor(h / 2), q = Math.round(p % 2 / 2 * 100), ot = Math.round(h % 2 / 2 * 100);
      if (b.get(E[M].prop) || ke(e, E[M].prop), d < 120 && h % 2 > p % 2) {
        e.cellEvents[E[M].prop].events.add("booked"), e.cellEvents[E[M].prop].includedItems.push(l), y.push(E[M].prop);
        const J = l.currentBooked ? "myBooked" : "otherBooked";
        b.get(E[M].prop).booked.push({
          color: J,
          size: [q, ot]
        });
      } else {
        if (q !== 100) {
          e.cellEvents[E[M].prop].events.add("booked"), e.cellEvents[E[M].prop].includedItems.push(l), y.push(E[M].prop);
          const J = l.currentBooked ? "myBooked" : "otherBooked";
          b.get(E[M].prop).booked.push({
            color: J,
            size: [q, 100]
          });
        }
        if (b.get(E[U].prop) || ke(e, E[U].prop), ot !== 0) {
          e.cellEvents[E[U].prop].events.add("booked"), e.cellEvents[E[U].prop].includedItems.push(l), y.push(E[U].prop);
          const J = l.currentBooked ? "myBooked" : "otherBooked";
          b.get(E[U].prop).booked.push({
            color: J,
            size: [0, ot]
          });
        }
        for (let J = M + 1; J < U; J++) {
          b.get(E[J].prop) || ke(e, E[J].prop), e.cellEvents[E[J].prop].events.add("booked"), e.cellEvents[E[J].prop].includedItems.push(l), y.push(E[J].prop);
          const Bt = l.currentBooked ? "myBooked" : "otherBooked";
          b.get(E[J].prop).booked.push({
            color: Bt,
            size: [0, 100]
          });
        }
        b.get(E[M + 1].prop).left = 95 - q, b.get(E[M + 1].prop).width = d / 120 * 35, d < 120 ? e[E[M + 1].prop] = "" : l.remark ? e[E[M + 1].prop] = l.experimenter + " " + l.timeShow + " " + S("bookInstruments.remark") + l.remark : e[E[M + 1].prop] = l.experimenter + " " + l.timeShow;
      }
      y.length > 0 && y.forEach((J) => {
        e.cellEvents[J].bookedCellName.push(y);
      });
    }, Ke = ({ column: a }) => {
      if (a.property === "instrumentName")
        return "clear-header-border";
      {
        const l = a.property.slice(3), t = Number(l);
        let s = "";
        return t === 0 ? s += "header-time-class" : s += "clear-border-right", s;
      }
    }, Ge = ({
      column: a,
      rowIndex: e
    }) => {
      let l = "";
      if (a.property !== "instrumentName") {
        l += " default-cell-color";
        const s = a.property.slice(3);
        Number(s) !== 11 && (l += " clear-border-right");
      }
      return l;
    }, Qe = ({
      row: a,
      column: e,
      rowIndex: l
    }) => {
      var g, u, b;
      const t = de.value, s = (g = a.cellMap) == null ? void 0 : g.get(e.property), d = {};
      return (b = (u = a.cellMap) == null ? void 0 : u.get(e.property)) != null && b.width && (d.zIndex = 1), s && ((l === re && t.has(e.property) ? 1 : 0) ? d.background = s.hoverFinal : d.background = s.final), d;
    }, et = (a, e, l) => {
      var s, d;
      const t = (d = (s = a.cellEvents) == null ? void 0 : s[e]) == null ? void 0 : d.includedItems;
      l.stopPropagation(), _.value = t ?? [], N.value = t ? t.length : 0, f.value = 1, t && t.length > 0 ? (te.value = {
        instrumentName: a.instrumentName,
        instrumentId: a.instrumentId,
        available_slots: a.available_slots,
        max_advance_day: a.max_advance_day,
        min_advance: a.min_advance,
        max_booking_duration: a.max_booking_duration
      }, ae.value = l.target, T.value = !0, Y.value = !1) : (te.value = {}, T.value = !1);
    }, tt = (a, e, l) => {
      var d, g, u, b, y, p, h, M;
      re = a.rowIndex;
      const t = (g = (d = a.cellEvents) == null ? void 0 : d[e.property]) == null ? void 0 : g.events;
      if (t && t.values().next().value === "white" && t.size === 1) {
        l.style.background = I.white[1];
        return;
      }
      (b = (u = a.cellEvents) == null ? void 0 : u[e.property]) != null && b.bookedCellName && ((p = (y = a.cellEvents) == null ? void 0 : y[e.property]) == null ? void 0 : p.bookedCellName.length) > 0 && (de.value = new Set(a.cellEvents[e.property].bookedCellName.flat(2)));
      const s = (M = (h = a.cellEvents) == null ? void 0 : h[e.property]) == null ? void 0 : M.includedItems;
      if ((s == null ? void 0 : s.length) > 0) {
        let U = "";
        s == null || s.forEach((q) => {
          q.remark ? U += q.experimenter + " " + q.timeShow + " " + S("bookInstruments.remark") + q.remark + "<br/>" : U += q.experimenter + " " + q.timeShow + "<br/>";
        }), Z.value = U, F.value = l, Y.value = !0;
      } else
        Y.value = !1;
    }, _e = (a, e, l) => {
      re = -1, l.style.background === I.white[1] && (l.style.background = I.white[0]), de.value.size > 0 && (de.value = /* @__PURE__ */ new Set()), Y.value = !1;
    }, Te = (a) => {
      var t;
      const e = te.value, l = {
        name: e.instrumentName,
        instrument_id: e.instrumentId,
        id: a.bookingId,
        start_time: a.bookedTime[0],
        end_time: a.bookedTime[1],
        source: "bookInstruments",
        warn: a.warn,
        related_experiment: (t = a.ELNPage) == null ? void 0 : t.join(","),
        remark: a.remark,
        available_slots: Array.isArray(e.available_slots) ? e.available_slots : JSON.parse(e.available_slots || "[]"),
        max_advance_day: e.max_advance_day,
        min_advance: JSON.parse(e.min_advance || '{"value":"","unit":""}'),
        max_booking_duration: JSON.parse(e.max_booking_duration || '{"value":"","unit":""}')
      };
      ue("openEdit", l);
    }, Le = (a, e) => {
      var l, t, s, d;
      if ((t = (l = a.cellMap) == null ? void 0 : l.get(e.prop)) != null && t.width) {
        const g = (s = a.cellMap.get(e.prop)) == null ? void 0 : s.width, u = -((d = a.cellMap.get(e.prop)) == null ? void 0 : d.left);
        return {
          width: g + "px",
          left: u + "%"
        };
      }
      return {};
    }, Be = {
      mounted(a) {
        let e = !1, l = null;
        a.addEventListener("mousedown", (d) => {
          document.querySelectorAll(".el-table__cell").forEach((u) => {
            u.classList.toggle("selected-cell", !1);
          });
          const g = d.target.closest(".el-table__cell");
          if (g) {
            if (l = me(g), O.value.has(`${l.rowIndex}-${l.colIndex}`)) {
              const b = Array.from(O.value).map((q) => parseInt(q.split("-")[1])), y = Math.min(...b), p = Math.max(...b), h = [Ne(y - 1), Ne(p)], M = Q.value[l.rowIndex], U = {
                name: M.instrumentName,
                id: M.instrumentId,
                time: h,
                related_experiment: "",
                remark: "",
                source: "bookInstruments",
                available_slots: Array.isArray(M.available_slots) ? M.available_slots : JSON.parse(M.available_slots || "[]"),
                max_advance_day: M.max_advance_day,
                min_advance: JSON.parse(M.min_advance || '{"value":"","unit":""}'),
                max_booking_duration: JSON.parse(M.max_booking_duration || '{"value":"","unit":""}')
              };
              ue("openCreate", U), O.value.clear();
              return;
            } else
              O.value.clear();
            e = !0;
            const u = we(l, l);
            ze(u, l.rowIndex), a.addEventListener("mousemove", t), a.addEventListener("mouseup", s);
          } else
            O.value.clear();
        });
        const t = (d) => {
          var u;
          if (!e) return;
          const g = document.elementFromPoint(d.clientX, d.clientY);
          if (g.closest(".el-table__row") && (g.closest(".el-table__row").style.cursor = "e-resize"), g.closest("tr")) {
            const b = me(g), y = Q.value[l.rowIndex].cellEvents;
            if ((u = y == null ? void 0 : y[E[b.colIndex - 1].prop]) != null && u.events.has("booked") && (he({
              message: S("bookInstruments.bookingConflict"),
              grouping: !0,
              type: "warning"
            }), s()), l && b) {
              const p = we(l, b);
              ze(p, l.rowIndex);
            }
          }
          d.stopPropagation();
        }, s = () => {
          e = !1, l = null, document.querySelectorAll(".el-table__row").forEach((d) => {
            d.style.cursor = "default";
          }), a.removeEventListener("mousemove", t), a.removeEventListener("mouseup", s);
        };
      }
    }, me = (a) => {
      const e = a.closest("tr").rowIndex, l = Array.from(a.closest("tr").children).indexOf(a.closest("td"));
      return {
        rowIndex: e,
        colIndex: l
      };
    }, we = (a, e) => {
      const l = a.rowIndex, t = Math.min(a.colIndex, e.colIndex), s = Math.max(a.colIndex, e.colIndex), d = [];
      for (let g = t; g <= s; g++)
        d.push(`${l}-${g}`);
      return d;
    }, ze = (a, e) => {
      var d, g, u;
      const l = Q.value[e].cellEvents, t = (d = ne.value) == null ? void 0 : d.$el;
      if (!t) return;
      const s = t.querySelectorAll(".el-table__row")[e];
      for (let b = 0; b < s.querySelectorAll(".el-table__cell").length - 1; b++) {
        const y = s.querySelectorAll(".el-table__cell")[b + 1];
        if (((g = l == null ? void 0 : l[E[b].prop]) == null ? void 0 : g.events.size) === 1 && ((u = l == null ? void 0 : l[E[b].prop]) == null ? void 0 : u.events.values().next().value) === "white") {
          const p = me(y), h = a.includes(`${p.rowIndex}-${p.colIndex}`);
          y.classList.toggle("selected-cell", h), h && O.value.add(`${p.rowIndex}-${p.colIndex}`);
        }
      }
    }, Ne = (a) => {
      const e = Math.floor(a / 12), l = (a % 12 * 2).toString().padStart(2, "0");
      return `${le.weekDate[e]} ${l}:00:00`;
    }, xe = (a) => {
      const e = a.getFullYear(), l = String(a.getMonth() + 1).padStart(2, "0"), t = String(a.getDate()).padStart(2, "0");
      return `${e}-${l}-${t}`;
    }, Pe = (a, e) => {
      if (!le.weekDate.includes(xe(G.value)))
        return !1;
      const l = G.value.getHours(), t = G.value.getDay() - 1, s = Math.floor(l / 2) + t * 12 + e;
      if (e) {
        if (l < 2)
          return a === E[s - 1].prop;
        if (l >= 2 && l < 4)
          return a === E[s - 2].prop;
      }
      return a === E[s].prop;
    }, lt = (a) => {
      const e = G.value.getHours(), l = G.value.getMinutes(), t = e % 2 / 2 + l / 120, s = G.value.getDay() - 1, d = Math.floor(e / 2) + s * 12 + 1;
      if (e < 2) {
        if (a === E[d - 1].prop)
          return {
            left: `calc(-20% + ${t * 100}% - 3.5px)`,
            bottom: "-12px"
          };
      } else if (e >= 2 && e < 4) {
        if (a === E[d - 2].prop)
          return {
            left: `calc(80% + ${t * 100}% - 3.5px)`,
            bottom: "-12px"
          };
      } else if (a === E[d].prop)
        return e >= 22 && e < 24 ? {
          left: `calc( -120% + ${t * 100}% - 3.5px)`,
          bottom: "-12px"
        } : {
          left: `calc( -100% + ${t * 100}% - 3.5px)`
        };
      return "";
    }, at = Ce(() => {
      const a = G.value.getHours(), e = G.value.getMinutes();
      return {
        left: `${(a % 2 / 2 + e / 120) * 100}%`
      };
    }), r = (a) => {
      ie = !1, B.value = a, P.value = !0;
    }, o = async (a) => {
      A.value = !0;
      try {
        (await je.post("/?r=instrument-booking/delete-instrument-booking", {
          id: a,
          headers: {
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        })).data.status === 1 ? (he({
          message: S("bookInstruments.deleteSuccess"),
          grouping: !0,
          type: "success"
        }), ie = !0, ve(!1)) : he({
          message: S("bookInstruments.deleteError"),
          grouping: !0,
          type: "error"
        });
      } catch {
        he({
          message: S("bookInstruments.deleteError"),
          grouping: !0,
          type: "error"
        });
      } finally {
        A.value = !1, P.value = !1;
      }
    }, c = () => {
      T.value = !ie, P.value = !1;
    }, n = (a) => {
      window.open("https://idataeln.integle.com/?exp_id=" + a, "_blank");
    }, k = (a) => {
      _t(() => {
        if (!ne.value) return;
        const e = E.findIndex((s) => s.prop === a);
        if (e === -1) return;
        const t = e * 60;
        setTimeout(() => {
          ne.value && ne.value.setScrollLeft(Math.max(0, t));
        }, 100);
      });
    };
    return ht(
      () => le.weekDate,
      (a, e) => {
        let l = 0;
        a.forEach((t) => {
          E[l].date = t.slice(-2), l += 12;
        }), ve(!1);
      },
      {
        deep: !0,
        immediate: !0
      }
    ), be({
      handlePrefixClick: ve
    }), it(() => {
      document.addEventListener("click", ye);
    }), bt(() => {
      document.removeEventListener("click", ye);
    }), (a, e) => {
      const l = Tt;
      return $(), K("div", _l, [
        gt(($(), W(i(wt), {
          data: Q.value,
          border: "",
          "element-loading-custom-class": "custom-loading",
          "element-loading-background": "rgba(255, 255, 255, 0.8)",
          style: { width: "calc(100vw - 72px)", position: "relative" },
          "header-cell-class-name": Ke,
          "cell-class-name": Ge,
          ref_key: "bookingDetailTable",
          ref: ne,
          "cell-style": Qe,
          "header-row-class-name": "clear-header-border",
          onCellMouseEnter: tt,
          onCellMouseLeave: _e,
          "row-height": 100
        }, {
          default: m(() => [
            v(i(Ae), {
              fixed: "",
              prop: "instrumentName",
              width: "270"
            }, {
              header: m(() => [
                v(i(xt), {
                  modelValue: ce.value,
                  "onUpdate:modelValue": e[1] || (e[1] = (t) => ce.value = t),
                  placeholder: i(S)("bookInstruments.searchInstrument"),
                  clearable: "",
                  class: "search-instruments",
                  onKeyup: e[2] || (e[2] = yt((t) => ve(!1), ["enter"]))
                }, {
                  prefix: m(() => [
                    v(i(X), {
                      onClick: e[0] || (e[0] = (t) => ve(!1))
                    }, {
                      default: m(() => [
                        v(i(Et))
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["modelValue", "placeholder"])
              ]),
              default: m((t) => [
                v(i(Fe), {
                  class: "box-item",
                  placement: "right",
                  transition: "el-zoom-in-left",
                  "show-arrow": !1,
                  width: "352"
                }, {
                  reference: m(() => [
                    C("div", wl, [
                      C("div", xl, x(t.row.instrumentName), 1),
                      C("div", Cl, x(t.row.instrumentCode), 1)
                    ])
                  ]),
                  default: m(() => [
                    C("div", El, [
                      C("div", Sl, x(t.row.instrumentName), 1),
                      C("div", Il, x(t.row.instrumentCode), 1),
                      v(i(qe)),
                      v(i(He), { column: 1 }, {
                        default: m(() => [
                          v(i(V), {
                            label: i(S)("bookInstruments.state"),
                            "class-name": "popover-state-class"
                          }, {
                            default: m(() => [
                              L(x(t.row.state), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.responsible")
                          }, {
                            default: m(() => [
                              L(x(t.row.responsiblePerson), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.position"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(t.row.position), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.bookableTime"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(t.row.bookableTime.join(",")), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.singleTimeLimit"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(t.row.singleTimeLimit), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"]),
                          v(i(V), {
                            label: i(S)("bookInstruments.advance"),
                            span: 2
                          }, {
                            default: m(() => [
                              L(x(t.row.advance), 1)
                            ]),
                            _: 2
                          }, 1032, ["label"])
                        ]),
                        _: 2
                      }, 1024)
                    ]),
                    C("img", {
                      src: t.row.picture
                    }, null, 8, Ml)
                  ]),
                  _: 2
                }, 1024)
              ]),
              _: 1
            }),
            ($(!0), K(Re, null, We(E, (t) => ($(), W(i(Ae), {
              key: t.key,
              prop: t.prop,
              label: t.label,
              width: "35",
              resizable: !1
            }, {
              header: m(() => [
                C("div", null, x(t.date ?? "") + " " + x(t.label), 1),
                Pe(t.prop, 1) ? ($(), K("div", {
                  key: 0,
                  class: "red-dot",
                  style: ge(lt(t.prop))
                }, null, 4)) : R("", !0)
              ]),
              default: m(({ row: s }) => [
                C("div", {
                  class: "blankCell",
                  onClick: (d) => et(s, t.prop, d)
                }, [
                  C("div", {
                    class: "cell-tips disable-text-select",
                    style: ge(Le(s, t))
                  }, x(s[t.prop]), 5),
                  Pe(t.prop, 0) ? ($(), K("div", {
                    key: 0,
                    class: "red-timeline-offset red-timeline",
                    style: ge(at.value)
                  }, null, 4)) : R("", !0)
                ], 8, Dl)
              ]),
              _: 2
            }, 1032, ["prop", "label"]))), 128))
          ]),
          _: 1
        }, 8, ["data"])), [
          [l, H.value],
          [Be]
        ]),
        v(i(Oe), {
          "page-sizes": [10, 15, 25, 50],
          "page-size": pe.value,
          total: oe.value,
          layout: "sizes, prev, slot, next, total",
          "pager-count": 2,
          style: { "margin-left": "7px" },
          onSizeChange: Xe,
          onPrevClick: Ie,
          onNextClick: Ie
        }, {
          default: m(() => [
            C("span", $l, x(se.value), 1),
            L("/" + x(Math.ceil(oe.value / pe.value)), 1)
          ]),
          _: 1
        }, 8, ["page-size", "total"]),
        v(i(Ct), {
          class: "box-item",
          effect: "dark",
          visible: Y.value,
          placement: "top",
          trigger: "hover",
          "virtual-triggering": "",
          "virtual-ref": F.value
        }, {
          content: m(() => [
            C("div", { innerHTML: Z.value }, null, 8, Tl)
          ]),
          _: 1
        }, 8, ["visible", "virtual-ref"]),
        v(i(Fe), {
          visible: T.value,
          width: 400,
          trigger: "click",
          placement: "right",
          "virtual-ref": ae.value,
          "virtual-triggering": "",
          "show-arrow": !1
        }, {
          default: m(() => [
            _.value[f.value - 1] ? ($(), K("div", Ll, [
              C("div", Bl, [
                C("div", zl, x(_.value[f.value - 1].experimenter) + x(i(S)("bookInstruments.bookingOf")), 1),
                C("div", Nl, [
                  _.value[f.value - 1].updateShow ? ($(), W(i(X), { key: 0 }, {
                    default: m(() => [
                      v(i(St), {
                        onClick: e[3] || (e[3] = (t) => Te(_.value[f.value - 1]))
                      })
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].deleteShow ? ($(), W(i(X), { key: 1 }, {
                    default: m(() => [
                      v(i(It), {
                        onClick: e[4] || (e[4] = (t) => r(_.value[f.value - 1]))
                      })
                    ]),
                    _: 1
                  })) : R("", !0)
                ])
              ]),
              C("div", Pl, x(_.value[f.value - 1].instrument), 1),
              v(i(qe)),
              v(i(He), { column: 1 }, {
                default: m(() => [
                  _.value[f.value - 1].bookedTime ? ($(), W(i(V), { key: 0 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i(Mt))
                        ]),
                        _: 1
                      }),
                      L(" " + x(_.value[f.value - 1].timeShow), 1)
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].reminder ? ($(), W(i(V), { key: 1 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i(Dt))
                        ]),
                        _: 1
                      }),
                      L(" " + x(_.value[f.value - 1].reminder), 1)
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].ELNPage ? ($(), W(i(V), { key: 2 }, {
                    default: m(() => [
                      v(i(X), { class: "icon-align" }, {
                        default: m(() => [
                          v(i($t))
                        ]),
                        _: 1
                      }),
                      C("div", Yl, [
                        ($(!0), K(Re, null, We(_.value[f.value - 1].ELNPage, (t) => ($(), K("div", {
                          class: "eln-page",
                          key: t,
                          onClick: (s) => n(t)
                        }, x(t), 9, Vl))), 128))
                      ])
                    ]),
                    _: 1
                  })) : R("", !0),
                  _.value[f.value - 1].remark ? ($(), W(i(V), { key: 3 }, {
                    default: m(() => {
                      var t;
                      return [
                        e[6] || (e[6] = C("span", { class: "icon-align" }, [
                          C("svg", {
                            width: "13",
                            height: "13",
                            viewBox: "0 0 13.2 13.2",
                            fill: "none",
                            xmlns: "http://www.w3.org/2000/svg",
                            "xmlns:xlink": "http://www.w3.org/1999/xlink"
                          }, [
                            C("defs"),
                            C("path", {
                              id: "路径（边框）",
                              d: "M6.6 0C9.33 0 11.26 1.93 11.26 1.93C13.2 3.86 13.2 6.59 13.2 6.59C13.2 9.33 11.26 11.26 11.26 11.26C9.33 13.2 6.6 13.2 6.6 13.2C3.86 13.2 1.93 11.26 1.93 11.26C0 9.33 0 6.59 0 6.59C0 3.86 1.93 1.93 1.93 1.93C3.86 0 6.6 0 6.6 0ZM2.78 2.78C2.78 2.78 4.36 1.19 6.6 1.19C6.6 1.19 8.83 1.19 10.41 2.78C10.41 2.78 12 4.36 12 6.59C12 6.59 12 8.83 10.41 10.41C10.41 10.41 8.83 12 6.6 12C6.6 12 4.36 12 2.78 10.41C2.78 10.41 1.19 8.83 1.19 6.59C1.19 6.59 1.19 4.36 2.78 2.78ZM6.6 3.6C6.27 3.6 6 3.86 6 4.2C6 4.53 6.26 4.8 6.6 4.8C6.93 4.8 7.2 4.53 7.2 4.2C7.2 4.04 7.14 3.88 7.03 3.77C6.91 3.66 6.76 3.6 6.6 3.6ZM6 6.59C6 6.26 6.26 6 6.6 6C6.93 6 7.2 6.26 7.2 6.59L7.2 8.99C7.2 9.33 6.93 9.59 6.6 9.59C6.26 9.59 6 9.33 6 8.99L6 6.59Z",
                              fill: "#B6B6BF",
                              "fill-opacity": "1.000000",
                              "fill-rule": "evenodd"
                            })
                          ])
                        ], -1)),
                        L(" " + x((t = _.value[f.value - 1]) == null ? void 0 : t.remark), 1)
                      ];
                    }),
                    _: 1
                  })) : R("", !0)
                ]),
                _: 1
              }),
              v(i(Oe), {
                "page-size": 1,
                total: N.value,
                layout: "prev, slot, next",
                style: { "margin-left": "7px" },
                onPrevClick: Me,
                onNextClick: Me
              }, {
                default: m(() => [
                  C("span", Rl, x(f.value), 1),
                  L("/" + x(N.value), 1)
                ]),
                _: 1
              }, 8, ["total"])
            ])) : R("", !0)
          ]),
          _: 1
        }, 8, ["visible", "virtual-ref"]),
        v(Lt, {
          visible: P.value,
          "booking-info": B.value,
          loading: A.value,
          onConfirm: e[5] || (e[5] = (t) => o(B.value.bookingId)),
          onCancel: c
        }, null, 8, ["visible", "booking-info", "loading"])
      ]);
    };
  }
}), Al = /* @__PURE__ */ ut(Wl, [["__scopeId", "data-v-82a3fa6e"]]), Fl = { class: "book-instruments" }, ql = { class: "my-book-header" }, Hl = { class: "book-title" }, Ol = { class: "left-header" }, jl = { class: "time-type" }, Jl = { class: "custom-date-picker" }, Xl = { class: "custom-week-picker" }, Zl = { class: "time-type quickTimePick" }, Ul = { class: "header-tail" }, Kl = { class: "booked-instruments" }, Gl = /* @__PURE__ */ rt({
  __name: "BookInstruments",
  props: {
    closeBookInstruments: {
      type: Function,
      required: !0
    }
  },
  setup(fe) {
    const be = Ce(() => (zt.en.weekStart = window.lang === "cn" ? 1 : 0, window.lang === "cn" ? jt : Ot)), ee = Ce(() => window.lang === "cn" ? "108px" : "145px");
    Ee.extend(Qt), Ee.locale(window.lang === "cn" ? "zh-cn" : "en");
    const { t: T } = dt(), ae = w(!0), _ = w("day"), f = w(/* @__PURE__ */ new Date()), N = w(""), P = w(""), B = w(!1), A = w(!1), Y = w([]), F = w(), Z = w(), S = w(), H = fe, ce = () => {
      document.body.classList.remove("dialog-body-hidden"), H.closeBookInstruments();
    }, se = (I) => {
      _.value = I, I === "week" && O(f.value);
    }, G = Ce(() => [
      {
        text: T("bookInstruments.today"),
        value: /* @__PURE__ */ new Date()
      },
      {
        text: T("bookInstruments.yesterday"),
        value: () => {
          const I = /* @__PURE__ */ new Date();
          return I.setTime(I.getTime() - 3600 * 1e3 * 24), I;
        }
      },
      {
        text: T("bookInstruments.weekAgo"),
        value: () => {
          const I = /* @__PURE__ */ new Date();
          return I.setTime(I.getTime() - 3600 * 1e3 * 24 * 7), I;
        }
      }
    ]), pe = () => {
      _.value === "day" ? f.value = new Date(f.value.getTime() - 3600 * 1e3 * 24) : _.value === "week" && O(new Date(f.value.getTime() - 3600 * 1e3 * 24 * 7));
    }, oe = () => {
      _.value === "day" ? f.value = new Date(f.value.getTime() + 3600 * 1e3 * 24) : _.value === "week" && O(new Date(f.value.getTime() + 3600 * 1e3 * 24 * 7));
    }, ne = () => {
      _.value === "day" ? f.value = /* @__PURE__ */ new Date() : _.value === "week" && O(/* @__PURE__ */ new Date());
    }, O = (I) => {
      if (!I) return;
      const D = Ee(I), z = D.startOf("isoWeek"), j = D.endOf("isoWeek");
      N.value = z.format("YYYY-MM-DD"), P.value = j.format("YYYY-MM-DD"), f.value = z.toDate();
      for (let E = 0; E < 7; E++)
        Y.value[E] = z.add(E, "day").format("YYYY-MM-DD");
    }, re = () => new Promise((I) => {
      _.value === "day" && F.value ? F.value.handlePrefixClick(!0) : _.value === "week" && Z.value && Z.value.handlePrefixClick(!0), I(!0);
    }), te = (I) => {
      A.value = I;
    }, ie = (I) => {
      S.value.openDialogCreate(I);
    }, de = () => {
      _.value === "day" && F.value ? F.value.handlePrefixClick(!1) : _.value === "week" && Z.value && Z.value.handlePrefixClick(!1);
    }, ue = (I) => {
      S.value.openDialogEdit(I);
    };
    return it(() => {
      document.body.classList.add("dialog-body-hidden");
    }), (I, D) => ($(), K("div", Fl, [
      v(i(Nt), { locale: be.value }, {
        default: m(() => [
          v(i(Pt), {
            modelValue: ae.value,
            "onUpdate:modelValue": D[5] || (D[5] = (z) => ae.value = z),
            fullscreen: "",
            top: "40vh",
            width: "70%",
            draggable: "",
            "append-to-body": !1,
            "show-close": !1,
            onClose: ce
          }, {
            header: m(({ close: z }) => [
              C("div", ql, [
                v(i(nt), {
                  key: "exit",
                  text: "",
                  onClick: z
                }, {
                  default: m(() => [
                    v(i(X), null, {
                      default: m(() => [
                        v(i(Ft))
                      ]),
                      _: 1
                    }),
                    L("  " + x(i(T)("bookInstruments.exit")), 1)
                  ]),
                  _: 2
                }, 1032, ["onClick"]),
                C("span", Hl, x(i(T)("bookInstruments.title")), 1)
              ])
            ]),
            default: m(() => [
              v(i(ct), null, {
                default: m(() => [
                  C("div", Ol, [
                    C("div", jl, [
                      v(i(Yt), {
                        modelValue: _.value,
                        "onUpdate:modelValue": D[0] || (D[0] = (z) => _.value = z),
                        onChange: se,
                        style: ge({ width: ee.value })
                      }, {
                        default: m(() => [
                          v(i(pt), { label: "day" }, {
                            default: m(() => [
                              L(x(I.$t("bookInstruments.dayAbbr")), 1)
                            ]),
                            _: 1
                          }),
                          v(i(pt), { label: "week" }, {
                            default: m(() => [
                              L(x(I.$t("bookInstruments.weekAbbr")), 1)
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }, 8, ["modelValue", "style"])
                    ]),
                    C("div", Jl, [
                      _.value === "day" ? ($(), W(i(vt), {
                        key: 0,
                        modelValue: f.value,
                        "onUpdate:modelValue": D[1] || (D[1] = (z) => f.value = z),
                        "default-value": /* @__PURE__ */ new Date(),
                        type: "date",
                        "popper-class": "booking-header-date-picker",
                        shortcuts: G.value,
                        size: "default",
                        clearable: !1,
                        "append-to-body": !1,
                        editable: !1,
                        format: i(T)("bookInstruments.dateFormat"),
                        class: "readonly-datepicker"
                      }, null, 8, ["modelValue", "default-value", "shortcuts", "format"])) : R("", !0),
                      C("div", Xl, [
                        _.value === "week" ? ($(), W(i(vt), {
                          key: 0,
                          modelValue: f.value,
                          "onUpdate:modelValue": D[2] || (D[2] = (z) => f.value = z),
                          type: "week",
                          "popper-class": "booking-header-date-picker",
                          "first-day-of-week": 1,
                          clearable: !1,
                          editable: !1,
                          format: `${N.value} - ${P.value}`,
                          onChange: O
                        }, null, 8, ["modelValue", "format"])) : R("", !0)
                      ])
                    ]),
                    C("div", Zl, [
                      v(i(X), {
                        class: "pointer-style",
                        onClick: pe
                      }, {
                        default: m(() => [
                          v(i(Rt))
                        ]),
                        _: 1
                      }),
                      v(i(X), {
                        class: "pointer-style",
                        onClick: oe
                      }, {
                        default: m(() => [
                          v(i(Wt))
                        ]),
                        _: 1
                      }),
                      v(i(nt), {
                        class: "today-button",
                        onClick: ne
                      }, {
                        default: m(() => [
                          L(x(i(T)("bookInstruments.today")), 1)
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  C("div", Ul, [
                    C("div", Kl, [
                      v(i(Vt), {
                        modelValue: B.value,
                        "onUpdate:modelValue": D[3] || (D[3] = (z) => B.value = z),
                        size: "small",
                        loading: A.value,
                        "before-change": re
                      }, null, 8, ["modelValue", "loading"]),
                      C("span", null, x(i(T)("bookInstruments.onlyShowMine")), 1)
                    ]),
                    v(i(nt), {
                      type: "primary",
                      class: "create-book",
                      loading: A.value,
                      onClick: D[4] || (D[4] = (z) => ie({ source: "bookInstruments" }))
                    }, {
                      default: m(() => [
                        v(i(X), null, {
                          default: m(() => [
                            v(i(At))
                          ]),
                          _: 1
                        }),
                        L(" " + x(i(T)("bookInstruments.createBooking")), 1)
                      ]),
                      _: 1
                    }, 8, ["loading"])
                  ])
                ]),
                _: 1
              }),
              v(i(ct), null, {
                default: m(() => [
                  _.value === "day" ? ($(), W(yl, {
                    key: 0,
                    bookTime: f.value,
                    onlyBooked: B.value,
                    ref_key: "dayTableRef",
                    ref: F,
                    onBookedLoadingChange: te,
                    onOpenEdit: ue,
                    onOpenCreate: ie
                  }, null, 8, ["bookTime", "onlyBooked"])) : ($(), W(Al, {
                    key: 1,
                    weekDate: Y.value,
                    onlyBooked: B.value,
                    ref_key: "weekTableRef",
                    ref: Z,
                    onBookedLoadingChange: te,
                    onOpenEdit: ue,
                    onOpenCreate: ie
                  }, null, 8, ["weekDate", "onlyBooked"]))
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["modelValue"]),
          v(Jt, {
            ref_key: "createBookingDialogRef",
            ref: S,
            onCloseDialog: de
          }, null, 512)
        ]),
        _: 1
      }, 8, ["locale"])
    ]));
  }
}), da = /* @__PURE__ */ ut(Gl, [["__scopeId", "data-v-e2a1d54d"]]);
export {
  da as default
};
