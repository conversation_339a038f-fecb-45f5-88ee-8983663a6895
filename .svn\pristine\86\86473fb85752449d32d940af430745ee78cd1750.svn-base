-- 仪器预约
-- 仪器表的行字段过长，转换utf8mb4为utf8mb3
ALTER TABLE `instruments`
CONVERT TO  CHARACTER SET utf8 
COLLATE utf8_general_ci;
ADD COLUMN `max_advance_day` int(4) DEFAULT NULL COMMENT '最多提前预约 单位天 \r\n	NULL	允许无限提前预约，无需校验 预约时间-当前时间 的差值\r\n	N (正整数)	校验逻辑：预约开始时间 - 当前时间 <= N天，否则禁止预约';

ALTER TABLE `instruments` 
ADD COLUMN  `min_advance` varchar(255) DEFAULT NULL COMMENT '至少提前预约 NULL	允许随时预约，不显示前端输入框\r\n	{"value":2,"unit":"hours"}	校验逻辑：预约开始时间 - 当前时间 >= 2小时 否则禁止预约';

ALTER TABLE `instruments` 
ADD COLUMN  `max_booking_duration` varchar(255) DEFAULT NULL COMMENT '单次预约限制 ​​默认值​​: NULL（表示“不限制”）\r\n​​自定义校验规则​​:\r\n若字段非空，必须包含 value（正整数）和 unit（时间单位）\r\n时间单位仅允许 minutes/hours/days\r\n {\r\n  "value": 2,\r\n  "unit": "hours"  // 允许值：minutes/hours/days\r\n}';


ALTER TABLE `instruments_book` 
ADD COLUMN  `reminder` int(2) DEFAULT NULL COMMENT '提前提醒时间 0 不提醒 1 5m 2 15m 3 30m 4 1h 5 2h 6 1d';

-- 模板
-- 1. 模板表字段拆分
CREATE TABLE `template_intext_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一自增主键',
  `template_id` int(11) NOT NULL COMMENT '关联模板id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '子模板内容',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1043 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文本模板长文本内容表' ROW_FORMAT = COMPACT;

-- 历史数据插入 文本方法模板适用
INSERT INTO template_intext_data(template_id, content)
SELECT id, content FROM template where type = 2;


CREATE TABLE `template_intable_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一自增主键',
  `template_id` int(11) NOT NULL COMMENT '关联模板id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '子模板内容',
  `img` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '模板预览图',
  `history_data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '历史数据',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 157 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '表格模板长文本内容表' ROW_FORMAT = COMPACT;

-- 历史数据插入 表格模板才插入
INSERT INTO template_intable_data(template_id,content,img,history_data)
SELECT id,content, img,history_data FROM template where type = 4;

-- 2. 模板全局审批项数据新增
INSERT INTO `company_setting` (
  `key`, `value`, `remark`, `status`, `company_id`
) VALUES (
  'TEMPLATE_EFFECT_MODE',
  '1',
  '模板生效模式开关项：1为模板保存立即生效，2为需要发布才生效',
  1,
  1
);

-- 3. 模板子分类表
CREATE TABLE `template_subtype`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一自增主键',
  `main_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 全局模板 2子模板 3默认模板 4表格模板',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板子类型名称 100字以内',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` int(11) NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0未启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '模板子分类表' ROW_FORMAT = COMPACT;


-- 模板表新增子分类字段  如果无法新增字段，先把 template表中的 content字段改成 text类型
ALTER TABLE `template`
ADD COLUMN `subtype_id` INT(11) NOT NULL DEFAULT 0;


-- 4. 模板历史表和模板历史依赖表
CREATE TABLE `template_history_new`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NULL DEFAULT NULL COMMENT '模板id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '名称',
  `descript` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '描述',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '标题',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '关键字',
  `define_item` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '自定义项',
  `subtype_id` int(11) NULL DEFAULT NULL COMMENT '模板子类型',
  `action_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '产生这条痕迹的动作',
  `action_id` int(11) NULL DEFAULT NULL COMMENT '产生这条痕迹的动作ID',
  `inner_version` int(11) NULL DEFAULT NULL COMMENT '内部版本号',
  `user_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户自定义版本号',
  `create_by` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `actual_effect_time` datetime NULL DEFAULT NULL COMMENT '实际生效日期',
  `status` tinyint(4) NULL DEFAULT 1,
  `effect_date` datetime NULL DEFAULT NULL COMMENT '生效日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for template_history_relay
-- ----------------------------
CREATE TABLE `template_history_relay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `history_id` int(11) NULL DEFAULT NULL COMMENT '模板的痕迹id',
  `component_id` tinyint(11) NULL DEFAULT NULL COMMENT '模块类型',
  `component_type` tinyint(11) NULL DEFAULT NULL COMMENT '自定义表格类型：1代表自定义表格;2代表材料仪器;3代表参考文献',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '模块名称',
  `class` int(11) NULL DEFAULT NULL COMMENT '模块排序',
  `config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '模块配置',
  `template_relay_id` int(11) NULL DEFAULT NULL COMMENT '对应模板中模块的ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` tinyint(4) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 263 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Compact;

-- 5.模板操作日志表
CREATE TABLE `template_action_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NULL DEFAULT NULL,
  `action_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `history_id` int(11) NULL DEFAULT NULL,
  `create_by` int(11) NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 191 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Compact;
