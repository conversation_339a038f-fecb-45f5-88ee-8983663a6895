a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:54:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:48:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:52:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:6:"pragma";s:8:"no-cache";s:13:"cache-control";s:8:"no-cache";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:3:"*/*";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750388960952";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:18:"experiment-list/my";s:6:"action";s:57:"frontend\controllers\ExperimentListController::actionMy()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:0:{}s:6:"SERVER";a:44:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:11:"HTTP_PRAGMA";s:8:"no-cache";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:3:"*/*";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750388960952";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:47:"D:/integle2025/eln_trunk/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"52181";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:29:"r=experiment-list/my&limit=15";s:11:"REQUEST_URI";s:31:"/?r=experiment-list/my&limit=15";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1750388961.8210001;s:12:"REQUEST_TIME";i:1750388961;}s:3:"GET";a:2:{s:1:"r";s:18:"experiment-list/my";s:5:"limit";s:2:"15";}s:4:"POST";a:0:{}s:6:"COOKIE";a:9:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:15:"center_language";s:2:"CN";s:11:"dataview_id";s:3:"101";s:9:"page_type";s:1:"1";s:16:"last_active_time";s:13:"1750388960952";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1135";s:5:"email";N;s:4:"name";s:6:"chenqi";s:5:"phone";N;s:6:"ticket";s:32:"38828f261ee60584144cf546b2ff9ece";s:8:"reg_time";s:10:"1744077856";s:5:"Token";s:32:"7eb44480540d6e80df79fce77c791828";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:6:"陈奇";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"2";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,84";s:10:"department";a:0:{}s:2:"id";s:4:"1135";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"3";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:45:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750388961.9343951;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750388962.0636439;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750388962.283155;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750388962.286459;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1750388962.290256;i:4;a:0:{}}i:5;a:5:{i:0;s:37:"Route requested: 'experiment-list/my'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1750388962.290355;i:4;a:0:{}}i:6;a:5:{i:0;s:56:"请求数据为:{"r":"experiment-list\/my","limit":"15"}";i:1;i:4;i:2;s:7:"Request";i:3;d:**********.012476;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:132;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}}i:7;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.6018901;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.623142;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.6236269;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.6240001;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.624388;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.6244781;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.6268449;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.632113;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.654376;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.6549821;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.6555021;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:18;a:5:{i:0;s:32:"Route to run: experiment-list/my";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.6595449;i:4;a:0:{}}i:19;a:5:{i:0;s:73:"Running action: frontend\controllers\ExperimentListController::actionMy()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.6614089;i:4;a:0:{}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.1401601;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:84:"SELECT * FROM `user_group_role` WHERE (`group_id` IN ('1', '598')) AND (`role_id`=3)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1527081;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_group_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1581299;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'user_group_role' AND kcu.table_name = 'user_group_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1636341;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:30:"Executing Redis Command: setex";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750388964.1647041;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:136;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:136;s:8:"function";s:5:"setex";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.203289;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:36;a:5:{i:0;s:283:"SELECT COUNT(*) FROM (SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC) `c`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2052381;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:265:"SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC LIMIT 15";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.206495;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `experiment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2074051;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'experiment' AND kcu.table_name = 'experiment'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2118189;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:1142:"SELECT `EXP`.`id`, `EXP`.`id` AS `experiment_id`, `EXP`.`template_id`, `EXP`.`book_id`, `EXP`.`exp_page`, `EXP`.`keywords`, `EXP`.`title`, `EXP`.`weather_json`, `EXP`.`witness_user_id`, `EXP`.`witness_reason`, `EXP`.`witness_comment`, `EXP`.`witness_status`, `EXP`.`witness_time`, `EXP`.`pretrial_user_id`, `EXP`.`pretrial_comment`, `EXP`.`pretrial_status`, `EXP`.`pretrial_time`, `EXP`.`abbr_info`, `EXP`.`indraw_info`, `EXP`.`project_id`, `EXP`.`task_id`, `EXP`.`project_progress`, `EXP`.`user_id`, `EXP`.`edit_user_id`, `EXP`.`create_time`, `EXP`.`update_time`, `EXP`.`step`, `EXP`.`isPerson`, `EXP`.`result`, `EXP`.`star`, `EXP`.`reopen_status`, `EXP`.`route_exp_ids`, `EXP`.`before_route_exp_ids`, `EXP`.`after_route_exp_ids`, `BOK`.`name` AS `book_name`, `BOK`.`code`, `BOK`.`book_code`, `BOK`.`group_id`, `BOK`.`create_time` AS `book_create_time`, `Temp`.`name` AS `template_name` FROM `experiment` `EXP` LEFT JOIN `template` `Temp` ON Temp.id=EXP.template_id LEFT JOIN `book` `BOK` ON BOK.id=EXP.book_id AND EXP.exp_page !="" WHERE `EXP`.`id` IN ('36630', '36557', '36556', '36546', '36545', '36537') ORDER BY `EXP`.`create_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2182021;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"D:\integle2025\eln_trunk\common\models\BaseModel.php";s:4:"line";i:266;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\models\ExperimentModel.php";s:4:"line";i:656;s:8:"function";s:13:"cacheQueryAll";s:5:"class";s:23:"common\models\BaseModel";s:4:"type";s:2:"::";}}}i:51;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750388964.563832;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2413;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:52;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750388964.564322;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1430;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1430;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2416;s:8:"function";s:21:"getVisibleDepartments";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:53;a:5:{i:0;s:120:"SELECT `experiment_id` FROM `experiment_favorites` WHERE (`user_id`='1135') AND (`status`=1) ORDER BY `update_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.577081;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7713;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1336;s:8:"function";s:13:"favoritesList";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:56;a:5:{i:0;s:110:"SELECT `experiment_id`, `setting` FROM `experiment_reminder_setting` WHERE (`user_id`='1135') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.582104;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7537;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1340;s:8:"function";s:13:"listRemindExp";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:192:"SELECT `parent_id` AS `id`, count(parent_id) cnt FROM `share` WHERE (`parent_id` IN ('36630', '36557', '36556', '36546', '36545', '36537')) AND (`status`=1) AND (`type`=1) GROUP BY `parent_id`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.5934839;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\integle2025\eln_trunk\frontend\models\ShareModel.php";s:4:"line";i:1192;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:58:"D:\integle2025\eln_trunk\frontend\services\ShareServer.php";s:4:"line";i:578;s:8:"function";s:14:"listShareCount";s:5:"class";s:26:"frontend\models\ShareModel";s:4:"type";s:2:"::";}}}i:62;a:5:{i:0;s:570:"SELECT `temple`.`id`, `temple`.`name` FROM `template` `temple` LEFT JOIN `share` `share` ON share.parent_id = temple.id AND share.user_id = temple.user_id WHERE ((`temple`.`status`=1) AND (`temple`.`type`=1)) AND (((`share`.`to_group_id` IN ('1', '598')) AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR ((`share`.`to_user_id`='1135') AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR (`temple`.`user_id`='1135') OR (`temple`.`is_system`=1) OR (`temple`.`is_company`=1)) ORDER BY `temple`.`create_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6225331;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.634577;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6377499;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:78:"Rendering view file: D:\integle2025\eln_trunk\frontend\views\exp_list/main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1750388964.647723;i:4;a:1:{i:0;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:117;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='SHOW_COPY_EXP_BUTTON') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6588881;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\tool.php";s:4:"line";i:8;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:94:"Rendering view file: D:\integle2025\eln_trunk\frontend\views/components/multi_group_select.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1750388964.67553;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\views\exp_list\filter.php";s:4:"line";i:55;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\main.php";s:4:"line";i:10;s:8:"function";s:7:"include";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:117;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:76;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750388964.6866701;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_trunk\frontend\views\components\multi_group_select.php";s:4:"line";i:22;s:8:"function";s:24:"getGroupsListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:88:"Rendering view file: D:\integle2025\eln_trunk\frontend\views/components/multi_select.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1750388964.7173951;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\views\exp_list\filter.php";s:4:"line";i:169;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\main.php";s:4:"line";i:10;s:8:"function";s:7:"include";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:117;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750388964.7292819;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:23156184;s:4:"time";d:2.9771180152893066;s:8:"messages";a:34:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.140238;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.152586;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:84:"SELECT * FROM `user_group_role` WHERE (`group_id` IN ('1', '598')) AND (`role_id`=3)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1527591;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:84:"SELECT * FROM `user_group_role` WHERE (`group_id` IN ('1', '598')) AND (`role_id`=3)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.156626;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_group_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1582241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_group_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1606531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'user_group_role' AND kcu.table_name = 'user_group_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1636879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'user_group_role' AND kcu.table_name = 'user_group_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.164402;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.203347;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750388964.205164;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:283:"SELECT COUNT(*) FROM (SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC) `c`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.205276;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:283:"SELECT COUNT(*) FROM (SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC) `c`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2062571;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:265:"SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC LIMIT 15";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2065361;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:265:"SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC LIMIT 15";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.207314;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `experiment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2074549;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `experiment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2107611;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'experiment' AND kcu.table_name = 'experiment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2118721;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'experiment' AND kcu.table_name = 'experiment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.212785;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:1142:"SELECT `EXP`.`id`, `EXP`.`id` AS `experiment_id`, `EXP`.`template_id`, `EXP`.`book_id`, `EXP`.`exp_page`, `EXP`.`keywords`, `EXP`.`title`, `EXP`.`weather_json`, `EXP`.`witness_user_id`, `EXP`.`witness_reason`, `EXP`.`witness_comment`, `EXP`.`witness_status`, `EXP`.`witness_time`, `EXP`.`pretrial_user_id`, `EXP`.`pretrial_comment`, `EXP`.`pretrial_status`, `EXP`.`pretrial_time`, `EXP`.`abbr_info`, `EXP`.`indraw_info`, `EXP`.`project_id`, `EXP`.`task_id`, `EXP`.`project_progress`, `EXP`.`user_id`, `EXP`.`edit_user_id`, `EXP`.`create_time`, `EXP`.`update_time`, `EXP`.`step`, `EXP`.`isPerson`, `EXP`.`result`, `EXP`.`star`, `EXP`.`reopen_status`, `EXP`.`route_exp_ids`, `EXP`.`before_route_exp_ids`, `EXP`.`after_route_exp_ids`, `BOK`.`name` AS `book_name`, `BOK`.`code`, `BOK`.`book_code`, `BOK`.`group_id`, `BOK`.`create_time` AS `book_create_time`, `Temp`.`name` AS `template_name` FROM `experiment` `EXP` LEFT JOIN `template` `Temp` ON Temp.id=EXP.template_id LEFT JOIN `book` `BOK` ON BOK.id=EXP.book_id AND EXP.exp_page !="" WHERE `EXP`.`id` IN ('36630', '36557', '36556', '36546', '36545', '36537') ORDER BY `EXP`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.218261;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"D:\integle2025\eln_trunk\common\models\BaseModel.php";s:4:"line";i:266;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\models\ExperimentModel.php";s:4:"line";i:656;s:8:"function";s:13:"cacheQueryAll";s:5:"class";s:23:"common\models\BaseModel";s:4:"type";s:2:"::";}}}i:50;a:5:{i:0;s:1142:"SELECT `EXP`.`id`, `EXP`.`id` AS `experiment_id`, `EXP`.`template_id`, `EXP`.`book_id`, `EXP`.`exp_page`, `EXP`.`keywords`, `EXP`.`title`, `EXP`.`weather_json`, `EXP`.`witness_user_id`, `EXP`.`witness_reason`, `EXP`.`witness_comment`, `EXP`.`witness_status`, `EXP`.`witness_time`, `EXP`.`pretrial_user_id`, `EXP`.`pretrial_comment`, `EXP`.`pretrial_status`, `EXP`.`pretrial_time`, `EXP`.`abbr_info`, `EXP`.`indraw_info`, `EXP`.`project_id`, `EXP`.`task_id`, `EXP`.`project_progress`, `EXP`.`user_id`, `EXP`.`edit_user_id`, `EXP`.`create_time`, `EXP`.`update_time`, `EXP`.`step`, `EXP`.`isPerson`, `EXP`.`result`, `EXP`.`star`, `EXP`.`reopen_status`, `EXP`.`route_exp_ids`, `EXP`.`before_route_exp_ids`, `EXP`.`after_route_exp_ids`, `BOK`.`name` AS `book_name`, `BOK`.`code`, `BOK`.`book_code`, `BOK`.`group_id`, `BOK`.`create_time` AS `book_create_time`, `Temp`.`name` AS `template_name` FROM `experiment` `EXP` LEFT JOIN `template` `Temp` ON Temp.id=EXP.template_id LEFT JOIN `book` `BOK` ON BOK.id=EXP.book_id AND EXP.exp_page !="" WHERE `EXP`.`id` IN ('36630', '36557', '36556', '36546', '36545', '36537') ORDER BY `EXP`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2207921;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"D:\integle2025\eln_trunk\common\models\BaseModel.php";s:4:"line";i:266;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\models\ExperimentModel.php";s:4:"line";i:656;s:8:"function";s:13:"cacheQueryAll";s:5:"class";s:23:"common\models\BaseModel";s:4:"type";s:2:"::";}}}i:54;a:5:{i:0;s:120:"SELECT `experiment_id` FROM `experiment_favorites` WHERE (`user_id`='1135') AND (`status`=1) ORDER BY `update_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.577142;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7713;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1336;s:8:"function";s:13:"favoritesList";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:120:"SELECT `experiment_id` FROM `experiment_favorites` WHERE (`user_id`='1135') AND (`status`=1) ORDER BY `update_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.577883;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7713;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1336;s:8:"function";s:13:"favoritesList";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:110:"SELECT `experiment_id`, `setting` FROM `experiment_reminder_setting` WHERE (`user_id`='1135') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.582159;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7537;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1340;s:8:"function";s:13:"listRemindExp";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:110:"SELECT `experiment_id`, `setting` FROM `experiment_reminder_setting` WHERE (`user_id`='1135') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.582684;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7537;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1340;s:8:"function";s:13:"listRemindExp";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:192:"SELECT `parent_id` AS `id`, count(parent_id) cnt FROM `share` WHERE (`parent_id` IN ('36630', '36557', '36556', '36546', '36545', '36537')) AND (`status`=1) AND (`type`=1) GROUP BY `parent_id`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.593595;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\integle2025\eln_trunk\frontend\models\ShareModel.php";s:4:"line";i:1192;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:58:"D:\integle2025\eln_trunk\frontend\services\ShareServer.php";s:4:"line";i:578;s:8:"function";s:14:"listShareCount";s:5:"class";s:26:"frontend\models\ShareModel";s:4:"type";s:2:"::";}}}i:61;a:5:{i:0;s:192:"SELECT `parent_id` AS `id`, count(parent_id) cnt FROM `share` WHERE (`parent_id` IN ('36630', '36557', '36556', '36546', '36545', '36537')) AND (`status`=1) AND (`type`=1) GROUP BY `parent_id`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.5945499;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\integle2025\eln_trunk\frontend\models\ShareModel.php";s:4:"line";i:1192;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:58:"D:\integle2025\eln_trunk\frontend\services\ShareServer.php";s:4:"line";i:578;s:8:"function";s:14:"listShareCount";s:5:"class";s:26:"frontend\models\ShareModel";s:4:"type";s:2:"::";}}}i:63;a:5:{i:0;s:570:"SELECT `temple`.`id`, `temple`.`name` FROM `template` `temple` LEFT JOIN `share` `share` ON share.parent_id = temple.id AND share.user_id = temple.user_id WHERE ((`temple`.`status`=1) AND (`temple`.`type`=1)) AND (((`share`.`to_group_id` IN ('1', '598')) AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR ((`share`.`to_user_id`='1135') AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR (`temple`.`user_id`='1135') OR (`temple`.`is_system`=1) OR (`temple`.`is_company`=1)) ORDER BY `temple`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6225929;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:570:"SELECT `temple`.`id`, `temple`.`name` FROM `template` `temple` LEFT JOIN `share` `share` ON share.parent_id = temple.id AND share.user_id = temple.user_id WHERE ((`temple`.`status`=1) AND (`temple`.`type`=1)) AND (((`share`.`to_group_id` IN ('1', '598')) AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR ((`share`.`to_user_id`='1135') AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR (`temple`.`user_id`='1135') OR (`temple`.`is_system`=1) OR (`temple`.`is_company`=1)) ORDER BY `temple`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.634429;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6346321;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6372271;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6377821;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6385469;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='SHOW_COPY_EXP_BUTTON') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6589611;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\tool.php";s:4:"line";i:8;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='SHOW_COPY_EXP_BUTTON') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.659456;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\tool.php";s:4:"line";i:8;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:30:{i:24;a:5:{i:0;s:84:"SELECT * FROM `user_group_role` WHERE (`group_id` IN ('1', '598')) AND (`role_id`=3)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1527591;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:84:"SELECT * FROM `user_group_role` WHERE (`group_id` IN ('1', '598')) AND (`role_id`=3)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.156626;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_group_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1582241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_group_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1606531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'user_group_role' AND kcu.table_name = 'user_group_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.1636879;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'user_group_role' AND kcu.table_name = 'user_group_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.164402;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:117;s:8:"function";s:7:"findAll";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:43;s:8:"function";s:20:"elnGroupListByUserId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:283:"SELECT COUNT(*) FROM (SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC) `c`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.205276;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:283:"SELECT COUNT(*) FROM (SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC) `c`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2062571;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2181;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:265:"SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC LIMIT 15";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2065361;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:265:"SELECT DISTINCT `exp`.`id` FROM `experiment` `exp` INNER JOIN `book` `book` ON exp.book_id = book.id WHERE (((`exp`.`status`=1) AND (`book`.`status`=1)) AND (`book`.`group_id` IN ('1', '598'))) AND (`exp`.`user_id`='1135') ORDER BY `exp`.`create_time` DESC LIMIT 15";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.207314;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `experiment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2074549;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `experiment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2107611;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'experiment' AND kcu.table_name = 'experiment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2118721;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'experiment' AND kcu.table_name = 'experiment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.212785;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:2190;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:61;s:8:"function";s:24:"listExperimentIdByFilter";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:1142:"SELECT `EXP`.`id`, `EXP`.`id` AS `experiment_id`, `EXP`.`template_id`, `EXP`.`book_id`, `EXP`.`exp_page`, `EXP`.`keywords`, `EXP`.`title`, `EXP`.`weather_json`, `EXP`.`witness_user_id`, `EXP`.`witness_reason`, `EXP`.`witness_comment`, `EXP`.`witness_status`, `EXP`.`witness_time`, `EXP`.`pretrial_user_id`, `EXP`.`pretrial_comment`, `EXP`.`pretrial_status`, `EXP`.`pretrial_time`, `EXP`.`abbr_info`, `EXP`.`indraw_info`, `EXP`.`project_id`, `EXP`.`task_id`, `EXP`.`project_progress`, `EXP`.`user_id`, `EXP`.`edit_user_id`, `EXP`.`create_time`, `EXP`.`update_time`, `EXP`.`step`, `EXP`.`isPerson`, `EXP`.`result`, `EXP`.`star`, `EXP`.`reopen_status`, `EXP`.`route_exp_ids`, `EXP`.`before_route_exp_ids`, `EXP`.`after_route_exp_ids`, `BOK`.`name` AS `book_name`, `BOK`.`code`, `BOK`.`book_code`, `BOK`.`group_id`, `BOK`.`create_time` AS `book_create_time`, `Temp`.`name` AS `template_name` FROM `experiment` `EXP` LEFT JOIN `template` `Temp` ON Temp.id=EXP.template_id LEFT JOIN `book` `BOK` ON BOK.id=EXP.book_id AND EXP.exp_page !="" WHERE `EXP`.`id` IN ('36630', '36557', '36556', '36546', '36545', '36537') ORDER BY `EXP`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.218261;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"D:\integle2025\eln_trunk\common\models\BaseModel.php";s:4:"line";i:266;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\models\ExperimentModel.php";s:4:"line";i:656;s:8:"function";s:13:"cacheQueryAll";s:5:"class";s:23:"common\models\BaseModel";s:4:"type";s:2:"::";}}}i:50;a:5:{i:0;s:1142:"SELECT `EXP`.`id`, `EXP`.`id` AS `experiment_id`, `EXP`.`template_id`, `EXP`.`book_id`, `EXP`.`exp_page`, `EXP`.`keywords`, `EXP`.`title`, `EXP`.`weather_json`, `EXP`.`witness_user_id`, `EXP`.`witness_reason`, `EXP`.`witness_comment`, `EXP`.`witness_status`, `EXP`.`witness_time`, `EXP`.`pretrial_user_id`, `EXP`.`pretrial_comment`, `EXP`.`pretrial_status`, `EXP`.`pretrial_time`, `EXP`.`abbr_info`, `EXP`.`indraw_info`, `EXP`.`project_id`, `EXP`.`task_id`, `EXP`.`project_progress`, `EXP`.`user_id`, `EXP`.`edit_user_id`, `EXP`.`create_time`, `EXP`.`update_time`, `EXP`.`step`, `EXP`.`isPerson`, `EXP`.`result`, `EXP`.`star`, `EXP`.`reopen_status`, `EXP`.`route_exp_ids`, `EXP`.`before_route_exp_ids`, `EXP`.`after_route_exp_ids`, `BOK`.`name` AS `book_name`, `BOK`.`code`, `BOK`.`book_code`, `BOK`.`group_id`, `BOK`.`create_time` AS `book_create_time`, `Temp`.`name` AS `template_name` FROM `experiment` `EXP` LEFT JOIN `template` `Temp` ON Temp.id=EXP.template_id LEFT JOIN `book` `BOK` ON BOK.id=EXP.book_id AND EXP.exp_page !="" WHERE `EXP`.`id` IN ('36630', '36557', '36556', '36546', '36545', '36537') ORDER BY `EXP`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.2207921;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"D:\integle2025\eln_trunk\common\models\BaseModel.php";s:4:"line";i:266;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\models\ExperimentModel.php";s:4:"line";i:656;s:8:"function";s:13:"cacheQueryAll";s:5:"class";s:23:"common\models\BaseModel";s:4:"type";s:2:"::";}}}i:54;a:5:{i:0;s:120:"SELECT `experiment_id` FROM `experiment_favorites` WHERE (`user_id`='1135') AND (`status`=1) ORDER BY `update_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.577142;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7713;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1336;s:8:"function";s:13:"favoritesList";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:120:"SELECT `experiment_id` FROM `experiment_favorites` WHERE (`user_id`='1135') AND (`status`=1) ORDER BY `update_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.577883;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7713;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1336;s:8:"function";s:13:"favoritesList";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:110:"SELECT `experiment_id`, `setting` FROM `experiment_reminder_setting` WHERE (`user_id`='1135') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.582159;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7537;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1340;s:8:"function";s:13:"listRemindExp";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:110:"SELECT `experiment_id`, `setting` FROM `experiment_reminder_setting` WHERE (`user_id`='1135') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.582684;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\ExperimentServer.php";s:4:"line";i:7537;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:1340;s:8:"function";s:13:"listRemindExp";s:5:"class";s:34:"frontend\services\ExperimentServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:192:"SELECT `parent_id` AS `id`, count(parent_id) cnt FROM `share` WHERE (`parent_id` IN ('36630', '36557', '36556', '36546', '36545', '36537')) AND (`status`=1) AND (`type`=1) GROUP BY `parent_id`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.593595;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\integle2025\eln_trunk\frontend\models\ShareModel.php";s:4:"line";i:1192;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:58:"D:\integle2025\eln_trunk\frontend\services\ShareServer.php";s:4:"line";i:578;s:8:"function";s:14:"listShareCount";s:5:"class";s:26:"frontend\models\ShareModel";s:4:"type";s:2:"::";}}}i:61;a:5:{i:0;s:192:"SELECT `parent_id` AS `id`, count(parent_id) cnt FROM `share` WHERE (`parent_id` IN ('36630', '36557', '36556', '36546', '36545', '36537')) AND (`status`=1) AND (`type`=1) GROUP BY `parent_id`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.5945499;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\integle2025\eln_trunk\frontend\models\ShareModel.php";s:4:"line";i:1192;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:58:"D:\integle2025\eln_trunk\frontend\services\ShareServer.php";s:4:"line";i:578;s:8:"function";s:14:"listShareCount";s:5:"class";s:26:"frontend\models\ShareModel";s:4:"type";s:2:"::";}}}i:63;a:5:{i:0;s:570:"SELECT `temple`.`id`, `temple`.`name` FROM `template` `temple` LEFT JOIN `share` `share` ON share.parent_id = temple.id AND share.user_id = temple.user_id WHERE ((`temple`.`status`=1) AND (`temple`.`type`=1)) AND (((`share`.`to_group_id` IN ('1', '598')) AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR ((`share`.`to_user_id`='1135') AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR (`temple`.`user_id`='1135') OR (`temple`.`is_system`=1) OR (`temple`.`is_company`=1)) ORDER BY `temple`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6225929;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:570:"SELECT `temple`.`id`, `temple`.`name` FROM `template` `temple` LEFT JOIN `share` `share` ON share.parent_id = temple.id AND share.user_id = temple.user_id WHERE ((`temple`.`status`=1) AND (`temple`.`type`=1)) AND (((`share`.`to_group_id` IN ('1', '598')) AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR ((`share`.`to_user_id`='1135') AND (`share`.`type`=2) AND (`share`.`status`=1) AND (`share`.`share_type`=1)) OR (`temple`.`user_id`='1135') OR (`temple`.`is_system`=1) OR (`temple`.`is_company`=1)) ORDER BY `temple`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.634429;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6346321;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6372271;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6377821;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6385469;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:59:"D:\integle2025\eln_trunk\frontend\services\TempleServer.php";s:4:"line";i:1448;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:74:"D:\integle2025\eln_trunk\frontend\controllers\ExperimentListController.php";s:4:"line";i:101;s:8:"function";s:16:"tempForExpFilter";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='SHOW_COPY_EXP_BUTTON') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.6589611;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\tool.php";s:4:"line";i:8;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='SHOW_COPY_EXP_BUTTON') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750388964.659456;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"D:\integle2025\eln_trunk\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"D:\integle2025\eln_trunk\frontend\views\exp_list\tool.php";s:4:"line";i:8;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"6854d0e244900";s:3:"url";s:57:"http://dev.eln.integle.com/?r=experiment-list/my&limit=15";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";i:1750388964;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:9:"mailCount";i:0;}}