define(function (require) {
    const LOCAL_INSTRUMENT_INS_FIELDS = 'instrument_insert_data_fields';//localhost中保存仪器插入数据字段选项的key
    const INST_FIELDS_TYPE = {DATA:1, FILE: 2, DATA_KEY: 'DATA', FILE_KEY: 'FILE'};//仪器插入数据 的类型：1=数值，2=文件

    // add by hkk 2020/7/20 打开仪器维修记录页面
    $('body').on('click', '.operateRepairRecord', function () {
        instrumentApi.insId = $(this).attr('data-id');
        require('get_html').genInstrumentRepairRecordPage(instrumentApi.insId);
    });

    // add by hkk 2020/7/20  维修记录页面添加维修记录操作 2021/4/14 增加编辑维修记录
    $('body').on('click', '.addRepairRecord', function () {


        var instrumentId = $(this).attr('data-id');
        var type = $(this).attr('data-type');
        var data = {
            instrumentId: instrumentId,
            type: type,
            runningId: $(this).attr('data-repairRecordId'), // 编辑记录需要记录的id
            orderNumber: $(this).attr('data-orderNumber') // 编辑记录需要记录的序号
        };


        var addRepairRecordArray = {};
        var th = $('.exp_conetnt.active .instruments_repair_record_table').find('tr th');
        th.each(function () {
            if ($(this).attr('data-field')) {
                addRepairRecordArray[$(this).attr('data-field')] = $(this).find('.instrument_def_title').attr('title');
            }
        });
        data.addRepairRecordArray = addRepairRecordArray;

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-repair-record-operate-page',
            data: data,
            success: function (data) {
                if (data.status == 1) {

                    $(".instrument_add_repair_record_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_add_repair_record_page").modal('show');

                    //界面调用日历插件
                    $('.instrument_add_repair_record_page .newdatetimepicker').datetimepicker({
                        format: 'yyyy-mm-dd hh:ii',
                        autoclose: true,
                        minView: 0,
                        clearBtn: true,
                        container:'#ins_pop_add_repair_page', // add by hkk 2022/8/12 防止滚动失效
                    });
                }
            }
        });


    });

    // add by hkk 2020/7/20  维修记录页面新增弹框确认操作 2021/4/14 增加编辑维修记录
    $('body').on('click', '.add_repair_record_conform_btn', function () {

        var repairRecordData = {};
        var hasEmptyRequire = false; // 有空的必填项
        var type = $(this).attr('data-type');

        // 遍历选择框存储数据
        $('.instrument_add_repair_record_page li').each(function (index, item) {

            var key = $(item).attr('data-field');
            if (key === 'review') {
                return;
            }
            var result = $(item).find('input').val();

            if (key === 'repair_start_time' && result === ''&& $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip5'));
                hasEmptyRequire = true;
                return false;
            }
            if ( key === 'repair_type') {
                result = $('.repair-type-select option:selected').val();
            }

            if (key === 'repair_end_time' && result === '' && $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip6'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'repair_content' && result === ''&& $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip7'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'repair_result' && result === '' && $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip8'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'repair_person' && result === '' && $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip9'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'file') { // 文件
                var checkFilesArray = [];
                $('.instrument_add_repair_record_page .single_detail_file').each(function () {
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    checkFilesArray.push(fileData)
                });
                result = JSON.stringify(checkFilesArray);
                repairRecordData[key] = result;
            } else {
                repairRecordData[key] = result;
            }

        });

        // 开始结束结束时间比较
        if ((new Date(repairRecordData['repair_start_time']).getTime()) > (new Date(repairRecordData['repair_end_time']).getTime())) {
            $.showAlert(mainLang('instrument_reminder_tip24'));
            return false
        }

        if(hasEmptyRequire){ // 有空的必填项
            return;
        }
        repairRecordData['operation_time'] = $(this).attr('data-operationTime');
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/submit-add-repair-record',
            data: {
                repairRecordData: repairRecordData,
                instrumentId: $(this).attr('data-id'),
                statusNormal: $('.instrument_add_repair_record_page input.instrumentStatus').prop('checked'),
                type: $(this).attr('data-type'), // addRepairRecord editBeforeEndRepairRecord editAfterEndRepairRecord
                runningId: $(this).attr('data-repairRecordId'), // 编辑记录需要记录id
                orderNumber: $(this).attr('data-orderNumber'), // 编辑记录需要记录的序号
                checkIds: $(this).attr('data-checkIds'), // 编辑记录需要记录的序号
            },
            success: function (data) {
                if (data.status == 1) {
                    $(".instrument_add_repair_record_page").remove();
                    $('body').removeClass('modal-open');

                    if (type === 'addRepairRecord') {
                        $.showAlert(mainLang('instrument_add_success'));
                    } else {
                        $.showAlert(mainLang('edit_running_success'));
                    }

                    require('tab').reloadActiveTag();  // 再次刷新维修记录页面
                }
            },
        })
    });

    // add by hkk 2020/7/20  维修记录页面新增列
    $('body').on('click', '.addRepairRecordCol', function () {

        //计算当前已添加的自定义列索引 文件列前的th的index
        var instrumentId = $(this).attr('data-id');
        var extendFields = $('.exp_conetnt.active .instruments_repair_record_table th[data-field*="extend_field"]');
        var defineColumnField = extendFields.length+1;

        if (parseInt(defineColumnField) > 50) {
            $.showAlert(mainLang('instrument_reminder_tip10'));
            return
        }

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/add-repair-record-column',
            data: {
                field:defineColumnField,
                instrumentId: instrumentId,
            },
            success: function (data) {
                if (data.status == 1) {
                    // 再次刷新维修记录页面  // require('tab').reloadActiveTag();
                    $.ajaxFn({
                        type: 'post',
                        url: ELN_URL + '?r=instrument/get-repair-record-page',
                        data: {
                            needUpdateAllPage: "no",
                            instrumentId:instrumentId,
                        },
                        success:function(data){
                            if (data.status === 1) {

                                $('.exp_conetnt.active .instruments_repair_record_table').html(data.data.file);
                                // 激活刚增加的列标题编辑
                                var head = $('.exp_conetnt.active .instruments_repair_record_table th[data-field="extend_field_'+ defineColumnField +'"]')
                                $('.instrument_title_input', head).show().focus();
                                $('.instrument_title_input', head).select();
                                $('.instrument_ext_title', head).hide();
                            }
                        },
                    });

                    // 找到文件列索引矫正显示隐藏列缓存
                    /*var fileThNumber = fileTh.index() + 1;
                    var listCols = localStorage.getItem('instruments_repair_record_table_' + instrumentId + '_showHideColumn_cols_index');
                    if (listCols) {
                        var listArray = listCols.split(',');
                        for (var i = 0; i < listArray.length; i++) {
                            if(parseInt(listArray[i],10) >= fileThNumber){
                                listArray[i] = (parseInt(listArray[i],10) + 1).toString()
                            }
                        }
                        localStorage.setItem('instruments_repair_record_table_' + instrumentId + '_showHideColumn_cols_index', listArray);
                    }*/


                }
            },
            noLoad:true,
        })

    });

    // add by hkk 2020/7/21  维修记录页面/运行记录页面/校验记录页面双击触发更改标题事件   2021/4/28 加仪器管理页面修改自定义标题
    $('body').on('dblclick', '.instrument_ext_title', function () {
        window.noLoadTip = true;
        var head = $(this).parents('th');
        $('.instrument_title_input', head).show().focus().select();
        $('.instrument_ext_title', head).hide();
    });

    // add by hkk 2020/7/21  维修记录页面/运行记录页面/校验记录页面标题修改判断回车失去焦点 2021/4/28 加仪器管理页面修改自定义标题
    $('body').on('keypress', '.instrument_title_input', function (e) {
        e = e || window.event;
        var key = e.keyCode || e.which;
        if (key === 13) {
            $(this).blur();
        }
    });

    // add by hkk 2020/7/21  维修记录页面/运行记录页面/校验记录页面标题修改发送数据库更改 2021/4/28 加仪器管理页面修改自定义标题
    $('body').on('blur', '.instrument_title_input', function () {

        var head = $(this).parents('th');
        if($(this).val() == ''){ // 不让修改为空
            $.showAlert(mainLang('instrument_reminder_tip11'));
            $('.instrument_title_input', head).show().focus();
            return
        }

        $('.instrument_title_input', head).hide();
        $('.instrument_ext_title', head).text($(this).val()).show();
        $('.instrument_ext_title', head).attr('title',$(this).val());

        // 更改数据库
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/modify-repair-record-title',
            data: {
                instrumentId: $(this).parents('table').attr('data-id') ||'',
                field: head.attr('data-field'),
                fieldValue: $(this).val(),
                type: $(this).parents('table').attr('data-type'), // 加仪器自定义标题修改
            },
            noLoad: true,
        })
    });

    // add by hkk 2020/7/21 维修记录页面复核事件
    $('body').on('click', '.repairRecordDoubleCheck', function () {

        // 获取选中的维修记录id
        var chooseIds = [];
        $('.instruments_repair_record_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).attr('data-repairRecordId'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip12'));
            return
        }

        var html = '<div class="instrument_repair_record_check">  ' +
            '<li class="user_count"><label>'+ mainLang('account')  + ':</label><input type="text" /></li>' +
            '<li class="user_password"><label>'+ mainLang('password')  + ':</label><input type="password" /></li>' +
            '<li class="check_result"><label>'+ mainLang('repair_conclusion')  + ':</label><span><input type="radio" name="check_result_radio" checked value="1" />' +mainLang('allow') +'</span><span><input type="radio" name="check_result_radio" value="2" />' + mainLang('refuse') +'</span></li>' +
            '<li class="check_reason"><label >'+ mainLang('repair_reason')  + ':</label><textarea > </textarea></li>' +
            '</div>';

        $.popContent(html, mainLang('review'), function() {

            var userAccount = $('.instrument_repair_record_check .user_count input').val();
            var userPassword = hex_md5($('.instrument_repair_record_check .user_password input').val());
            var checkResult = $('.instrument_repair_record_check .check_result input:checked').val();
            var checkReason = $('.instrument_repair_record_check .check_reason textarea').val();

            if (userAccount === '') {
                $.showAlert(mainLang('instrument_reminder_tip13'));
                return
            }
            if (userPassword === '') {
                $.showAlert(mainLang('pass_must'));
                return
            }
            if (checkReason.trim() === '') {
                $.showAlert(mainLang('instrument_reminder_tip14'));
                return
            }

            // 更改数据库
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/check-repair-record',
                data: {
                    chooseIds: chooseIds,
                    userAccount:userAccount,
                    userPassword:userPassword,
                    checkResult:checkResult,
                    checkReason:checkReason,
                },
                noLoad:true,
                success: function (data) {
                    if (data.status == 1) {
                        require('tab').reloadActiveTag(); // 刷新页面
                        $(".instrument_repair_record_check").parents('.pop_modal').modal('hide'); // 隐藏弹框
                        $.showAlert(mainLang('review_success'));
                    }
                },
            })

        });

        return


    });

    // add by hkk 2020/7/21  维修记录页面导出事件
    $('body').on('click', '.repairRecordExport', function() {

        // 记录显示的导出列
        var repairRecordArray = [];
        var th = $('.exp_conetnt.active .instruments_repair_record_table').find('tr th');
        var repair_type = $(".exp_conetnt.active select[name='repair_type']").val();
        th.each(function(index, item) {
            if ($(this).attr('data-field') && $(this).is(":visible")) {
                repairRecordArray.push($(this).attr('data-field'))
            }
        });

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-repair-record-export',
            data: {
                repairRecordArray:repairRecordArray,
                instrumentId: $(this).attr('data-id'),
                repair_type: repair_type
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instrumentsRepairRecord.xlsx&file_name=instrumentsRepairRecord.xlsx";
                }
            }
        });
    });

    // add by hkk 2020/7/22 打开仪器运行记录页面
    $('body').on('click', '.operateOperateRecord', function () {
        instrumentApi.insId = $(this).attr('data-id');
        var expId = $('#exp_id').val();
        require('get_html').genInstrumentOperateRecordPage(instrumentApi.insId, expId);
    });

    // add by hkk 2022/6/20 实验页面打开运行记录
    $('body').on('click', '.operateOperateRecordByExp', function () {
        instrumentApi.insId = $(this).attr('data-id');
        var expId = $('#exp_id').val();
        require('get_html').genInstrumentOperateRecordPage(instrumentApi.insId, expId);
        $(".instrument_begin_operate_record_page").remove();

    });

    // add by hkk 2020/7/22  运行记录/开始结束运行 2021/4/14 增加编辑记录
    $('body').on('click', '.beginOrEndOperateRecord', function () {

        var instrumentId = $(this).attr('data-id');
        var data = {
            instrumentId: instrumentId,
            type:$(this).attr('data-type'),
            runningId:$(this).attr('data-operateId'), // 运行状态操作id
            orderNumber: $(this).attr('data-orderNumber') // 编辑记录需要记录的序号
        };

        // 计算当前显示列
        var addRepairRecordArray = {};
        var th = $('.exp_conetnt.active .instruments_operate_record_table').find('tr th');
        th.each(function(index, item) {
            if ($(this).attr('data-field')) {
                addRepairRecordArray[$(this).attr('data-field')] = $(this).text();
            }
        });
        data.addRepairRecordArray = addRepairRecordArray;

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-begin-operate-record-page', //actionGetBeginOperateRecordPage
            data: data,
            success: function (data) {
                if (data.status == 1) {

                    $(".instrument_begin_operate_record_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_begin_operate_record_page").modal('show');

                    //调用日历插件
                    $('.instrument_begin_operate_record_page .newdatetimepicker').datetimepicker({
                        format: 'yyyy-mm-dd hh:ii',
                        autoclose: true,
                        minView: 0,
                        clearBtn: true,
                        container:'#ins_pop_add_operate_page', // add by hkk 2022/8/12 防止滚动失效
                    });
                }
            }
        });

    });

    // add by hkk 2021/4/22  实验页面/开始结束运行记录
    $('body').on('click', '.beginOrEndOperateRecordByExp', function () {

        var type =  $(this).attr('data-type');
        var data = {
            instrumentId: $(this).attr('data-id'),
            runningId:$(this).attr('data-running-id'), // 运行状态操作id
            type,
        };

        var selectorSign = $(this).parents('.modul_box').find('.modul_part_id').attr('data-id') + '-' + $(this).parents('tr').index();

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-operate-record-page-by-exp', //actionGetOperateRecordPageByExp
            data: data,
            success: function (data) {
                if (data.status == 1) {

                    $(".instrument_begin_operate_record_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_begin_operate_record_page").modal('show');

                    if (type === 'begin') {  // 开始时自动填充实验页码 // add by hkk 2022/6/17
                        $(".instrument_begin_operate_record_page li[data-field=book_exp_page] input").val($('.exp_title .tag.on span').text())
                    }

                    // 写入元素定位标记
                    $('.instrument_begin_operate_record_page button.beginOrEndOperateRecordSubmit').attr('data-selector',selectorSign)

                    //调用日历插件
                    $('.instrument_begin_operate_record_page .newdatetimepicker').datetimepicker({
                        format: 'yyyy-mm-dd hh:ii',
                        autoclose: true,
                        minView: 0,
                        clearBtn: true,
                        container:'#ins_pop_add_operate_page', // add by hkk 2022/8/12 防止滚动失效
                    });
                }
            }
        });

    });


    // add by hesiliang 2024/10/9  不可编辑情况下打开仪器运行记录
    $('body').on('click', '.openOperateRecordByExp', function () {

        var data = {
            needUpdateAllPage: "yes",
            instrumentId: $(this).attr('data-id'),
            expId:$(this).attr('data-experiment-id'), // 实验ID
        };
        var tab = require('tab');
        var tagId = tab.getTag('getInstrumentOperateRecordContent',  [$(this).attr('data-id')]);
        if (tagId) { //存在Tag 直接切换到对应Tag即可
            tab.switchTag(tagId);
        } else {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-operate-record-page', //actionGetOperateRecordPage
                data: data,
                success: function (data) {
                    if (data.status === 1) {
                        var obj = {
                            html: data.data.contentHtml,
                            params: [$(this).attr('data-id')],
                            func: 'getInstrumentOperateRecordContent',
                            name: mainLang('operate_record') + "( " + data.data.insName + ")",
                            title: mainLang('operate_record') + "( " + data.data.insName + ")",
                        };
                        tab.openTag(obj);
                    }
                }
            });
        }

    });

    // add by hkk 2020/7/20  运行记录/开始结束运行 提交 2021/4/14 增加编辑记录提交
    $('body').on('click', '.beginOrEndOperateRecordSubmit', function () {

        var operateRecordData = {};
        var hasEmptyRequire = false; // 有空的必填项
        var forExp = $(this).attr('data-forExp') === 'forExp' ;
        var type = $(this).attr('data-type');
        var beforeClose = $(this).attr('data-beforeClose') === 'beforeClose'; // add by hkk 2022/6/16 编辑结束前编辑


        var selectorSign = $(this).attr('data-selector'); // add by hkk 2022/6/17


        // 遍历选择框存储数据
        $('.instrument_begin_operate_record_page li').each(function (index, item) {

            var key = $(item).attr('data-field');
            if (key === 'review') {
               return;
            }
            var result = $(item).find('input').val().trim();

            if (key === 'start_running_time' && result === '') {  //如果开始时间没有填拦截
                $.showAlert(mainLang('instrument_reminder_tip15'));
                hasEmptyRequire = true;
                return false;
            }
            if (key === 'end_running_time' && result === '' && (type === 'end' || type ==='edit') && !beforeClose) { //如果是结束后的编辑或者结束，没有填结束时间拦截
                $.showAlert(mainLang('instrument_reminder_tip16'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'running_content' && result === '' && (type ==='end' || type ==='edit') && !beforeClose) { //如果是结束后的编辑或者结束，没有填内容拦截
                $.showAlert(mainLang('instrument_reminder_tip17'));
                hasEmptyRequire = true;
                return false;
            }
            if (key === 'operator' && result === '') { //如果操作人没有填，拦截
                $.showAlert(mainLang('instrument_reminder_tip18'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'file') { // 文件
                var checkFilesArray = [];
                $('.instrument_begin_operate_record_page .single_detail_file').each(function () {
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    checkFilesArray.push(fileData)
                });
                result = JSON.stringify(checkFilesArray);
                operateRecordData[key] = result;
            } else {
                operateRecordData[key] = result;
            }
        });


        // 开始结束结束时间比较  缺少时间的比较逻辑
        if((new Date(operateRecordData['start_running_time']).getTime()) > (new Date(operateRecordData['end_running_time']).getTime())){
            $.showAlert(mainLang('instrument_reminder_tip24'));
            return false;
        }
        if (hasEmptyRequire) { // 有空的必填项
            return;
        }
        operateRecordData['start_operator_time'] = $(this).attr('data-startOperatorTime');
        var postData = {
            operateRecordData: operateRecordData,
            instrumentId: $(this).attr('data-id'),
            type: type, // begin end edit
            runningId: $(this).attr('data-operateId'), // 运行状态操作id 数据库中的该记录id
            orderNumber: $(this).attr('data-orderNumber'), // 编辑记录需要记录的序号 该仪器的运行记录的记录序号
            checkIds: $(this).attr('data-checkIds'), // 编辑记录需要记录的序号
        }
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/submit-begin-operate-record',
            data: postData,
            success: function (data) {
                if (data.status === 1) {
                    $(".instrument_begin_operate_record_page").remove();
                    $('body').removeClass('modal-open');

                    if (type === 'begin') {
                        $.showAlert(mainLang('start_running_success'));
                    } else if (type === 'edit') {
                        $.showAlert(mainLang('edit_running_success'));
                    } else {
                        $.showAlert(mainLang('end_running_success'));
                    }

                    if (!forExp) { // 除去实验页面的按钮都要刷新
                         require('tab').reloadActiveTag();  // 再次刷新记录页面
                    } else {
                        if (selectorSign) { // 返回的运行id写入记录
                            var moduleId = selectorSign.split('-')[0];
                            var rowIndex = selectorSign.split('-')[1];
                            var runningSelector = $('.exp_conetnt.active .modul_box .modul_part_id[data-id=' + moduleId + ']').next().find('tr:nth-child(' + (1 + rowIndex / 1) + ') i.beginOrEndOperateRecordByExp');

                            if (runningSelector.length > 0) { // 切换图标
                                if (type === 'begin') {
                                    runningSelector.attr('data-running-id', data['data']['runningId']);
                                    runningSelector.attr('data-type', 'end');
                                    runningSelector.attr('title', mainLang('end_running'));
                                    runningSelector.removeClass('instrument-running-record-ico');
                                    runningSelector.addClass('instrument-end-ico');
                                } else {
                                    runningSelector.attr('data-running-id', '');
                                    runningSelector.attr('data-type', 'begin');
                                    runningSelector.attr('title', mainLang('instrument_operate'));
                                    runningSelector.removeClass('instrument-end-ico');
                                    runningSelector.addClass('instrument-running-record-ico');
                                }
                            }
                        }
                    }
                }
            },
        });
    });

    // add by hkk 2020/7/20  运行记录页面新增列
    $('body').on('click', '.addOperateRecordCol', function () {

        //计算当前已添加的自定义列索引 记录本页码列前的th的index
        var instrumentId = $(this).attr('data-id');
        var extendFields = $('.exp_conetnt.active .instruments_operate_record_table th[data-field*="extend_field"]');
        var defineColumnField = (extendFields.length + 1).toString();


        if (parseInt(defineColumnField) > 50) {
            $.showAlert(mainLang('instrument_reminder_tip10'));
            return;
        }

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/add-operate-record-column',
            data: {
                field:defineColumnField,
                instrumentId: instrumentId,
            },
            success: function (data) {
                if (data.status == 1) {
                    // 再次刷新维修记录页面  // require('tab').reloadActiveTag();
                    var experimentId = $('#exp_id').val() ?? null;
                    $.ajaxFn({
                        type: 'post',
                        url: ELN_URL + '?r=instrument/get-operate-record-page',
                        data: {
                            needUpdateAllPage: "no",
                            instrumentId:instrumentId,
                            expId: experimentId
                        },
                        success:function(data){
                            if (data.status === 1) {

                                $('.exp_conetnt.active .instruments_operate_record_table').html(data.data.file);
                                // 激活刚增加的列标题编辑
                                var head = $('.exp_conetnt.active .instruments_operate_record_table th[data-field="extend_field_'+ defineColumnField + '"]')
                                $('.instrument_title_input', head).show().focus();
                                $('.instrument_title_input', head).select();
                                $('.instrument_ext_title', head).hide();
                            }
                        },
                    });

                }
            },
            noLoad:true,
        })

    });

    // add by hkk 2020/7/21  运行记录页面复核事件
    $('body').on('click', '.operateRecordDoubleCheck', function () {

        // 获取选中的维修记录id
        var chooseIds = [];
        $('.instruments_operate_record_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).attr('data-operateRecordId'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip12'));
            return
        }

        var html = '<div class="instrument_operate_record_check">  ' +
            '<li class="user_count"><label>'+ mainLang('account')  + ':</label><input type="text" /></li>' +
            '<li class="user_password"><label>'+ mainLang('password')  + ':</label><input type="password" /></li>' +
            '<li class="check_result"><label>'+ mainLang('repair_conclusion')  + ':</label><span><input type="radio" name="check_result_radio" checked value="1" />' + mainLang('allow') +'</span><span><input type="radio" name="check_result_radio" value="2" />' + mainLang('refuse') +'</span></li>' +
            '<li class="check_reason"><label >'+ mainLang('repair_reason')  + ':</label><textarea > </textarea></li>' +
            '</div>';

        $.popContent(html, mainLang('review'), function() {

            var userAccount = $('.instrument_operate_record_check .user_count input').val();
            var userPassword = hex_md5($('.instrument_operate_record_check .user_password input').val());
            var checkResult = $('.instrument_operate_record_check .check_result input:checked').val();
            var checkReason = $('.instrument_operate_record_check .check_reason textarea').val();

            if (userAccount === '') {
                $.showAlert(mainLang('instrument_reminder_tip13'));
                return
            }
            if (userPassword === '') {
                $.showAlert(mainLang('pass_must'));
                return
            }
            if (checkReason.trim() === '') {
                $.showAlert(mainLang('instrument_reminder_tip14'));
                return
            }

            // 更改数据库
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/check-repair-record',
                data: {
                    chooseIds: chooseIds,
                    userAccount:userAccount,
                    userPassword:userPassword,
                    checkResult:checkResult,
                    checkReason:checkReason,
                },
                noLoad:true,
                success: function (data) {
                    if (data.status == 1) {
                        require('tab').reloadActiveTag(); // 刷新页面
                        $(".instrument_operate_record_check").parents('.pop_modal').modal('hide'); // 隐藏弹框
                        $.showAlert(mainLang('review_success'));
                    }
                },
            })

        });

        return


    });

    // add by hkk 2020/7/21  运行记录页面导出事件
    $('body').on('click', '.operateRecordExport', function() {

        // 记录显示的导出列
        var operateRecordArray = [];
        var th = $('.exp_conetnt.active .instruments_operate_record_table').find('tr th');
        th.each(function(index, item) {
            if ($(this).attr('data-field') && $(this).is(":visible")) {
                operateRecordArray.push($(this).attr('data-field'))
            }
        });
        // bug#28132-仪器库管理-运行记录，如果有勾选，导出的仅包括勾选的条目 jiangdm 2022/8/9
        var chooseIds = [];
        $('.exp_conetnt.active .instruments_operate_record_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).attr('data-operaterecordid'));
        });
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-operate-record-export',
            data: {
                operateRecordArray:operateRecordArray,
                instrumentId: $(this).attr('data-id'),
                chooseIds: chooseIds,
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instrumentsOperateRecord.xlsx&file_name=instrumentsOperateRecord.xlsx";
                }
            }
        });
    });



    // add by hkk 2020/7/20 打开仪器校验记录页面
    $('body').on('click', '.operateCheckRecord', function () {
        instrumentApi.insId = $(this).attr('data-id');
        require('get_html').genInstrumentCheckRecordPage(instrumentApi.insId);
    });

    // add by hkk 2020/7/20 校验记录页面 新增校验页面 2021/4/14 新增编辑校验记录
    $('body').on('click', '.addCheckRecord', function () {

        var instrumentId = $(this).attr('data-id');
        var type = $(this).attr('data-type');
        var data = {
            instrumentId: instrumentId,
            type: type,
            runningId: $(this).attr('data-repairRecordId'), // 编辑记录需要记录的id
            orderNumber: $(this).attr('data-orderNumber'), // 编辑记录需要记录的序号
        };

        var addCheckRecordArray = {};
        var th = $('.exp_conetnt.active .instruments_check_record_table').find('tr th');
        th.each(function(index, item) {
            if ($(this).attr('data-field')) {
                addCheckRecordArray[$(this).attr('data-field')] = $(this).find('.instrument_def_title').attr('title');
            }
        });
        data.addCheckRecordArray = addCheckRecordArray;

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-check-record-operate-page',
            data: data,
            success: function (data) {
                if (data.status == 1) {

                    $(".instrument_add_check_record_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_add_check_record_page").modal('show');

                    //界面调用日历插件
                    $('.instrument_add_check_record_page .newdatetimepicker').datetimepicker({
                        format: 'yyyy-mm-dd hh:ii',
                        autoclose: true,
                        minView: 0,
                        clearBtn: true,
                        container:'#ins_pop_add_check_page', // add by hkk 2022/8/12 防止滚动失效
                    });
                }
            }
        });



    });

    // add by hkk 2022/6/14 校验记录页面 撤销复核记录操作
    $('body').on('click', '.undoCheckRecord', function () {
        var data = {
            instrumentId:  $(this).attr('data-id'),
            runningId: $(this).attr('data-repairRecordId'), // 记录的id 复核的business_id
            orderNumber: $(this).attr('data-orderNumber'), // 需要记录的序号
            recordType : $(this).attr('data-recordType'),//需要记录从哪个表里查数据
        };
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/before-undo-check-record',
            data,
            success: function (res) {
                if (res.status === 1) {
                    var html = `<label>${mainLang('undo_check_reason')}：</label><textarea class="instrument_undo_check_reason" ></textarea>`
                    $.showContent('undo_check', mainLang('undo_check'), html, function () {
                        data.undoReason = $('.instrument_undo_check_reason').val();
                        if (data.undoReason) {
                            $.ajaxFn({
                                url: ELN_URL + '?r=instrument/undo-check-record',
                                data,
                                success: function (resData) {
                                    if (resData.status === 1) {
                                        $.closeModal();
                                        require('tab').reloadActiveTag(); // 刷新页
                                        $.showAlert(mainLang('undo_successful_tip'));
                                    } else {
                                        setTimeout(() => {
                                            $.closeModal();
                                            require('tab').reloadActiveTag(); // 刷新页
                                        },2600)
                                    }
                                }
                            })
                        } else {
                            $.showAlert(mainLang('undo_check_reason'));
                        }
                    })
                } else {
                    setTimeout(() => {
                        require('tab').reloadActiveTag(); // 刷新页
                    },2600)
                }
            }

        })
        return false // 防止冒泡


    });


    // add by hkk 2022/6/14 校验记录页面 查看复核详情
    $('body').on('click', '.view-check-detail', function () {

        var html = '<div class="view-check-detail-page" style="margin-left: 80px">';
        var info = $(this).attr('data-info').split(';;');
        for (var i = 0; i < info.length; i++) {
            html += `<p>${info[i]} </p>`;
        }
        html += '</div>'
        $.popContent(html, mainLang('review_detail'), null,null,false)

    });

    // add by hkk 2020/7/20  校验记录页面新增弹框确认操作  2021/4/14 新增编辑校验记录
    $('body').on('click', '.add_check_record_conform_btn', function () {

        var recordData = {};
        var hasEmptyRequire = false; // 有空的必填项
        var type = $(this).attr('data-type');

        // 遍历选择框存储数据
        $('.instrument_add_check_record_page li').each(function (index, item) {

            var key = $(item).attr('data-field');
            if (key === 'review') {
                return;
            }
            var result = $(item).find('input').val();

            if (key === 'start_check_time' && result === '' && $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip19'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'end_check_time' && result === '' && $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip20'));
                hasEmptyRequire = true;
                return false;
            }

            if (key === 'check_person' && result === ''&& $(item).hasClass('required')) {
                $.showAlert(mainLang('instrument_reminder_tip21'));
                hasEmptyRequire = true;
                return false;
            }
            //有效期判断
            if (key === 'start_end_expiry_time') {
                if (($('.instrument_add_check_record_page li input#start_expiry_time').val() === '' || $('.instrument_add_check_record_page li input#end_expiry_time').val() === '' ) && $(item).hasClass('required')) {
                    $.showAlert(mainLang('instrument_reminder_tip22'));
                    hasEmptyRequire = true;
                    return false;
                } else {
                    recordData['start_expiry_time'] = $('.instrument_add_check_record_page li input#start_expiry_time').val();
                    recordData['end_expiry_time'] = $('.instrument_add_check_record_page li input#end_expiry_time').val();
                }
            }

            if (key === 'file') { // 文件
                var checkFilesArray = [];
                $('.instrument_add_check_record_page .single_detail_file').each(function () {
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    checkFilesArray.push(fileData)
                });
                result = JSON.stringify(checkFilesArray);
                recordData[key] = result;
            } else {
                recordData[key] = result;
            }
        })


        // 开始结束结束时间比较
        if ((new Date(recordData['start_check_time']).getTime()) > (new Date(recordData['end_check_time']).getTime())) {
            $.showAlert(mainLang('instrument_reminder_tip24'));
            return false
        }

        // 有效期开始结束结束时间比较
        if ((new Date(recordData['start_expiry_time']).getTime()) >= (new Date(recordData['end_expiry_time']).getTime())) {
            $.showAlert(mainLang('instrument_reminder_tip25'));
            return false
        }

        if(hasEmptyRequire){ // 有空的必填项
            return;
        }

        recordData['operation_time'] = $(this).attr('data-operationTime');
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/submit-add-check-record',
            data: {
                recordData:recordData,
                instrumentId: $(this).attr('data-id'),
                type: $(this).attr('data-type'), // addCheckRecord editBeforeEndCheckRecord editAfterEndCheckRecord
                runningId: $(this).attr('data-repairRecordId'), // 编辑记录需要记录id
                orderNumber: $(this).attr('data-orderNumber'), // 编辑记录需要记录的序号
                checkIds: $(this).attr('data-checkIds') // 编辑记录需要记录的序号
            },
            success: function (data) {
                if (data.status == 1) {
                    $(".instrument_add_check_record_page").remove();
                    $('body').removeClass('modal-open');
                    if (type === 'addCheckRecord') {
                        $.showAlert(mainLang('instrument_add_success'));
                    } else {
                        $.showAlert(mainLang('edit_running_success'));
                    }
                    require('tab').reloadActiveTag();  // 再次刷新校验记录页面

                }
            },
        })
    });

    // add by hkk 2020/7/20  校验记录页面新增列
    $('body').on('click', '.addCheckRecordCol', function () {

        //计算当前已添加的自定义列索引 文件列前的th的index
        var instrumentId = $(this).attr('data-id');
        var extendFields = $('.exp_conetnt.active .instruments_check_record_table th[data-field*="extend_field"]');
        //var fileTh = $('.exp_conetnt.active .instruments_check_record_table th[data-field="45"]');
        //var defineColumnField = (parseInt(fileTh.prev('th').attr('data-field')) + 2).toString(); // 加2 是由于有效期占两列
        var defineColumnField = extendFields.length + 1;


        if (parseInt(defineColumnField) > 50) {
            $.showAlert(mainLang('instrument_reminder_tip10'));
            return
        }

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/add-check-record-column',
            data: {
                field:defineColumnField,
                instrumentId: instrumentId,
            },
            success: function (data) {
                if (data.status == 1) {
                    // 再次刷新维修记录页面  // require('tab').reloadActiveTag();
                    $.ajaxFn({
                        type: 'post',
                        url: ELN_URL + '?r=instrument/get-check-record-page',
                        data: {
                            needUpdateAllPage: "no",
                            instrumentId:instrumentId,
                        },
                        success:function(data){
                            if (data.status === 1) {

                                $('.exp_conetnt.active .instruments_check_record_table').html(data.data.file);
                                // 激活刚增加的列标题编辑
                                var head = $('.exp_conetnt.active .instruments_check_record_table th[data-field_="'+ defineColumnField + '"]')
                                $('.instrument_title_input', head).show().focus();
                                $('.instrument_title_input', head).select();
                                $('.instrument_ext_title', head).hide();
                            }
                        },
                    });

                  /*  // 找到文件列索引矫正显示隐藏列缓存
                    var fileThNumber = fileTh.index() + 1;
                    var listCols = localStorage.getItem('instruments_check_record_table_' + instrumentId + '_showHideColumn_cols_index');
                    if (listCols) {
                        var listArray = listCols.split(',');
                        for (var i = 0; i < listArray.length; i++) {
                            if(parseInt(listArray[i],10) >= fileThNumber){
                                listArray[i] = (parseInt(listArray[i],10) + 1).toString()
                            }
                        }
                        localStorage.setItem('instruments_check_record_table_' + instrumentId + '_showHideColumn_cols_index', listArray);
                    }
*/
                }
            },
            noLoad:true,
        })

    });

    // add by hkk 2020/7/21  校验记录页面复核事件
    $('body').on('click', '.checkRecordDoubleCheck', function () {

        // 获取选中的维修记录id
        var chooseIds = [];
        $('.instruments_check_record_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).attr('data-checkRecordId'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip12'));
            return
        }

        var html = '<div class="instrument_check_record_check">  ' +
            '<li class="user_count"><label>'+ mainLang('account')  + ':</label><input type="text" /></li>' +
            '<li class="user_password"><label>'+ mainLang('password')  + ':</label><input type="password" /></li>' +
            '<li class="check_result"><label>'+ mainLang('repair_conclusion')  + ':</label><span><input type="radio" name="check_result_radio" checked value="1" />' +mainLang('allow') +'</span><span><input type="radio" name="check_result_radio" value="2" />' + mainLang('refuse') +'</span></li>' +
            '<li class="check_reason"><label >'+ mainLang('repair_reason')  + ':</label><textarea > </textarea></li>' +
            '</div>';

        $.popContent(html, mainLang('review'), function() {

            var userAccount = $('.instrument_check_record_check .user_count input').val();
            var userPassword = hex_md5($('.instrument_check_record_check .user_password input').val());
            var checkResult = $('.instrument_check_record_check .check_result input:checked').val();
            var checkReason = $('.instrument_check_record_check .check_reason textarea').val();

            if (userAccount === '') {
                $.showAlert(mainLang('instrument_reminder_tip13'));
                return
            }
            if (userPassword === '') {
                $.showAlert(mainLang('pass_must'));
                return
            }
            if (checkReason.trim() === '') {
                $.showAlert(mainLang('instrument_reminder_tip14'));
                return
            }

            // 更改数据库
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/check-repair-record',
                data: {
                    chooseIds: chooseIds,
                    userAccount:userAccount,
                    userPassword:userPassword,
                    checkResult:checkResult,
                    checkReason:checkReason,
                },
                noLoad:true,
                success: function (data) {
                    if (data.status == 1) {
                        require('tab').reloadActiveTag(); // 刷新页面
                        $(".instrument_check_record_check").parents('.pop_modal').modal('hide'); // 隐藏弹框
                        $.showAlert(mainLang('review_success'));
                    }
                },
            })

        });

        return


    });

    // add by hkk 2020/7/21  校验记录页面导出事件
    $('body').on('click', '.checkRecordExport', function() {

        // 记录显示的导出列
        var recordArray = [];
        var th = $('.exp_conetnt.active .instruments_check_record_table').find('tr th');
        th.each(function(index, item) {
            if ($(this).attr('data-field') && $(this).is(":visible")) {
                recordArray.push($(this).attr('data-field'))
            }
        });

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-check-record-export',
            data: {
                recordArray:recordArray,
                instrumentId: $(this).attr('data-id'),
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instrumentsCheckRecord.xlsx&file_name=instrumentsCheckRecord.xlsx";
                }
            }
        });
    });

    // add by hkk 2021/4/30  校验页面点击记录本页码能打开实验标签页
    $('body').on('click',
        '.instrument_operate_record_page .exp_number, .instrument_check_record_page .exp_number, .instrument_repair_record_page .exp_number , .instrument_record_page .exp_number',
        function () {
            var batchNum = $(this)[0].innerText.trim();
            if (!batchNum) {
                return;
            }
            // batchNum以BN开头，认为是库存商品，打开库存系统
            if (batchNum.indexOf('BN') === 0) {
                window.open(INVENTORY_URL + '?key=' + batchNum);
                return;
            }
            if (!batchNum.match(/(.*\d{6,}-\d{3})(.*)/)) {
                $.showAlert(mainLang('some_tip'));
                return;
            } else {
                batchNum = batchNum.match(/(.*\d{6,}-\d{3})(.*)/)[1]
            }
            const instrumentRecordPage = $(this).closest('.instrument_record_page');
            $.ajaxFn({
                noLoad: true,
                url: ELN_URL + '?r=experiment/get-exp-id-by-exp-num',
                type: 'GET',
                data: {
                    exp_num: batchNum
                },
                success: function (res) {
                    if (res.status === 1) {
                        if (res.data) { // 获取到了非空id
                            //如果是审核的弹窗，关闭弹窗后打开实验
                            if (instrumentRecordPage.length){
                                instrumentRecordPage.find('.modal-footer button').trigger('click');
                            }
                            require(['get_html'], function (get_html) {
                                get_html.genExpPage(res.data)
                            });
                        } else {
                            $.showAlert(mainLang('exp_not_exist_tip'));
                        }
                    }
                }
            });
        })


    // add by hkk 2020/4/29 // 痕迹单独做
    $('body').on('click', '.operateInstrumentTrace', function () {
        instrumentApi.insId = $(this).attr('data-id');
        require('get_html').genInstrumentTracePage(instrumentApi.insId);

    });

    // 痕迹搜索
    $('body').on('click', '.search-instrument-trace', function() {
        instrumentApi.pageTrace = 1;
        instrumentApi.insId = $(this).attr('data-id');
        instrumentApi.openInstrumentTrace(1,"no")
    });

    // 痕迹导出
    $('body').on('click', '.operateInstrumentTraceExport', function() {
        // 搜索条件
        instrumentApi.insId = JSON.parse($('.exp_title .tag.on').attr('data-funcparams')).join('');
        var data = {
            instrumentId: instrumentApi.insId,
            action_details:$('.exp_conetnt.active .instrument_trace_page .operate_detail').val(), // 详情搜索
            action:$('.exp_conetnt.active .instrument_trace_page #instrument_trace_type').val(), // 类别筛选
            create_by:$('.exp_conetnt.active .instrument_trace_page input[name=instrument_trace_user]').attr('idbox'), // 创建人筛选
            start_time:$('.exp_conetnt.active .instrument_trace_page #start_time').val(),
            end_time:$('.exp_conetnt.active .instrument_trace_page #end_time').val(),
        };

        window.location.href = ELN_URL + '?r=instrument/instruments-trace-export&instrumentId=' + data.instrumentId + "&action_details=" + data.action_details + "&action=" + data.action+ "&create_by=" + data.create_by+ "&start_time=" + data.start_time+ "&end_time=" + data.end_time;

    });

    $('body').on('click', '.reset-instrument-manage', function () {
        var topFilterBox = $('.exp_conetnt.active .ineln_title1.clear');
        // 触发所有多选选择框的clear删除按钮
        var clearCheckBtns = topFilterBox.find('.visible_user_clear_check_btn');
        clearCheckBtns.each(function() {
            $(this).click();
        });
        // 清空选择框
        var nameArr = ['instrument_status','instrument_type','instrument_check','instrument_data_type'];
        nameArr.forEach(function (item) {
            var curnode = $(`.exp_conetnt.active .ineln_title1 [name=${item}] option:selected`);
            curnode.removeAttr('selected');
            curnode.parent().prev().text(curnode.parent().find('option:first').text());
        });
        //部门重置
        topFilterBox.find('.clear_select').click();
        //鹰群重置
        topFilterBox.find('.clear_check_btn').click();
        // 清空文本框
        var textNameArr = ['instrument_name', 'instrument_position', 'instrument_remark', 'instrument_supplier', 'instrument_manufacturer', 'start_time', 'end_time'];
        textNameArr.forEach(function (item) {
            $(`[name=${item}]`, topFilterBox).val('');
        });

        // 重新查找
        topFilterBox.find('.search-instrument-manage').click();
    });

    $('body').on('click','.inscada_summary_reset_btn',function (){
        var topFilterBox = $('.exp_conetnt.active .ineln_title1.clear');

        // 触发所有多选选择框的clear删除按钮
        var clearCheckBtns = topFilterBox.find('.visible_user_clear_check_btn');
        clearCheckBtns.each(function() {
            $(this).click();
        });

        $(".search-inscada-box select[name='status']").val('1');
        $(".search-inscada-box .inscada-summary-filter-instrument .fs-options").children().first().click(); // 仪器重置
        $(".search-inscada-box input[name='keyword']").val('');
        $(".search-inscada-box input[name='start_time']").val('');
        $(".search-inscada-box input[name='start_time']").datetimepicker('setEndDate', '');
        $(".search-inscada-box input[name='end_time']").val('');
        $(".search-inscada-box input[name='end_time']").datetimepicker('setStartDate', '');
        $('.exp_conetnt.active .filter_unclaimed').prop('checked', true); // 重置不看他人数据
        // 清空文字框后进行一次搜索点击，以将搜索列表重置为全部
        $('.exp_conetnt.active .search-inscada-summary').trigger('click');
    });

    // 绑定仪器库搜索提交事件 add by hkk 2019/10/30
    $("body").on('click', '.search-instrument-manage', function (event) {

        // 获取查询条件和页数信息
        var box = $(".exp_conetnt.active #search-instrument-manage");
        var data = $.formSerializeFn(box);
        data['instrument_create_user'] = box.find('input[name=instrument_create_user]').attr('idbox');
        data['instrument_maintenance_user'] = box.find('input[name=instrument_maintenance_user]').attr('idbox');
        data['instrument_in_charge_user'] = box.find('input[name=instrument_in_charge_user]').attr('idbox');
        data['instrument_responsible_user'] = box.find('input[name=instrument_responsible_user]').attr('idbox');
        data['page'] = 1;
        data['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
        data['needUpdateAllPage'] = 0;
        data['type'] = $(this).attr('data-type');

        data['instrument_belong_group'] = $('.exp_conetnt.active #search-instrument-manage input[name=group_ids]').attr('idbox');
        data['instrument_belong_department'] = $('.exp_conetnt.active #search-instrument-manage input.dept_select').attr('data'); // add by hkk 2022/7/11

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/manage',
            data: data,
            type: 'POST',
            success: function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');

                    $('.exp_conetnt.active .instruments_table').html(data.data.file);

                }
            }
        });
    });

    // 搜索仪器
    function searchInstrumentManage() {
        var searchInstrument = $('.exp_conetnt.active .ineln_title1 .search-instrument-manage');
        if (searchInstrument) {
            searchInstrument.click();
        }
    }
    // inscada 搜索功能
    $("body").on('click', '.search-inscada', function (event) {
        // 获取查询条件和页数信息
        var data, box;
        if(this.className.indexOf('popup-search')!==-1) {
            box = $(this).closest(".instrument_insert_model").find(".search-inscada-box");
        }else{
            box = $(this).closest(".search-inscada-box");
        }

        data = $.formSerializeFn(box);
        data['page'] = 1;
        data['limit'] = $('.exp_conetnt.active .page_box').data('limit') || 15;
        data['dataType'] = box.attr('data-type');
        data['instrumentId'] = box.attr('data-id');
        data['batch_number'] = box.data('batch_number');
        data['needUpdateAllPage'] = '0';
        data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
        if(this.className.indexOf('popup-search')!==-1)
            data['source'] = 'popup';
        console.log(data)
        console.log(typeof data.start_time)
        $.ajaxFn({
            type: 'post',
            url: ELN_URL + '?r=instrument/get-inscada-page',
            data,
            success: function (data) {
                if (1 == data.status) {
                    const modal = $('.instrument-data-modal:visible');
                    if(data.data.source==='popup'){
                        modal.find('.instrument-data-detail').html(data.data.file);
                        modal.find('.inscada_data_page_box').attr('data-limit', data.data.limit).attr('data-num', data.data.total_count);
                        instrumentApi.inscadaManagePageFn('popup');
                    }
                    else{
                        $('.exp_conetnt').removeClass('search');
                        $('.inscada_table:visible').html(data.data.file);

                    }

                }
            }
        });
    });

    // inscada汇总 搜索功能
    $("body").on('click', '.exp_conetnt.active .search-inscada-summary', function() {
        // 获取查询条件和页数信息
        var box = $(".exp_conetnt.active .search-inscada-box");
        var data = $.formSerializeFn(box);
        data['page'] = 1;
        data['limit'] = $('.exp_conetnt.active .page_box').data('limit') || 15;
        data['dataType'] = box.attr('data-type');
        data['needUpdateAllPage'] = '0';
        data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
        data['numerical_instrument_type'] = $(".exp_conetnt.active .top-group-tabs li.on").attr('data-numerical-type');
        if ($(this).hasClass('my-instruments')){
            data['inscada_type'] = 'my-instruments';
        }
        console.log(data)
        $.ajaxFn({
            type: 'post',
            url: ELN_URL + '?r=instrument/get-inscada-summary-page',
            data,
            success: function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    $('.exp_conetnt.active .inscada_table').html(data.data.file);
                }
            }
        });
    })



    // inscada汇总 图表
    $("body").on('click', '.exp_conetnt.active .inscada-chart', function() {
        //dataType用来区分是数值类还是文件类仪器
        var dataType = $(this).attr('data-type');
        require('get_html').genInscadaGraphPage(dataType);
    })

    // inscada 更多筛选
    $('body').on('click', '.inscada_need_more_filter', function () {

        if($('.exp_conetnt.active .inscadaMoreFilter').hasClass('hide')){
            $('.exp_conetnt.active .inscadaMoreFilter').removeClass('hide')
        }else{
            $('.exp_conetnt.active .inscadaMoreFilter').addClass('hide')
        }
    });

    // inscada 签字原因
    $('body').on('input', 'textarea[name="instrument_sign_remark"]', function () {

        instrumentApi.updateCharacterCount();
    });

    // inscada 操作列 //! Inscada插入仪器数据
    $('body').on('click', '.operateInscadaBtn', function () {
        var type = $(this).attr('data-type');  // receive edit delete history
        var inscadaId = [];
        inscadaId.push($(this).attr('data-id'));
        var dataType = $(this).parents('tr').attr('data-data-type');

		// 实验页领取数据不需要验证用户名密码 jiangdm 2022/11/4
        if (type == 'instant_receive') {
            var config = $(this).attr('data-insert-type');
            insertInstrumentData('receive', $(this).closest('tr'), config);
            return;
        } else if (type == 'instant_receive_file') {
            insertInstrumentFile($(this).closest('tr'));
            return;
        }
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-inscada-operate-page',
            data: { type, inscadaId, dataType },
            success: function(res) {
                if (res.status === 1) {
                    if ((type == 'receive' || type == 'batch_receive') && dataType == 1) {
                        $.showAlert(mainLang('data_claimed') + '(' + res.data.name + '(' + res.data.real_name + ')' + ')');
                        $('.exp_conetnt.active .current.page-btn').trigger('click')
                    }
                    if ($('.inscada_operate_page').length == 0) {
                        $('body').append(res.data.file);
                    }
                    $('.inscada_operate_page').prop('data-info', JSON.stringify({type, inscadaId, dataType}));
                    $('.inscada_operate_page').modal('show');
                    $('.inscada_operate_page').on('hide.bs.modal', function() {
                        // 如果用户主动关闭对话框，则将对话框销毁
                        if ($(this).attr('data-remove') == 1) $(this).remove();
                    })
                }
            }
        })
    });

    const insertFileToExp = function (res, dataInfo) {
        var refreshBtn = $('.search-inscada:visible');
        if (!$('.inscada_table:visible').attr('data-ins-id')) {
            refreshBtn = $('.search-inscada-summary:visible');
        }
        const state = res.data.receiveState || 0;
        // console.log(state)
        switch (state) {
            case 3:
                // 只领取不上传
                $.popContent(mainLang('inscada_receive_state3'), mainLang('system alert'), $.closeModal);
                refreshBtn.trigger('click');
                $('.inscada_operate_page').remove();
                break;
            case 2:
                // 提示领取成功，点击查看按钮可以跳转到实验详情
                $.popContent(mainLang('inscada_receive_state2'), mainLang('system alert'), $.closeModal, null, null, null, function () {
                    $('.pop_modal_submit').removeClass('exp_detial');
                });
                // 对话框有动画，需要在动画结束后再改按钮的字
                setTimeout(()=>{
                    $('.pop_modal_submit').text(mainLang('show'));
                    $('.pop_modal_submit').addClass('exp_detial').attr('data-id',res.data.expId);
                },500);
                refreshBtn.trigger('click');
                $('.inscada_operate_page').remove();
                addInstrumentFileBlock(res.data.addFilesRes); // 在文件上传模块中显示刚领取的文件
                break;
            case 1:
                // 多个上传模块需要二次确认，暂时隐藏当前表单（data-remove设为0, 不销毁）
                $('.inscada_operate_page').attr('data-remove', 0).hide();
                if ($('.inscada_operate_uploadpage').length == 0) {
                    $('body').append(res.data.file);
                }
                $('.inscada_operate_uploadpage').modal('show');
                $('.inscada_operate_uploadpage').prop('data-info', JSON.stringify({
                    type: dataInfo.type,
                    inscadaId: dataInfo.inscadaId,
                    dataType: dataInfo.dataType
                }));
                $('.inscada_operate_uploadpage').on('hide.bs.modal', function() {
                    $('.inscada_operate_page').remove();
                })
                break;
            case 5:
            case 6:
                $.popContent(mainLang('inscada_receive_state'+state), mainLang('system alert'), false, false, false);
                $('.inscada_operate_page').remove();
                break;
            default:
                $('.inscada_operate_page').remove();
                refreshBtn.trigger('click');
                break;
        }
    }

    // inscada 保存编辑的信息
    $('body').on('click', '.inscada_operate_conform_btn', function () {
        // data_info: {type, inscadaId, dataType}
        var data_info = JSON.parse($('.inscada_operate_page').prop('data-info'));
        var box = $(".inscada_operate_page #inscada_operate_form");
        var data = $.formSerializeFn(box);
        // 如果是签字和编辑需要验证账号密码，领取不需要
        if (data_info['type'] == 'edit' ||
            data_info['type'] == 'delete' ||
            data_info['type'] == 'batch_delete' ||
            data_info['type'] == 'batch_sign' ||
            data_info['type'] == 'instrument_sign') {
            if (!data['username'].length || !data['password'].length) {
                var alert = mainLang('usrname_pwd_required');
                return $.showAlert(alert);
            }

            data['password'] = $.passwordEncipher(data['password']);
        }

        var refreshBtn = $('.search-inscada:visible');
        if (!$('.inscada_table:visible').attr('data-ins-id')) {
            refreshBtn = $('.search-inscada-summary:visible');
        }

        // 编辑、删除、领取
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/update-inscada',
            data: { ...data_info, ...data },
            success: function (res) {
                if (res.status === 1) {
                    // 关闭对话框
                    if (data_info['type'] == 'receive'||data_info['type'] == 'batch_receive') {
                        insertFileToExp(res, data_info);
                    } else {
                        $('.inscada_operate_page').remove();
                        refreshBtn.trigger('click');
                    }
                }
            }
        })
    })

    // inscada 上传文件到选定的模块
    $('body').on('click', '.inscada_operate_uploadpage_conform_btn', function() {
        //
        var moduleId = [];
        $('#inscada_operate_uploadpage_form input:radio.beauty-checkbox-big:checked').each(function() {
            moduleId.push($(this).attr('data-id'));
        })
        console.log(moduleId);
        var fileList = JSON.parse($('.uploadpage_filelist').text());
        console.log(fileList);

        var data_info = JSON.parse($('.inscada_operate_uploadpage').prop('data-info'));
        console.log(data_info)
        var box = $(".inscada_operate_page #inscada_operate_form");
        if (box.length > 0) {
            var data = $.formSerializeFn(box);
        } else {
            var data = {
                exp_pages: $('.exp_title .tag.on .name').text(),
                remark: '',
            }
        }

        var expId = $('.inscada_operate_uploadpage').attr('data-id');
        console.log({ ...data_info, ...data, moduleId, fileList, expId })
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/insert-file-to-module',
            data: { ...data_info, ...data, moduleId, fileList, expId },
            success: function (res) {
                if (res.status == 1) {
                    $('.exp_conetnt.active .search-inscada').trigger('click');
                    $('.inscada_operate_uploadpage').modal('hide');
                    $.popContent(mainLang('inscada_receive_state2'), mainLang('system alert'), function () {
                        $('.pop_modal').modal('hide')
                    });
                    // 对话框有动画，需要在动画结束后再改按钮的字
                    setTimeout(()=>{
                        $('.pop_modal_submit').text(mainLang('show'));
                        $('.pop_modal_submit').addClass('exp_detial').attr('data-id',res.data.expId);
                    },500);
                    $('.exp_conetnt.active .search-inscada').trigger('click');
                    $('.inscada_operate_page').remove();
                    addInstrumentFileBlock(res.data.addFilesRes);
                }
            }
        })

    })

    // inscada 仪器数据 批量删除 批量领取 批量导出
    $('body').on('click', '.inscada_more .group-opt-btn', function() {
        //
        var type = $(this).attr('data-type');  // batchReceive batchExport batchDelete
        var dataType = $('.exp_conetnt.active .inscada_table').attr('data-data-type');

        // 获取选中的仪器记录id
        var inscadaId = [];
        $('.exp_conetnt.active .inscada_table input:checkbox.checkboxBtn:checked').each(function() {
            inscadaId.push($(this).parents('tr').attr('data-id'));
        });
        // 批量导出
        if (type == 'batch_export') {
            // 获取查询条件和页数信息
            var box = $(".exp_conetnt.active .search-inscada-box");
            var data = $.formSerializeFn(box);
            data['instrumentId'] = $('.exp_conetnt.active .inscada_table').attr('data-ins-id');
            data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
            data['numerical_instrument_type'] = $('.exp_conetnt.active .top-group-tab.eln_setting_btn.on').attr('data-numerical-type')
            console.log({ inscadaId, ...data, dataType })
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/export-inscada',
                data: { inscadaId, ...data, dataType },
                success: function(res) {
                    if (res.status === 1) {
                        // window.location.href = ELN_URL + "?r=download/file&path=&name=inscada.xlsx&file_name=inscada.xlsx";
                        var $a = $("<a>");
                        $a.attr("href", res.data.file);
                        $("body").append($a);
                        $a.attr("download", res.data.name);
                        $a[0].click();
                        $a.remove();

                    }
                }
            })
            return
        }
        // 其它批量操作
        if (inscadaId.length === 0) {
            $.showAlert(mainLang('please_choose_data'));
            return
        }
        // console.log({ type, inscadaId, dataType });
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-inscada-operate-page',
            data: { type, inscadaId, dataType },
            success: function(res) {
                if (res.status === 1) {
                    if ($('.inscada_operate_page').length == 0) {
                        $('body').append(res.data.file);
                    }
                    $('.inscada_operate_page').prop('data-info', JSON.stringify({type, inscadaId, dataType}));
                    $('.inscada_operate_page').modal('show');
                    $('.inscada_operate_page').on('hide.bs.modal', function() {
                        // 如果用户主动关闭对话框，则将对话框销毁
                        if ($(this).attr('data-remove') == 1) $(this).remove();
                    })

                    // 如果是数值类那么就直接返回
                    if(type == 'batch_receive' && dataType == 1) {
                        $.showAlert(mainLang('data_claimed'));
                    }
                }
            }
        })

    })

    // 维保页面筛选搜索
    $('body').on('change', '.repair_type_filter', function() {
        var that = this;
        var repair_type = $(".exp_conetnt.active .instrument_repair_record_page select[name='repair_type']").val();
        var data = {
            instrumentId: instrumentApi.insId || $('.exp_conetnt.active .addRepairRecord').attr('data-id'),
            needUpdateAllPage:"no",
            page: 1,
            limit:$('.exp_conetnt.active .pager-select:visible').val() || that.default_page_size || undefined,
            repair_type: repair_type
        };

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-repair-record-page',
            data:data,
            success: function (data) {
                if (data.status === 1) {
                    $('.exp_conetnt.active .instruments_repair_record_table').html(data.data.file);
                }
            }
        });
    })

    //三大记录作废
    $('body').on('click', '.cancellation', function () {

        let ajaxData = {};
        ajaxData.instrumentId = $(this).attr('data-id')
        ajaxData.recordType = $(this).attr('data-type')
        let recordId_order = {}
        const checkedBoxs = $('.checkboxBtn:checked')
        //获取记录的id
        if (ajaxData.recordType == 'repair') {
            checkedBoxs.each(function () {
                recordId_order[$(this).attr('data-repairRecordId')] = $(this).attr('data-orderNumber')
            })
        } else if (ajaxData.recordType == 'check') {
            checkedBoxs.each(function () {
                recordId_order[$(this).attr('data-checkRecordId')] = $(this).attr('data-orderNumber')
            })
        } else {
            checkedBoxs.each(function () {
                recordId_order[$(this).attr('data-operateRecordId')] = $(this).attr('data-orderNumber')
            })
        }
        ajaxData.recordId_order = recordId_order
        const html = `<div style="width: 400px">${mainLang('cancel_record_remind')}</div><br><textarea class="instrument_cancel_record_reason" placeholder="${mainLang('cancel_record_reason')}"></textarea>`
        $.showContent('', mainLang('cancellation'), html, function () {
            ajaxData.cancelReason = $('.instrument_cancel_record_reason').val()
            if (ajaxData.cancelReason) {
                if (!ajaxData.instrumentId || !ajaxData.recordType || !ajaxData.recordId_order) {
                    $.showAlert('lose param')
                    return
                }
                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/cancel-record',
                    data:ajaxData,
                    success: function (resData) {
                        if (resData.status === 1) {
                            $.closeModal();
                            require('tab').reloadActiveTag(); // 刷新页
                            $.showAlert(mainLang('cancel_successful_tip'));
                        } else {
                            $.showAlert(resData.info);
                            setTimeout(() => {
                                $.closeModal();
                                require('tab').reloadActiveTag(); // 刷新页
                            },2600)
                        }
                    }
                })
            } else {
                $.showAlert(mainLang('cancel_record_reason'));
            }
        })

    })

    //三大记录恢复
    $('body').on('click','.recoverRecord', function() {
        let ajaxData = {};
        ajaxData.instrumentId = $(this).attr('data-id')
        ajaxData.recordType = $(this).attr('data-recordType')
        ajaxData.recordId = $(this).attr('data-repairrecordid')
        ajaxData.orderNumber = $(this).attr('data-orderNumber')
        if (!ajaxData.instrumentId || !ajaxData.recordType || !ajaxData.recordId || !ajaxData.orderNumber) {
            $.showAlert('lose param')
            return
        }
        const html = `<div style="width: 400px">${mainLang('recover_record_remind')}</div>`
        $.showContent('', mainLang('tip_'), html, function () {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/recover-record',
                data: ajaxData,
                success: function (resData) {
                    if (resData.status === 1) {
                        require('tab').reloadActiveTag(); // 刷新页
                    } else {
                        if (resData.data.tipType == 'popContent') {
                            $.popContent(resData.info, `<div style="color: red">${mainLang('failed_to_recover')}</div>`, null, null, false)
                        } else {
                            $.showAlert(resData.info)
                        }
                    }
                }
            })
        })
    })

    //作废按钮禁用及显示
    $('body').on('click','.instrument_record_table input[type="checkbox"]',function () {
        const cancelBot = $(this).closest('.exp_data_box').find('.together-tools .eln_setting_btn .cancellation')
        if ($(this).attr('id') == 'checkAll') {
            if ($(this).prop('checked')) {
                cancelBot.removeClass('disabled')
            } else {
                cancelBot.addClass('disabled')
            }
        } else {
            if ($(this).closest('.instrument_record_table').find('.checkboxBtn:checked').length) {
                cancelBot.removeClass('disabled')
            } else {
                cancelBot.addClass('disabled')
            }
        }
    })

	// 弹出仪器数据弹窗 jiangdm 2022/11/4
    window.loadInstrumentPopup = function (instrumentInfo = null) {
        if ($('.instrument-data-modal:visible').length <= 0) {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-available-instrument-list-for-insert',
                type: 'POST',
                success: function (res) {
                    $(".instrument-data-modal").remove();
                    $("body").append(res.data.html);
                    $(".instrument-data-modal").modal({backdrop:false});
                    window.instrumentList=res.data.instrumentList;
                    if (instrumentInfo) {
                        loadInstrumentData(instrumentInfo); // 如果是通过点击已插入数据弹出的，需要加载数据
                    }
                }
            });
        } else {
            if (instrumentInfo){
                loadInstrumentData(instrumentInfo);
            }
        }
    }

    // 保存选择仪器历史记录 jiangdm 2022/11/4
    const saveInstrumentSelectHistory = function (instrumentId) {
        let historyInstruments = localStorage.getItem('history_insert_instruments');
        historyInstruments = historyInstruments ? historyInstruments.split(',') : [];
        //加toString点已插入数据会报错，不加的话在localStorage中去重无效,现在已经没问题了 zwm 2023/1/7
        const historyIndex = historyInstruments.indexOf(instrumentId.toString());
        // const historyIndex = historyInstruments.indexOf(instrumentId);

        if (historyIndex > -1) {
            historyInstruments.splice(historyIndex, 1);
        }
        historyInstruments.unshift(instrumentId);
        localStorage.setItem('history_insert_instruments', historyInstruments.slice(0, 10).join(','));
        }

    // 加载仪器数据 jiangdm 2022/11/4
    const loadInstrumentData = function (dataInfo) {
        const modal = $('.instrument-data-modal:visible');
        modal.find('.instrument-select-box').addClass('hidden');
        const instrumentType = dataInfo.instrument_type;
		// 根据仪器类型切换字段配置选项
        if (instrumentType == '1') {
            modal.find('.instrument-data-fields')
                .find('.instrument-fields-file').addClass('hidden').end()
                .find('.instrument-fields-data').removeClass('hidden');
        } else {
            modal.find('.instrument-data-fields')
                .find('.instrument-fields-data').addClass('hidden').end()
                .find('.instrument-fields-file').removeClass('hidden');
        }
        //高级搜索选项
        var box = modal.find(".search-inscada-box");
        var data = $.formSerializeFn(box);
        data['batch_number']=dataInfo.batch_number;
        data['data_id']=dataInfo.data_id;
        data['dataType']=instrumentType;
        data['source']='popup';
        data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
        // 查询仪器数据
        $.ajax({
            url: ELN_URL + '?r=instrument/get-inscada-page',
            type: 'post',
            data,
            success: function(res) {
                const instrumentInfo = res.data.ins_info;
                const instrumentId = instrumentInfo.id;
                modal.find('.instrument-data-detail').html(res.data.file);
                modal.find('.inscada_data_page_box').attr('data-limit', res.data.limit).attr('data-num', res.data.total_count);
                instrumentApi.inscadaManagePageFn('popup');
                modal.find('.instrument-select').val(instrumentInfo.name+'('+instrumentInfo.batch_number+')').data('instrument_id', instrumentId)
                    .data('batch_number', instrumentInfo.batch_number).data('instrument_type', instrumentInfo.data_type);
                modal.find('.search-inscada-box').attr('data-type', instrumentType).attr('data-id', instrumentId).attr('data-batch_number', dataInfo.batch_number);//bug#31546，修改弹框中box的selector mod dx
                saveInstrumentSelectHistory(instrumentId);
                initInstrumentDataFields({data_type: instrumentType});
                // 弹窗显示隐藏列替换
                if (instrumentType == 1) {
                    modal.find('.inscada-show-hide-fields .filename').addClass('hidden');
                    modal.find('.inscada-show-hide-fields .numerical_value').removeClass('hidden');
                } else if (instrumentType == 2) {
                    modal.find('.inscada-show-hide-fields .filename').removeClass('hidden');
                    modal.find('.inscada-show-hide-fields .numerical_value').addClass('hidden');
                }
            }
        })
    }

    // 点击仪器插入数据字段选项，更新保存的仪器插入数据字段配置
    let instrument_insert_data_selector = '.modal:visible .instrument_fields_warp .instrument_data_fields_wrap input[type=checkbox]'
    $('body').on('click', instrument_insert_data_selector, function (e) {
        let inst_data_type = $('.modal:visible .search-inscada-box').attr('data-type');
        if (!inst_data_type) {
            return;
        }

        let $checked_inst_input = $('.modal:visible .instrument_fields_warp .instrument_data_fields_wrap input[type=checkbox]:checked:visible');
        let checked_values = [];//已选中的数据
        $checked_inst_input.each(function (idx, ele) {
            checked_values.push($(ele).val());
        });
        let checked_inst_data = localStorage.getItem(LOCAL_INSTRUMENT_INS_FIELDS);
        let inst_data = {};
        if (checked_inst_data) {
            inst_data = JSON.parse(checked_inst_data);
        }
        switch (Number(inst_data_type)) {//INST_DATA_TYPE中为数字，switch时先转换
            case INST_FIELDS_TYPE.DATA://仪器插入数据为数值类型
                inst_data[INST_FIELDS_TYPE.DATA_KEY] = checked_values
                break;
            case INST_FIELDS_TYPE.FILE://插入数据为文件类型
                inst_data[INST_FIELDS_TYPE.FILE_KEY] = checked_values;
                break;
        }
        localStorage.setItem(LOCAL_INSTRUMENT_INS_FIELDS, JSON.stringify(inst_data));
    });

    // 插入仪器数据 jiangdm 2022/11/4
    const insertInstrumentData = function(type, dataItem, config) {
        // 获取并验证要插入的元素
        const selectInfo = window.instrumentDataBox;
        const currentTime = (new Date()).getTime();
        let dataId, instrumentType;
        if (type == 'receive') {
            dataId = dataItem.data('id');
            instrumentType = dataItem.data('data-type');
        } else {
            dataId = dataItem['id'];
            instrumentType = '1';
        }
        if (!selectInfo) {
            $.showAlert(instrumentType == '1' ? mainLang('instrument_data_num_locate_error') : mainLang('instrument_data_file_locate_error'));
            return;
        }
        if ((currentTime - selectInfo.time) > 30000) {
            window.instrumentDataBox = null;
            $.showAlert(mainLang('instrument_data_timeout_error'));
            return;
        }
        const selectBox = selectInfo.box;
        if (!selectBox || !$(selectBox).is(':visible')) return;
        const modal = $('.instrument-data-modal:visible');
        const instrument = modal.find('.instrument-select');
        const batchNumber = instrument.data('batch_number');
        const instrumentName = instrument.val();

        /** @type {InsertInstrumentDataDetail} 插入数据的详情数据 */
        const insertDataDetail = {
            batch_number: batchNumber || undefined,
            instrument_name: instrumentName || undefined,
            raw_data: dataItem.data('raw_data') || undefined,
            numerical_value: dataItem.data('numerical_value') || undefined,
            unit: dataItem.data('unit') || undefined,
            filename: dataItem.find(`[data-field="filename"]`).text().trim() || undefined,
        };
        // 组装要插入的文本
        const hasDetail = modal.find('.instrument-fields-data.raw_data input')[0].checked
        let insertValue = '';
        let basicValue = '';
        modal.find('.instrument-data-fields div').not('.hidden').find('input:checked').each(function () {
            const field = $(this).val();
            if (field != 'numerical_value' && field != 'unit') insertValue += hasDetail ? '\n' : ' ';
            if (field == 'batch_number') {
                insertValue += batchNumber;
            } else if (field == 'instrument_name') {
                insertValue += instrumentName;
            } else if (field == 'raw_data') {
                insertValue += dataItem.data('raw_data')
            } else if (field == 'numerical_value') {
                basicValue += dataItem.data('numerical_value');
            } else if (field == 'unit') {
                basicValue += dataItem.data('unit');
            } else if (field == 'filename') {
                basicValue += dataItem.find(`[data-field="${field}"]`).text().trim();
            } else {
                if (type == 'receive') {
                    insertValue += dataItem.find(`[data-field="${field}"]`).text().trim();
                } else {
                    insertValue += dataItem[field];
                }
            }
        });
        insertValue = basicValue + (insertValue == '' ? '' : `(${insertValue})`);
        const dataInfo = {
            value: insertValue,
            data_id: dataId,
            instrument_type: instrumentType,
            batch_number: batchNumber,
            insertDataDetail,
        }
        switch (selectInfo.type) {
            case 'define_table':
				// 自定义表格、物料表
                // 如果是物料表这种只能插入数字的元素，无论设置插入多少信息都只插入数值
                if (
                    selectBox[0].tagName.toLowerCase() === 'input' &&
                    selectBox[0].type.toLowerCase() === 'number'
                ) {
                    if (instrumentType == '1') {
                        insertValue = dataItem.data('numerical_value');
                    } else {
                        $.showAlert(mainLang('inscada_file_error'));
                        return;
                    }
                }


                if (selectBox.prop('tagName') !== 'BODY') {
                    selectBox.val(insertValue).addClass('instrument-data-input').data('data_id', dataId).data('batch_number', batchNumber).data('instrument_type', instrumentType);
                    selectBox.trigger('change', ['new']);
                }

                /// 新版物料表的单元格通过调用特定的参数进行插入
                /// 插入物料表的数据只能使用纯数字
                const insertMaterialNumStr = String(dataItem.data('numerical_value') ?? '');
                const isValidMaterialNum = insertMaterialNumStr.match(/^\d+(\.\d+)?$/g); // 插入物料表的只能是纯数值的字符串
                if (selectInfo?.materialParams && isValidMaterialNum) {
                    const instParams = {
                        newVal: insertMaterialNumStr,
                        instDataInfo: {mass: dataInfo},
                    };
                    selectInfo.materialParams.insertInstrumentData(instParams);
                }
                break;
            case 'intable':
                if (hasDetail) {
                    insertValue = insertValue.split('\n').reduce((str, val) => `${str}<div>${val}</div>`, '')
                }

                const data = {
                    'config': config,
                    'dataInfo': [dataInfo]
                };
                if (selectBox.contentWindow.xs) {
                    selectBox.contentWindow.xs.sheet.setInstrumentDataBatch(data);
                }
                else {
                    selectBox.contentWindow.setInstrumentDataBatch(data);
                }
                break;
            case 'ueditor':
                // 文本编辑器
                handleUEditorData([dataInfo], selectBox, hasDetail, [], config);
                break;
        }
		// 保存领取详情
        $.ajax({
            url: ELN_URL + '?r=instrument/update-inscada',
            type: 'post',
            data: {
                type: 'instant_receive',
                inscadaId: [dataId],
                dataType: instrumentType,
                exp_pages: $('.exp_title .tag.on .name').text(),
                remark: '',
            },
            success: function() {
                $.showAlert(mainLang('insert_data_success'));
            }
        })
    }

    const insertInstrumentDataBatch = function(config) {
        // 获取并验证要插入的元素
        const selectInfo = window.instrumentDataBox;
        const currentTime = (new Date()).getTime();
        if ((currentTime - selectInfo.time) > 30000) {
            window.instrumentDataBox = null;
            $.showAlert(mainLang('instrument_data_timeout_error'));
            return;
        }
        if(selectInfo.type !== 'intable' && selectInfo.type !== 'ueditor'){
            $.showAlert(mainLang('inscada_batch_insert_tip'));
            return;
        }
        const selectBox = selectInfo.box;
        if (!selectBox || !$(selectBox).is(':visible')) return;
        const modal = $('.instrument-data-modal:visible');
        const instrument = modal.find('.instrument-select');
        const batchNumber = instrument.data('batch_number');
        const instrumentName = instrument.val();
        let dataIdArray = [];
        let dataInfoArray = [];
        const hasDetail = modal.find('.instrument-fields-data.raw_data input')[0].checked;
        $('.checkboxBtn:checked').each(function() {
            // 使用 closest('tr') 获取最近的父级表格行
            let dataItem = $(this).closest('tr');
            let dataId, instrumentType;
            dataId = dataItem.data('id');

            instrumentType = dataItem.data('data-type');
            // 组装要插入的文本
            let insertValue = '';
            let basicValue = '';
            modal.find('.instrument-data-fields div').not('.hidden').find('input:checked').each(function () {
                const field = $(this).val();
                if (field != 'numerical_value' && field != 'unit') insertValue += hasDetail ? '\n' : ' ';
                if (field == 'batch_number') {
                    insertValue += batchNumber;
                } else if (field == 'instrument_name') {
                    insertValue += instrumentName;
                } else if (field == 'raw_data') {
                    insertValue += dataItem.data('raw_data');
                } else if (field == 'numerical_value') {
                    basicValue += dataItem.data('numerical_value');
                } else if (field == 'unit') {
                    basicValue += dataItem.data('unit');
                } else if (field == 'filename') {
                    basicValue += dataItem.find(`[data-field="${field}"]`).text().trim();
                } else {
                    insertValue += dataItem.find(`[data-field="${field}"]`).text().trim();
                }
            });
            insertValue = basicValue + (insertValue == '' ? '' : `(${insertValue})`);
            if (selectInfo.type == 'intable' && hasDetail) {
                insertValue = insertValue.split('\n').reduce((str, val) => `${str}${val}`, '')
            }
            let dataInfo = {
                value: insertValue,
                data_id: dataId,
                instrument_type: instrumentType,
                batch_number: batchNumber,
            }
            dataInfoArray.push(dataInfo);
        });
        switch (selectInfo.type) {
            case 'intable':
                var data = {
                    'config': config,
                    'dataInfo':dataInfoArray
                }
                dataIdArray = dataInfoArray.map(dataInfo => dataInfo.data_id);
                if (selectBox.contentWindow.xs) {
                    selectBox.contentWindow.xs.sheet.setInstrumentDataBatch(data);
                }
                else {
                    selectBox.contentWindow.setInstrumentDataBatch(data);
                }
                break;
            case 'ueditor':
                // 文本编辑器
                const editorKey = selectBox.attr('id');
                const editor = UE.getEditor(editorKey);
                for (const dataInfo of dataInfoArray) {
                    // 获取当前选区（光标位置）的 Range 对象
                    const range = editor.selection.getRange();
                    // 获取当前选区的元素
                    let ancestorNode = range.getCommonAncestor();
                    if (ancestorNode.nodeType === Node.TEXT_NODE) {
                        ancestorNode = ancestorNode.parentNode;
                    }
                    let dataHtml;
                    if (UE.dom.domUtils.findParentByTagName(ancestorNode, ["table"])) {
                        // 如果父级能找到table,就是文本编辑器表格内批量插入，否则就是非表格
                        let insertValue = hasDetail ? dataInfo.value.split('\n') : [dataInfo.value];
                        dataHtml = `<p><a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a></p>`

                        UE.getEditor(editorKey).execCommand('insertHtml', dataHtml);
                        dataIdArray.push(dataInfo.data_id);
                        // 移动光标到插入后的位置
                        if (ancestorNode) {
                            // 通过 document 对象查找包含插入内容的 td或th 父节点
                            const parentTd = UE.dom.domUtils.findParentByTagName(ancestorNode, ["td", "th"], true);

                            if (config === 'row') {
                                // 插入到下一个 td
                                const parentTr = parentTd.parentNode;
                                const nextTd = parentTr.querySelector('td:nth-child(' + (parentTd.cellIndex + 2) + ')');
                                if (nextTd) {
                                    range.setStart(nextTd, 0);
                                    range.collapse(true);
                                }else{
                                    // 如果没有下一个格子就退出循环
                                    if(dataInfoArray.length - dataIdArray.length>0){
                                        $.popContent(mainLang('inscada_insert_tip', [dataIdArray.length, dataInfoArray.length - dataIdArray.length]), mainLang('tip'), undefined, undefined, false);
                                    }
                                    break;
                                }
                            } else if (config === 'col') {
                                // 插入到下一个 tr 的同列 td
                                const parentTr = parentTd.parentNode;
                                const nextTr = parentTr.nextElementSibling;
                                if (nextTr) {
                                    const sameColTd = nextTr.querySelector('td:nth-child(' + (parentTd.cellIndex + 1) + ')');
                                    if (sameColTd) {
                                        range.setStart(sameColTd, 0);
                                        range.collapse(true);
                                    }
                                }else{
                                    // 如果没有下一个格子就退出循环
                                    if(dataInfoArray.length - dataIdArray.length>0){
                                        $.popContent(mainLang('inscada_insert_tip', [dataIdArray.length, dataInfoArray.length - dataIdArray.length]), mainLang('tip'), undefined, undefined, false);
                                    }
                                    break;
                                }
                            }
                            range.setStartAfter(ancestorNode).setCursor(true);
                        }
                    } else {
                        let insertValue = hasDetail ? dataInfo.value.split('\n') : [dataInfo.value];
                        if (config === 'row') {
                            dataHtml = `<a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a>`

                            // 如果是最后一个，那么就不加空格
                            if(dataInfoArray.indexOf(dataInfo) !== dataInfoArray.length - 1){
                                dataHtml += '&nbsp;&nbsp;&nbsp;&nbsp;';
                            }
                        } else if (config === 'col') {
                            dataHtml = `<p><a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a></p>`

                        }
                        UE.getEditor(editorKey).execCommand('insertHtml', dataHtml);
                        dataIdArray.push(dataInfo.data_id);
                    }
                }
                break;
        }

		// 保存领取详情
        $.ajax({
            url: ELN_URL + '?r=instrument/update-inscada',
            type: 'post',
            data: {
                type: 'instant_receive',
                inscadaId: dataIdArray,
                dataType: '1',
                exp_pages: $('.exp_title .tag.on .name').text(),
                remark: '',
            },
            success: function() {
                // bug 2284 表格有3格，选4条数据批量插入，同时弹出啊了两个提示信息，应该只弹后面那个，“数据插入成功”的这条信息在这种情况下不弹出
                if (dataInfoArray.length - dataIdArray.length == 0) {
                    $.showAlert(mainLang('insert_data_success'));
                }
            }
        })
    }

    /**
     * 文件上传模块展示刚领取的文件 jiangdm 2022/11/4
     * @param {IReceiveScadaFileAddFilesRes} addFileRes
     */
    const addInstrumentFileBlock = function(addFileRes) {
        const moduleId = addFileRes['moduleArr'][0]['id'];
        const module = $(`.modul_part_id[data-id="${moduleId}"]`).closest('.modul_line');
        addFileRes.fileList.forEach(file => {
            //added by xieyuxiang 2023.1.6 提取文件名后缀
            var fileType = file.filename.replace(/.+\./,"");
            var fileBlock = `<div class="img_part file_up_box border_all iblock vertical-top" fileid="${file.upload_id}">
                                    <div class="border-b nowrap  overhidden file_name editable">
                                        <textarea maxlength="50" style="width: 85%"  title="${file.filename}" oldName="${file.filename}">${file.filename}</textarea>
                                    </div>
                                    <div class="img_box text_center relative">
                                        <img src="/image/file.png">
                                        <span class="openInscada absolute instrument-data-link" style="right: 25px; top: -25px;" data-batch_number="${file.batch_number}" data-data_id="${file.id}" data-instrument_type="2"></span>
                                    </div>
                                    <div class="img_bottom border-t text_center">
                                        <a class="_btn del_file del_ico" title="${mainLang('del')}"></a>
                                        <a class="see_ico preview_file" target="_blank" title="${mainLang('only_access_document_type')}"></a>
                                        <a class="local_preview_ico local_preview" target="_blank" title="${mainLang('local_preview_note')}"></a>
                                        <a class="local_edit_ico local_edit" target="_blank" title="${mainLang('local_edit_note')}"></a>
                                        <a class="download_ico" target="_blank" title="${mainLang('download')}" href="${ELN_URL}/?r=download/download-file-by-id&id=${file.upload_id}"></a>
                                        `;
            if(fileType==='pdf'){
                fileBlock+=`<a class="_btn file_analysis extract_ico">${mainLang('file_analysis')}</a>`;
            }
            fileBlock+=`<input type="hidden" name="dep_path" value="${file.dep_path}">
                        <input type="hidden" name="save_name" value="${file.eln_save_name}">
                        <input type="hidden" name="file_name" value="${file.filename}">
                        </div>
                        </div>`;
            module.find('.picker').not('.re_upload').closest('.img_part').before(fileBlock);
        })

    }

    // 向文件上传模块中插入仪器文件  jiangdm 2022/11/4
    const insertInstrumentFile = function (dataRow) {
        const dataInfo = {
            type: 'instant_receive_file',
            inscadaId: [dataRow.data('id')],
            dataType: '2',
            exp_pages: $('.exp_title .tag.on .name').text(),
            remark: '',
        };
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/update-inscada',
            data: dataInfo,
            success: function(res) {
                if (res.status === 1) {
                    insertFileToExp(res, dataInfo);
                }
            }
        })
    }

    // 初始化仪器对接类型的选项
    function initInstrumentDataFields({data_type}) {
        let local_inst_data = localStorage.getItem(LOCAL_INSTRUMENT_INS_FIELDS);
        if (!local_inst_data || local_inst_data === '{}') {//没有保存过仪器插入的字段，就不初始化
            console.log('no instrument fields in localstorage');
            return;
        }
        let local_inst_fields = JSON.parse(local_inst_data);
        let inst_fields = [];//需要初始化的仪器对接数据字段
        switch (Number(data_type)) {
            case INST_FIELDS_TYPE.DATA://仪器对接类型为数值
                inst_fields = local_inst_fields[INST_FIELDS_TYPE.DATA_KEY] || ['numerical_value'];//如果没有设置过对接数据，设置插入选项的默认值
                break;
            case INST_FIELDS_TYPE.FILE://仪器对接类型为文件
                inst_fields = local_inst_fields[INST_FIELDS_TYPE.FILE_KEY] || ['filename'];
                break;
        }

        let $inst_fields_wrap = $('.modal .instrument_data_fields_wrap');
        $inst_fields_wrap.find('input[type=checkbox]').prop('checked', false);
        for (const inst_field of inst_fields) {//遍历选项，设置选中值
            let $field = $inst_fields_wrap.find(`input[type=checkbox][value=${inst_field}]`);
            $field.prop('checked', true);
        }
    }

    // 仪器数据读取 jiangdm 2022/10/18
    $("body").on('click', '.detect-inscada', function() {
        var instrument_id = $(this).data('id');
        var tagId = $('.exp_title .tag.on').attr('data-id');
        var request_time = (new Date()).getTime();
        var isPopup = $(this).closest('.instrument-data-modal').length > 0;
        $.ajaxFn({
            type: 'post',
            url: ELN_URL + '?r=instrument/read-instrument-data',
            data: { instrument_id, request_time },
            ajaxTimeout: 5000,
            timeOut: function () {
                $.showAlert(mainLang('read_instrument_data_timeout'));
            },
            success: function (res) {
                if (res.status != 0){
                    if (isPopup) {
                        // 来自弹窗的检测，要刷新数据列表，同时插入数据
                        var instrument = $('.instrument-data-modal:visible .instrument-select');
                        const instrumentInfo = {
                            name: instrument.val(),
                            instrument_id: instrument.data('instrument_id'),
                            batch_number: instrument.data('batch_number'),
                            instrument_type: instrument.data('instrument_type'),
                        };
                        insertInstrumentData('detect', res.data.insert_data);
                        loadInstrumentData(instrumentInfo);
                    } else {
                        // 仪器数据详情页，直接刷新整个页面
                        $.showAlert(res.info);
                        require(['tab'], function (tab) {
                            tab.reloadTag(tagId);
                        });
                    }
                } else {
                    $.showAlert(res.info);
                }
            }
        });
    })

    // 选择仪器弹窗切换展示 jiangdm 2022/11/4
    $('body').on('click', '.instrument-select', function() {
        $(this).siblings('.instrument-select-box').toggleClass('hidden');
        if(!$(this).siblings('.instrument-select-box').hasClass('hidden')){
            $(this).addClass('over-input');
            $('.delete_2019_border').removeClass('hidden');
        }else{
            $(this).removeClass('over-input');
            $('.delete_2019_border').addClass('hidden');
        }
        $("input[name='instrument-select']").trigger('input');
    })

    //输入框失去焦点关闭 zwm 2023/1/4
    $('body').on('blur', '.instrument-select', function() {
        const selectBox = $(this).siblings('.instrument-select-box');
        setTimeout(()=>{
            selectBox.addClass('hidden');
            if(selectBox.hasClass('hidden')){
                $('.instrument-select').removeClass('over-input');
                $('.delete_2019_border').addClass('hidden');
            }
        },200)
    })

    //清空仪器输入 zwm 2022/11/15
    $("body").on('click','.delete_2019_border',function (){
        if($('.instrument-select').val()){
            $('.instrument-select').val('');
            $("input[name='instrument-select']").trigger('input');
        }
    })

    //点击时间戳跳转到仪器对接并只有点击的一条数据 zwm 2022/11/18
    $("body").on("click",".inscada_table_content-popup td[data-field=timestamp]",function (){
        var data_id= $(this).closest('tr').data('id');
        var dataType= $(this).closest('tr').data('data-type');
        var insId = $('.search-inscada-box.popup').data('id');
        require('get_html').genInstrumentInScadaPage( insId,dataType,data_id);
    })


    //隐藏筛选项 zwm 2022/11/15
    $("body").on('click','.instrument-filter-change',function (){
        if($('.search-inscada-box.popup').hasClass('hidden')){
            $('.search-inscada-box.popup').removeClass('hidden');
            $(this).addClass('instrument-filter');
            $(this).removeClass('instrument-filter-cancel');
            // $('.instrument-data-modal .modal-content').css('height','480px');
        }else{
            $('.search-inscada-box.popup').addClass('hidden');
            $(this).removeClass('instrument-filter');
            $(this).addClass('instrument-filter-cancel');
            // $('.instrument-data-modal .modal-content').css('height','435px');
        }
    })

    // 搜索鹰群标签 zwm 2022/11/10
    $("body").on("input", "input[name='instrument-select']", function() {
        var tagBox = $(".instrument-select-list");
        tagBox.html("").hide();
        var keywords = $(this).val().trim();
        var searchResult = [];

        //如果输入框内容为空则显示历史记录 zwm 2023/1/7
        if (keywords == "") {
            let historyInstruments = localStorage.getItem('history_insert_instruments');
            historyInstruments = historyInstruments.split(',');
            if (historyInstruments) {
                for (let k = 0; k < historyInstruments.length; k++) {
                    for (let i = 0; i < window.instrumentList.length; i++) {
                        let tag = window.instrumentList[i];
                        if (historyInstruments[k]==tag['id']) {
                            searchResult.push(tag);
                        }
                    }
                }
                searchResult .slice(0, 10);
                // 渲染选择列表
                for (let r = 0; r < searchResult.length; r++) {
                    let tagInfo = searchResult[r];
                    let tagItem = $("<div><div class='history-ico instrument-history' style='float: left'></div></div>");
                    let subTagItem = $("<div class='instrument-select-item'></div>");
                    subTagItem.data("instrument_id", tagInfo["id"]);
                    subTagItem.data("instrument_name", tagInfo["name"]);
                    subTagItem.data("batch_number", tagInfo["batch_number"]);
                    subTagItem.data("instrument_type", tagInfo["data_type"]);
                    const itemContent = `${tagInfo["name"]}(${tagInfo["batch_number"]})`;
                    subTagItem.attr('title', itemContent);
                    subTagItem.text(tagInfo["name"] + '(' + tagInfo["batch_number"] + ')');
                    tagItem.append(subTagItem);
                    tagBox.append(tagItem);
                }
                tagBox.show();
            }
        } else {
            // 模糊匹配标签结果
            for (let i = 0; i < window.instrumentList.length; i++) {
                let tag = window.instrumentList[i];
                var matchStr = tag.name + '(' + tag.batch_number +')';
                if (matchStr.indexOf(keywords) !== -1 ) {
                    searchResult.push(tag);
                }
            }
            searchResult = searchResult.sort(function (a, b) {
                return a["name"].length - b["name"].length;
            }).slice(0, 10);
            // 渲染选择列表
            for (let r = 0; r < searchResult.length; r++) {
                let tagInfo = searchResult[r];
                let tagItem = $("<div class='instrument-select-item'><td style='border: 1px solid #CCC'></td></div>");
                tagItem.data("instrument_id", tagInfo["id"]);
                tagItem.data("instrument_name", tagInfo["name"]);
                tagItem.data("batch_number", tagInfo["batch_number"]);
                tagItem.data("instrument_type", tagInfo["data_type"]);
                tagItem.attr("title", tagInfo["name"] + '(' + tagInfo["batch_number"] + ')');
                tagItem.text(tagInfo["name"] + '(' + tagInfo["batch_number"] + ')');
                tagBox.append(tagItem);
            }
            tagBox.show();
        }
        //如果没有搜索到数据就显示一个提示语
        if(searchResult.length==0){
            let remind=mainLang('instrument_not_found');
            let remindBox=$("<div></div>");
            remindBox.text(remind);
            tagBox.append(remindBox);
        }
    });

    // 查看仪器详情 jiangdm 2022/11/4
    $('body').on('click', '.instrument-select-detail', function () {
        const instrumentId = $(this).closest('.modal-body').find('.instrument-select').data('instrument_id');
        if (instrumentId) {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-page',
                data: {
                    instrumentId:instrumentId,
                    type: 'view',
                },
                success: function(res) {
                    if (res.status == 1) {
                        $(".instrument_page").remove();
                        $("body").append(res.data.file);
                        $(".instrument_page").modal('show');
                    }
                }
            })
        }
    })

    // 切换仪器列表分类 jiangdm 2022/11/4
    $('body').on('click', '.instrument-select-button', function() {
        // $(this).css('border-color')==='gray'?$(this).css('border-color','#1388FF'):$(this).css('border-color','gray');
        $(this).toggleClass('btn-checked');
        const selectType = $(this).data('select_type');
        const selectBox = $(this).closest('.instrument-select-box');
        if (selectType == '0') {
            // 全部仪器
            selectBox.find('.instrument-select-item').each(function () {
                $(this).removeClass('hidden');
            })
        } else if (selectType == '3') {
            // 历史仪器
            let historyInstruments = localStorage.getItem('history_insert_instruments');
            if (historyInstruments) {
                historyInstruments = historyInstruments.split(',');
                selectBox.find('.instrument-select-item').each(function () {
                    if (historyInstruments.includes($(this).data('instrument_id').toString())) {
                        $(this).removeClass('hidden');
                    } else {
                        $(this).addClass('hidden');
                    }
                })
            }
        } else {
            // 数值或文件仪器
            selectBox.find('.instrument-select-item').each(function () {
                if ($(this).data('instrument_type') == selectType) {
                    $(this).toggleClass('hidden');
                }
            })
        }

    })

    // 选择仪器后加载仪器数据 jiangdm 2022/11/4
    $('body').on('click', '.instrument-select-item', function () {
        const instrumentInfo = {
            instrument_id: $(this).data('instrument_id'),
            name: $(this).data('instrument_name'),
            batch_number: $(this).data('batch_number'),
            instrument_type: $(this).data('instrument_type'),
        };
        loadInstrumentData(instrumentInfo);
        $(this).addClass('selected');
        $(this).siblings().removeClass('selected');
        $('.over-input').removeClass('over-input');
        $('.instrument-select').attr({
            "value":$(this).data('instrument_name')+'('+$(this).data('batch_number')+')',
            "data-id" :$(this).data('instrument_id')
    });

    })

    // 鼠标离开自定义表格/物料表单元格，标记为待插入单元格 jiangdm 2022/11/4
    $('body').on('blur', '.define_table td input, .chendraw_data input.materiel_mass_input, .chendraw_data input.product_mass_input', function() {
        window.instrumentDataBox = {
            type: 'define_table',
            box: $(this),
            time: (new Date()).getTime(),
        };
    });

    /**
     * 待插入仪器数据的单元格信息
     * @typedef TInstrumentCellInfo
     * @property {'define_table' | 'intable' | 'ueditor'} type - 插入仪器数据的类型
     * @property {jQuery} box - 待插入单元格的对象
     * @property {number} time - 标记时间戳
     * @property {?{eventSource: 'in_material', insertInstrumentData: (newVal: string|number) => void}} materialParams - 来自物料表的相关参数
     */
    const exitMaterialInstrumentEvent = window.EVENT_NAMES.MATERIAL.EXIT_EDIT_INSTRUMENT_MASS;
    $('body').on(exitMaterialInstrumentEvent, function (evt, params) {
        console.log(params);
        /** @type {TInstrumentCellInfo} */
        const instrumentDataBox = {
            type: 'define_table',
            box: $(this),
            time: (new Date()).getTime(),
        };

        if (params?.eventSource === window.EVENT_NAMES.MATERIAL.EVENT_SOURCE) {
            instrumentDataBox.materialParams = params
        }
        /** @type {TInstrumentCellInfo} */
        window.instrumentDataBox = instrumentDataBox
    });

    // ctrl+鼠标点击自定义表格/物料表单元格，在仪器数据弹窗内展示对应数据 jiangdm 2022/11/4
    $('body').on('click', '.instrument-data-input, .material__cell--inst-info', function(event, params) {
        // 处理物料表内的单元格事件
        const isCtrlClick = event.ctrlKey;
        const instDataInfo = params?.instDataInfo;
        if (params?.eventSource === window.EVENT_NAMES.MATERIAL.EVENT_SOURCE) {
            if (isCtrlClick && instDataInfo) {
                window.loadInstrumentPopup(instDataInfo.mass);
                return;
            }
        }
        if (event.ctrlKey) {
            const instrumentInfo = {
                batch_number: $(this).data('batch_number'),
                data_id: $(this).data('data_id'),
                instrument_type: $(this).data('instrument_type'),
            };
            window.loadInstrumentPopup(instrumentInfo);
        }
    })

    // 鼠标点击文本编辑器/文件上传模块链接，在仪器数据弹窗内展示对应数据 jiangdm 2022/11/4
    $('body').on('click', '.instrument-data-link',function () {
        const instrumentInfo = {
            batch_number: $(this).data('batch_number'),
            data_id: $(this).data('data_id'),
            instrument_type: $(this).data('instrument_type'),
        };
        window.loadInstrumentPopup(instrumentInfo);
    })

    // 鼠标单击非仪器数据弹窗相关的位置，清空待插入单元格信息 jiangdm 2022/11/4
    $('body').on('click', function (event) {
        const target = $(event.target);
        if (target.data('type') !== 'insert_instrument_data' && target.closest('.instrument-data-modal').length <= 0) {
            window.instrumentDataBox = null;
        }
    })

    // 清空自定义表格/物料表单元格后，清除仪器数据绑定状态 jiangdm 2022/11/4
    $('body').on('change', '.instrument-data-input', function(evt, param1) {
        if ($(this).val() == '') {
            $(this).removeClass('instrument-data-input modified');
        } else if (param1 == 'new') {
            $(this).removeClass('modified');
        } else {
            $(this).addClass('modified');
        }
    })

    // 鼠标离开文本编辑器区域时，检测是否有点选操作，如果没有则不标记待插入状态，防止清除前面的待插入状态 jiangdm 2022/11/4
    $('body').on('mouseleave', '.tinymce_textarea', function() {
        const editorKey = $(this).attr('id');
        if (UE.getEditor(editorKey).execCommand('getselectstatus')) {
            window.instrumentDataBox = {
                type: 'ueditor',
                box: $(this),
                time: (new Date()).getTime(),
            };
        }
    })

    // 鼠标离开intable区域时，检测是否有点选操作，如果没有则不标记待插入状态，防止清除前面的待插入状态 jiangdm 2022/11/4
    $('body').on('mouseleave', '.detail_xsheet', function() {
        if(this.contentWindow.xs){
            if (this.contentWindow.xs.sheet.getSelectStatus()) {
                window.instrumentDataBox = {
                    type: 'intable',
                    box: this,
                    time: (new Date()).getTime(),
                };
            }
        }
        else{
            const getSelectStatus = this.contentWindow.getSelectStatus;
            if (getSelectStatus != null) {
                // 新intable
                if(getSelectStatus()){
                    window.instrumentDataBox = {
                        type: 'intable',
                        box: this,
                        time: (new Date()).getTime(),
                    };
                }
            }
        }
    });

    // 切换字段配置下拉菜单展示
    $('body').on('click', '.instrument-data-fields-box', function (e) {
        e.stopPropagation();
        let $instrument_data_config = $(this).siblings('.inscada-data-config');

        $instrument_data_config.toggleClass('hidden');
    });

    // inscada 展示配置弹窗
    $('body').on('click', '.inscada-show-hide-fields-config', function (e) {
        e.stopPropagation();
        let $inscada_show_hide_fields = $('.inscada-data-config .inscada-show-hide-fields');
        let $instrument_data_fields = $('.inscada-data-config .instrument-data-fields');

        $('.inscada-show-hide-fields-config').addClass('inscada-setting-active');
        $('.instrument-data-fields-setting').removeClass('inscada-setting-active');
        $inscada_show_hide_fields.removeClass('hidden');
        $instrument_data_fields.addClass('hidden');
    });

    // inscada 切换显示隐藏列和字段配置
    $('body').on('click', '.instrument-data-fields-setting', function (e) {
        e.stopPropagation();
        let $instrument_data_fields = $('.inscada-data-config .instrument-data-fields');
        let $inscada_show_hide_fields = $('.inscada-data-config .inscada-show-hide-fields');

        $('.instrument-data-fields-setting').addClass('inscada-setting-active');
        $('.inscada-show-hide-fields-config').removeClass('inscada-setting-active');
        $instrument_data_fields.removeClass('hidden');
        $inscada_show_hide_fields.addClass('hidden');
    });

    // inscada 显示字段
    $('body').on('click', '.inscada-hide', function (e) {
        e.stopPropagation();
        let inscada_column = $(this).attr('data-type');
        var listCols = localStorage.getItem('instrument_inscada_table_popup_showHideColumn_cols_index');
        listCols = listCols.split(',');
        if (listCols.includes(inscada_column)) {
            listCols = listCols.filter(item => item !== inscada_column);
        }
        listCols = listCols.join(',');
        localStorage.setItem('instrument_inscada_table_popup_showHideColumn_cols_index', listCols);
        $('#instrument_inscada_table_popup_showHideColumn').find(`td[data-field="${inscada_column}"]`).show();
        $('#instrument_inscada_table_popup_showHideColumn').find(`th[data-field="${inscada_column}"]`).show();
        $(this).removeClass('inscada-hide');
        $(this).addClass('inscada-show');
    });

    // inscada 隐藏字段
    $('body').on('click', '.inscada-show', function (e) {
        e.stopPropagation();
        let inscada_column = $(this).attr('data-type');
        var listCols = localStorage.getItem('instrument_inscada_table_popup_showHideColumn_cols_index');
        listCols = listCols.split(',');
        if (!listCols.includes(inscada_column)) {
            listCols.push(inscada_column);
        }
        listCols = listCols.join(',');
        localStorage.setItem('instrument_inscada_table_popup_showHideColumn_cols_index', listCols);
        $('#instrument_inscada_table_popup_showHideColumn').find(`td[data-field="${inscada_column}"]`).hide();
        $('#instrument_inscada_table_popup_showHideColumn').find(`th[data-field="${inscada_column}"]`).hide();
        $(this).removeClass('inscada-show');
        $(this).addClass('inscada-hide');
    });

    //  点击弹窗关掉字段配置下拉菜单展示
    $('body').on('click', '.instrument_insert_model .modal-content', function (e) {//bug#31824，修改了触发事件的selector，防止在其他地方点击弹框也会触发这个事件 mod dx
        let $inscada_data_config = $('.inscada-data-config');
        if (instrument_insert_data_selector.length > 0) {//如果有插入字段筛选下拉菜单，才修改
            if (!$(e.target).closest('.inscada-data-config').length) {
                // 如果点击目标不是.inscada-data-config内部的元素，则隐藏.inscada-data-config
                $inscada_data_config.toggleClass('hidden', true);
            }
        }
    })
    $('body').on('click', '.instrument-data-fields', function (e) {
        e.stopPropagation();
    })

    // intable预览excel
    $('body').on('click', '.preview-excel', function() {
        var window_width = window.innerWidth;
        var window_height = window.innerHeight;
        var path = $(this).siblings('input[name="dep_path"]').val();
        var save_name = $(this).siblings('input[name="save_name"]').val();
        var name = $(this).siblings('input[name="file_name"]').val();
        $.ajax({
            url: '?r=eln-interface/excel-preview',
            data:{
                path,
                save_name,
                name,
                window_width,
                window_height,
            },
            type: 'post',
            success: function(res) {
                $('body').append(res);
                $('.preview_xsheet').modal('show');
            }
        })
    })

    // 绑定仪器库我的预约界面搜索提交事件 add by hkk 2019/10/30
    $("body").on('click', '.search-my-instrument-book', function (event) {
        // 获取查询条件和页数信息
        var data = $.formSerializeFn($(".exp_conetnt.active #search-my-instrument-book"));
        data['page'] = 1;
        data['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
        data['needUpdateAllPage'] = 0;
        data['type'] = 'my_instruments';

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/my-book',
            data: data,
            type: 'POST',
            success: function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    $('.exp_conetnt.active .instruments_book_table').html(data.data.file);

                }
            }
        });
    });

    // 绑定仪器库导出 add by hkk 2019/10/30
    $('body').on('click', '.operateInstrumentExport', function() {
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage")); // 搜索条件
        // bug#28130-仪器库管理，如果有勾选，导出的仅包括勾选的条目 jiangdm 2022/8/9
        var chooseIds = [];
        $('.exp_conetnt.active .instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        $('.exp_conetnt.active .instruments_table .thead-fixed .basic_field')

        data['show_field'] = [];
        //显示的字段
        $('.exp_conetnt.active .instruments_table .thead-fixed .basic_field:visible').each(function () {
            data['show_field'].push($(this).attr('data-field'));
        });
        data['chooseIds'] = chooseIds;
        if ($(this).hasClass('my-instruments')){
            data['export_type'] = 'my_instruments';
        }
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-export',
            data: data,
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instruments.xlsx&file_name=instruments.xlsx";
                }
            }
        });

    });


    // add by hkk 2021/4/21 仪器记录的配置导入
    $('body').on('click', '.recordFieldSetting', function () {

        // 获取选中的仪器记录id
        var chooseIds = [];
        $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip23'));
            return
        }

        var data = {
            chooseIds: chooseIds.join(','),
        };

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-instrument-record-setting-page',
            data: data,
            success: function (data) {
                if (data.status == 1) {

                    $(".instrument_record_field_setting_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_record_field_setting_page").modal('show');


                    $('.fs_select_instrument_field').fSelect({
                        placeholder: mainLang('no_chosen_instrument'),
                        numDisplayed: 3,
                        overflowText: 'please select instrument',
                        noResultsText: mainLang('no_search_result'),
                        searchText: mainLang('search'),
                        showSearch: true
                    });
                }
            }
        });

        return
    });

    // add by hkk 2021/4/21 仪器记录的配置导入弹框->切换仪器查询对应仪器的三大记录字段配置
    $('body').on('change', '.instrument_record_field_setting_page select[name=instrument_record_config]', function () {

        var instrumentId = $(this).val();
        if (instrumentId) {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-instrument-record-setting-data',
                data: {
                    instrumentId: instrumentId
                },
                success: function (data) {
                    if (data.status === 1) {

                        // 设置过显示隐藏列，要显示到目前的页面
                        var operateShowConfig = localStorage.getItem('instruments_operate_record_table_' + instrumentId + '_showHideColumn_cols_index');
                        var operateShowArray =  operateShowConfig ? operateShowConfig.split(',') : []; // 为空是全显示
                        var repairShowConfig = localStorage.getItem('instruments_repair_record_table_' + instrumentId + '_showHideColumn_cols_index');
                        var repairShowArray =  repairShowConfig ? repairShowConfig.split(',') : []; // 为空是全显示
                        var checkShowConfig = localStorage.getItem('instruments_check_record_table_' + instrumentId + '_showHideColumn_cols_index');
                        var checkShowArray = checkShowConfig ? checkShowConfig.split(',') : []; // 为空是全显示
                        var value;
                        var operateHtml = '';
                        var repairHtml = '';
                        var checkHtml = '';
                        var operateFieldLength = 0;
                        var repairFieldLength = 0;
                        var checkFieldLength = 0;
                        var checked;

                        // 三大记录
                        if (data['data']['operateRecordField']) {
                            for (var key1 in data['data']['operateRecordField']) {
                                if (data['data']['operateRecordField'][key1]) {
                                    value = data['data']['operateRecordField'][key1];
                                    checked = operateShowArray.includes(key1) ? '' : 'checked';
                                    operateHtml += '<li class="recordSettingLi"  title="' + value + '"><input type="checkbox" ' + checked + ' class="disabled"  >' + value + '</li>'
                                }
                            }
                        }
                        if (data['data']['repairRecordField']) {
                            for (var key2 in data['data']['repairRecordField']) {
                                if (data['data']['repairRecordField'][key2]) {
                                    value = data['data']['repairRecordField'][key2];
                                    checked = repairShowArray.includes(key2) ? '' : 'checked';
                                    repairHtml += '<li class="recordSettingLi"  title="' + value + '"><input type="checkbox" ' + checked + ' class="disabled"  >' + value + '</li>'
                                }
                            }
                        }
                        if (data['data']['checkRecordField']) {
                            for (var key3 in data['data']['checkRecordField']) {
                                if (data['data']['checkRecordField'][key3]) {
                                    value = data['data']['checkRecordField'][key3];
                                    checked = checkShowArray.includes(key3) ? '' : 'checked';
                                    checkHtml += '<li class="recordSettingLi"  title="' + value + '"><input type="checkbox" ' + checked + ' class="disabled"  >' + value + '</li>'
                                }
                            }
                        }

                        // 补上前两列选中 序号 和最后一列操作栏的显示
                        if (!operateHtml) {
                            operateHtml = mainLang('default_config');
                        }
                        if (!repairHtml) {
                            repairHtml = mainLang('default_config');
                        }
                        if (!checkHtml) {
                            checkHtml = mainLang('default_config');
                        }

                        $('.instrument_record_field_setting_page .operateRecordSettingDiv').html(operateHtml);
                        $('.instrument_record_field_setting_page .repairRecordSettingDiv').html(repairHtml);
                        $('.instrument_record_field_setting_page .checkRecordSettingDiv').html(checkHtml);


                    }
                }
            });
        }

    });

    // add by hkk 2021/4/21 仪器记录的配置提交
    $('body').on('click', '.instrument_record_field_setting_page .instrument_record_config_conform_btn', function () {


        // 把目标仪器的仪器配置复制到选中的仪器中
        var chooseIds = $(this).attr('data-chooseIds');
        var instrumentId = $('.instrument_record_field_setting_page select[name=instrument_record_config]').val();
        var changeOperateConfig = $('.instrument_record_field_setting_page input[name=operate_record_config]').prop('checked');
        var changeRepairConfig = $('.instrument_record_field_setting_page input[name=repair_record_config]').prop('checked');
        var changeCheckConfig = $('.instrument_record_field_setting_page input[name=check_record_config]').prop('checked');

        if(!changeOperateConfig && !changeRepairConfig && !changeCheckConfig){
            $.showAlert(mainLang('record_config_setting_tip1'));
            return
        }

        if( (changeOperateConfig && $('.instrument_record_field_setting_page .operateRecordSettingDiv li').length === 0 ) ||
            (changeRepairConfig && $('.instrument_record_field_setting_page .repairRecordSettingDiv li').length === 0 ) ||
            (changeCheckConfig && $('.instrument_record_field_setting_page .checkRecordSettingDiv li').length === 0 )){
            $.showAlert(mainLang('record_config_setting_tip2'));
            return
        }

        // 默认配置提示无法勾选
        if (instrumentId) {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/submit-instrument-record-setting',
                data: {
                    chooseIds: chooseIds,
                    instrumentId: instrumentId,
                    changeOperateConfig: changeOperateConfig,
                    changeRepairConfig: changeRepairConfig,
                    changeCheckConfig: changeCheckConfig,
                },
                success: function (data) {
                    if (data.status === 1) {

                        // 更改前段显示隐藏列缓存
                        var showConfig;
                        var chooseIdsArr = chooseIds.split(',');
                        if (changeOperateConfig) {
                            showConfig = localStorage.getItem('instruments_operate_record_table_' + instrumentId + '_showHideColumn_cols_index') || '';
                            if (showConfig) {
                                for (var i = 0; i < chooseIdsArr.length; i++) {
                                    localStorage.setItem('instruments_operate_record_table_' + chooseIdsArr[i] + '_showHideColumn_cols_index', showConfig);
                                }
                            }
                        }
                        if (changeRepairConfig) {
                            showConfig = localStorage.getItem('instruments_repair_record_table_' + instrumentId + '_showHideColumn_cols_index') || '';
                            if (showConfig) {
                                for (var i = 0; i < chooseIdsArr.length; i++) {
                                    localStorage.setItem('instruments_repair_record_table_' + chooseIdsArr[i] + '_showHideColumn_cols_index', showConfig);
                                }
                            }
                        }
                        if (changeCheckConfig) {
                            showConfig = localStorage.getItem('instruments_check_record_table_' + instrumentId + '_showHideColumn_cols_index') || '';
                            if (showConfig) {
                                for (var i = 0; i < chooseIdsArr.length; i++) {
                                    localStorage.setItem('instruments_check_record_table_' + chooseIdsArr[i] + '_showHideColumn_cols_index', showConfig);
                                }
                            }
                        }

                        $(".instrument_record_field_setting_page").remove();
                        $.showAlert(mainLang('configure_success'));

                    }
                }
            });
        }else{
            $.showAlert(mainLang('please_select_instrument'));
        }


    });

    // add by hkk 2021/4/23 仪器使用图表统计
    $('body').on('click', '.viewUseGraph', function () {
        require('get_html').genInstrumentUseGraphPage();
    });

    // add by hkk 2021/4/25 绑定使用图表搜索提交事件
    $("body").on('click', '.instrument_use_graph_page .search-instrument-usage-submit', function (event) {

        // 获取查询条件
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-use-graph"));
        data['instrument_create_user'] = $(".exp_conetnt.active #search-instrument-use-graph input[name=instrument_create_user]").attr('idbox');
        data['needUpdateAllPage'] = "no";

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-use-graph-page',
            data: data,
            type: 'POST',
            success: function (data) {
                if (data.status === 1) {
                    // 替换表格重新生成
                    $('.exp_conetnt.active .instruments_use_graph_chart').html(data.data.file);

                }
            }
        });
    });

    // add by zwm 2022/11/1 绑定使用图表搜索提交事件
    $("body").on('click', '.instrument_use_graph_page .search-inscada-graph', function (event) {

        // 获取查询条件
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-use-graph"));
        data['dataType']=$('.inscada_table').attr('data-data-type');
        data['instrument_create_user'] = $(".exp_conetnt.active #search-instrument-use-graph input[name=inscada-operator]").attr('idbox');
        data['needUpdateAllPage'] = "no";

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-inscada-graph-page',
            data:data,
            type: 'POST',
            success: function (data) {
                if (data.status === 1) {
                    // 替换表格重新生成
                    $('.exp_conetnt.active .inscada_sum_graph').html(data.data.file);

                }
            }
        });
    });

    // add by hkk 2021/4/26  仪器预约图表统计
    $('body').on('click', '.viewBookGraph', function () {
        require('get_html').genInstrumentBookGraphPage();

    });

    // 更多-仪器对接 inscada
    $('body').on('click', '.viewInscadaSummary', function () {
        //权限控制，用来区分我的仪器库和仪器库管理
        inscada_type = '';
        if ($(this).hasClass('my-instruments')){
            inscada_type = 'my-instruments';
        }
        require('get_html').genInscadaSummaryPage(inscada_type);

        // $('.exp_title .tag.on').removeClass('on');
    });

    $('body').on('click', '.viewBookInstruments', function () {
        // 挂载vue，打开仪器预约的vue页面
        console.log('加载模板类型管理');
        // 动态加载组件脚本
        renderComponent('/vue-ui/dist/bookInstruments.js', '#book-app', {
            closeBookInstruments: function() {
                // 使用unmountDynamicApp销毁应用
                unrenderComponent('#book-app');
                $('#book-app').hide();
                $(".top_nav").show()
            }
        }).then(function (){
            $(".top_nav").hide()
            $('#book-app').show()
        });
       

    });

    //  add by hkk 2021/4/26 绑定预约图表搜索提交事件
    $("body").on('click', '.instrument_book_graph_page .search-instrument-book-submit', function (event) {

        // 获取查询条件
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-book-graph"));
        data['instrument_create_user'] = $(".exp_conetnt.active #search-instrument-book-graph input[name=instrument_create_user]").attr('idbox');
        data['needUpdateAllPage'] = "no";

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-book-graph-page',
            data: data,
            type: 'POST',
            success: function (data) {
                if (1 == data.status) {

                    // 替换表格重新生成
                    $('.exp_conetnt.active .instruments_book_graph_chart').html(data.data.file);

                }
            }
        });
    });

    // add by hkk 2021/4/26  打开单个仪器使用统计图
    $('body').on('click', '.operateSingleUsage', function () {
        instrumentApi.insId = $(this).attr('data-id');
        require('get_html').genInstrumentSingleUsagePage(instrumentApi.insId);
    });

    // add by hkk 2021/4/26 单个仪器使用统计图 查询
    $("body").on('click', '.instrument_use_graph_page_single .instrument-single-usage-submit', function (event) {

        // 获取查询条件
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-use-graph-single"));
        data['instrumentId'] = $(this).attr('data-id');
        data['needUpdateAllPage'] = "no";

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-single-usage-page',
            data: data,
            type: 'POST',
            success: function (data) {
                if ( data.status === 1) {

                    // 替换表格重新生成
                    $('.exp_conetnt.active .instruments_use_graph_single_chart').html(data.data.file);

                }
            }
        });
    });

    // InScada 功能
    $('body').on('click', '.operateInScada', function () {
        instrumentApi.insId = $(this).attr('data-id');
        instrumentApi.dataType = $(this).attr('data-type');
        require('get_html').genInstrumentInScadaPage(instrumentApi.insId, instrumentApi.dataType);
    });

    // add by hkk 2021/4/27  仪器表新增自定义列
    $('body').on('click', '.instruments_manage_style .addInstrumentCol', function () {

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/add-instrument-column',
            data: {},
            success: function (data) {
                if (data.status === 1) {
                    var defineColumnIndex = data.data.addField;
                    // 获取查询条件刷新仪器页面
                    var queryData = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage"));
                    queryData['page'] = 1;
                    queryData['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || undefined;
                    queryData['needUpdateAllPage'] = 0;
                    queryData['type'] = 'instruments_manage';
                    $.ajaxFn({
                        url: ELN_URL + '?r=instrument/manage',
                        data: queryData,
                        type: 'POST',
                        success: function (data) {
                            if (data.status === 1) {
                                $('.exp_conetnt').removeClass('search');
                                $('.exp_conetnt.active .instruments_table').html(data.data.file);
                                // 激活刚增加的列标题编辑
                                var head = $('.exp_conetnt.active .instruments_manage_style th[data-field="' + defineColumnIndex + '"]')
                                $('.instrument_title_input', head).show().focus();
                                $('.instrument_title_input', head).select();
                                $('.instrument_def_title', head).hide();
                            }
                        }
                    });
                }
            },
            noLoad: true,
        })

    });

    // add by hkk 2021/5/6  使用统计导出
    $('body').on('click', '.instrument_use_graph_page .exportUsageStatistics', function() {

        // 构造标题数据
        var fieldArr = [
            mainLang('instrument_name'),
            mainLang('instrument_usage_percent'),
            mainLang('use_times'),
            mainLang('use_members'),
            mainLang('repair_times'),
            mainLang('check_times')
        ];

        // 获取统计数据
        var usageDataObj = JSON.parse($('#instrument_usage_data').val());


        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-usage-statistics-export',
            data: {
                usageData:usageDataObj,
                fieldArr:fieldArr,
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instruments_usage_statistics.xlsx&file_name=instruments_usage_statistics.xlsx";
                }
            }
        });
    });

    // add by hkk 2021/5/6  预约统计导出
    $('body').on('click', '.instrument_book_graph_page .exportBookStatistics', function() {

        // 构造标题数据
        var fieldArr = [
            mainLang('instrument_name'),
            mainLang('instrument_book_percent'),
            mainLang('book_times'),
            mainLang('book_members'),
        ];

        // 获取统计数据
        var usageDataObj = JSON.parse($('#instrument_book_data').val());

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instruments-book-statistics-export', // actionInstrumentsUsageStatisticsExport
            data: {
                usageData:usageDataObj,
                fieldArr:fieldArr,
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instruments_book_statistics.xlsx&file_name=instruments_book_statistics.xlsx";
                }
            }
        });

    });

    // add by hkk 2021/5/6  使用统计导出(单个仪器)
    $('body').on('click', '.instrument_use_graph_page_single .exportSingleUsageStatistics', function() {

        // 获取当前的统计数据
        var type = 'date'; // 日期时长图
        var fieldArr = [];
        var instrumentId = $(this).attr('data-id');
        var usageDataObj = JSON.parse($('.instrument_use_graph_page_single #instrument_single_usage_data_' + instrumentId).val());

        if (usageDataObj['info'] && usageDataObj['info'] === 'no record') {

            $.showAlert(mainLang('no_record_tip'));
        } else {
            if (usageDataObj['nameArr']) { // 人员-时长图
                type = 'member'; // 日期时长图
                fieldArr = [
                    mainLang('user_name'),
                    mainLang('instrument_usage_percent'),
                    mainLang('use_time'),
                    mainLang('use_times'),
                ];
            }
        }



        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instrument-usage-statistics-export',
            data: {
                usageData:usageDataObj,
                fieldArr:fieldArr,
                type:type,
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=instrument_usage_statistics.xlsx&file_name=instrument_usage_statistics.xlsx";
                }
            }
        });

    });


    // add by hkk 2021/5/6  查看复核界面的仪器详情
    $('body').on('click', '.viewRecordData', function() {

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-record-page', // actionInstrumentsUsageStatisticsExport
            data: {
                instrumentId: $(this).attr('data-id'),
                runningId: $(this).attr('data-recordId'),
                approveId: $(this).attr('data-approveId') || '',
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {

                    $(".instrument_record_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_record_page").modal('show');

                }
            }
        });

    });

    // add by zwm 2023/4/14 查看复核界面的状态调整
    $('body').on('click', '.viewStatusChange', function () {

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-status-change-page',
            data: {
                instrumentId: $(this).attr('data-id'),
                approveId: $(this).attr('data-approveId') || '',
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {

                    $(".view_status_change").remove();
                    $("body").append(data.data.file);
                    $(".view_status_change").modal('show');

                }
            }
        });

    });

    // add by hkk 2019/10/29 仪器库查看记录详情页面 复核页通过和拒绝
    $('body').on('click', '.instrument_check_submit_btn', function () {

        var action = $(this).attr('data-type');
        var approveId = $(this).attr('data-id');

        var html,title;
        if (action === 'agree') { // 通过操作界面
             html =
                `<div style="margin-bottom: 10px">
                        <span style="padding-left: 54px">${mainLang('approval_success')}</span>
                    </div>
                    <div class="review" style="min-height: initial;">
                        <label class="body_left">${mainLang('pass')}</label>
                        <div style="display: inline-block;position: relative">
                            <input type="password" class="angle_input iblock pop_input_password  password-password" autocomplete="new-password"/>
                            <input type="text" class="angle_input iblock pop_input_password password-text"/>
                            <div class="visible-password">
                                <div class="visible-password-icon visible-icon invisible-icon"></div>
                            </div>
                        </div>
                    </div>`;
             title = mainLang('approval_agree');
        } else { // 拒绝操作界面
             html = `
                    <div class="review">
                        <div class="input_part">
                            <label class="body_left">${mainLang('pass')}：</label>
                            <div style="display: inline-block;position: relative">
                                <input type="password" class="angle_input iblock pop_input_password  password-password" autocomplete="new-password"/>
                                <input type="text" class="angle_input iblock pop_input_password password-text"/>
                                <div class="visible-password">
                                    <div class="visible-password-icon visible-icon invisible-icon"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input_part">
                            <label class="body_left">${mainLang('reason')}：</label>
                            <textarea class="angle_input iblock pop_input_con"></textarea>
                        </div>
                    </div>`;
            title = mainLang('approval_refuse');
        }

        // 弹出操作界面
        $.popContent(html, title , function() {


            var ajaxUrl = ELN_URL + '?r=approval/agree';
            var ajaxData = {};


            ajaxData.approval_id_arr = [approveId];


            var password = $('.pop_input_password:visible').val();
            if (!$.checkVal(password, 'noempty')) {
                $.showAlert(mainLang('pass_must'));
                return false;
            }

            ajaxData.password = $.passwordEncipher(password);

            if (action === 'refuse') {
                ajaxUrl = ELN_URL + '?r=approval/refuse';
                var remark = $('.pop_input_con:visible').val().trim();
                if (remark === '') {
                    $.showAlert(mainLang('refuse_reason_empty'));
                    return false;
                }
                if (remark.length > 1000) {
                    $.showAlert(mainLang('refuse_reason_exceed', [1000]));
                    return false;
                }
                ajaxData.remark = remark;
            }

            $.ajaxFn({
                url: ajaxUrl,
                data: ajaxData,
                success: function(res) {
                    if (res.status === 1) {
                        $.showAlert(mainLang('success'));
                        $('.pop_modal').modal('hide');

                        $(".instrument_record_page").remove();
                        $('.exp_conetnt.active .approval_tab.active').trigger('click');

                    }
                }
            });


        });
    });

    // add by hkk 2021/6/1 批量编辑弹框
    $('body').on('click', '.instruments_manage_style .batchEdit', function() {

        // 编辑弹框
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-batch-edit-page',
            data: {
                instrumentId: $(this).attr('data-id'),
                runningId: $(this).attr('data-recordId'),
                approveId: $(this).attr('data-approveId') || '',
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {

                    $(".instrument_batch_edit_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_batch_edit_page").modal('show');

                }
            }
        });


    });

    // add by hkk 2021/6/1 批量编辑-设置导出字段
    $('body').on('click', '.instrument_batch_edit_page .setConfig', function() {

        // 读取当前表格的字段配置
        var th = $(`.exp_conetnt.active #instrument_manage_table_showHideColumn`).find('tr th');

        // instrument_batch_edit_fields
        var fieldConfig = localStorage.getItem('instrument_batch_edit_fields');
        var unCheckedField =  fieldConfig ? fieldConfig.split(',') : [];
        var html = '<div class="instrumentsShowHideModal title"><input type="checkbox" class="beauty-checkbox-big" id="export_config"><label for="export_config">' + mainLang('select_all') +'</label></div><div class="instrumentShowModalContents"><ul class="ml30 exp-list-cols field clear">';
        var unShowFieldArray = ['create_by', 'create_time', 'picture', 'data_type', 'operation', 'repair_end_time', 'repair_start_time', 'end_running_time', 'start_running_time', 'end_check_time', 'start_check_time', 'end_expiry_time', 'start_expiry_time'];
        th.each(function(index, item) {
            var field = $(this).attr('data-field');
            if (field && !unShowFieldArray.includes(field) && !$(this).hasClass('manage_field') ) {
                var colFlag = $(this).attr('data-field');
                if (colFlag.slice(0,5) === 'field') {
                    colFlag = 'define__' + colFlag;
                } else {
                    colFlag = 'general__' + colFlag;
                }
                var checked = unCheckedField.includes(colFlag) ? '' : 'checked';
                html += '<li class="col-item">' +
                    '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '"' + checked + '/>' +
                    '<label for="' + colFlag + '" title="' + $(this).text() + '">' + $(this).text() + '</label>' +
                    '</li>';
            }
        });

        html += '</ul></div>';

        $.popContent(html, mainLang('set_field'), function() {
            var fieldIndexArray = []; // 存储隐藏列的字段
            $('.exp-list-cols.field .col-item :checkbox').each(function(index, item) {
                if (!$(this).is(':checked')) {
                    fieldIndexArray.push($(this).attr('id')); // 存储导出字段
                }
            });
            localStorage.setItem('instrument_batch_edit_fields', fieldIndexArray.toString());
            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        },function () {
            // 判断是否全选基础信息字段
            if ($('.instrumentShowModalContents ul .col-item input').length === $('.instrumentShowModalContents ul .col-item input:checked').length) { // 判断子节点是否都选中则选中标题全选框
                $('#export_config').prop('checked', true)
            }

        });


    });

    // add by hkk 2021/6/1 批量编辑-导出Excel
    $('body').on('click', '.instrument_batch_edit_page .exportBatchEditExcel', function() {

        // 导出筛选条件
        var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage"));
        var chooseIds = [];
        $('.exp_conetnt.active .instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        data['chooseIds'] = chooseIds;
        data['unCheckedField'] = localStorage.getItem('instrument_manage_table_showHideColumn_cols_index');
        data['instrument_status'] = $('.exp_conetnt.active #instrument_status').val();

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/export-batch-edit-excel',
            data: data,
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    window.location.href = ELN_URL + "?r=download/file&path=&name=batch_edit_instruments.xlsx&file_name=batch_edit_instruments.xlsx";
                }
            }
        });

    });

    // add by hkk 2021/6/1 批量编辑-历史信息
    $('body').on('click', '.instrument_batch_edit_page .exportBatchEditHistory', function() {

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-batch-edit-history',
            data: {
                type:1 // 1 编辑 2 添加
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    var html = data.data.file;
                    $.popContent(html, mainLang('batch_edit_history'),null, null, false,'820px')
                }
            }
        });


    });


    //  add by hkk 2022/5/27 批量添加-导出excel 兼容显示隐藏列
    $('body').on('click', '.instrument_batchAdd_page  .exportBatchAddExcel', function() {


        // 获取当前表格显示的字段导出
        var exportFields = [];
        var th = $('.exp_conetnt.active #instrument_manage_table_showHideColumn').find('tr th');
        th.each(function (index, item) {
            if ($(this).hasClass('basic_field')
                && $(this).attr('data-field') !== "create_time"
                && $(this).attr('data-field') !== "create_by"
                && $(this).attr('data-field') !== "groupIds"
                && $(this).attr('data-field') !== "departmentIds"
                && $(this).attr('data-field') !== "picture"
                && $(this).attr('data-field') !== "start_expiry_time"
                && $(this).attr('data-field') !== "end_expiry_time"
                && $(this).attr('data-field') !== "start_check_time"
                && $(this).attr('data-field') !== "end_check_time"
                && $(this).attr('data-field') !== "start_running_time"
                && $(this).attr('data-field') !== "end_running_time"
                && $(this).attr('data-field') !== "repair_start_time"
                && $(this).attr('data-field') !== "repair_end_time"
                && ($(this).is(":visible") || $(this).attr('data-field') === "name")
                && $(this).find('.text').length > 0) {
                exportFields.push($(this).find('.text').text().trim())
            }
        });

        window.location.href = ELN_URL + '?r=instrument/export-batch-add-excel&exportFields=' + exportFields.join(',');

    });

    // add by hkk 2021/6/1 批量添加-历史信息
    $('body').on('click', '.instrument_batchAdd_page .batchAddHistory', function() {

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-batch-edit-history',
            data: {
                type:2 // 1 编辑 2 添加
            },
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    var html = data.data.file;
                    $.popContent(html, mainLang('batch_add_history'),null, function () {
                        $('.import-edit-history-wrap').parents('.modal-content').css('width','820px')
                    }, false, '820px')

                }
            }
        });


    });

    // add by hkk 2022/5/31查看批量添加历史的错误信息
    $('body').on('click', '.import-edit-history-wrap .viewBatchAddError', function() {

        var id = $(this).attr('data-id');
        var insertSelector = $('.import-edit-history-wrap .hide_view_detail');

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-batch-add-error',
            data: {id:id},
            type: 'POST',
            success: function (data) {
                if (1 === data.status) {
                    var errorInfo = data.data.errorInfo;
                    var errorHtml = '';
                    for (var i = 0; i < errorInfo.length; i++) {
                        errorHtml += `<tr><td>${errorInfo[i]['row']}</td><td>${errorInfo[i]['error_info']}</td></tr>`
                    }
                    var tableHtml = `<table>
                                        <thead><tr>
                                            <th class="row-title">${mainLang('row_number')}</th>
                                            <th>${mainLang('failure_reason')}<span class="close">×</span></th>
                                        </tr></thead>
                                        <tbody>${errorHtml}</tbody>
                                    </table>`
                    insertSelector.html(tableHtml)
                }
            }
        });


    });

    // add by hkk 2022/5/31 关闭错误信息弹框
    $('body').on('click', '.import-edit-history-wrap .hide_view_detail .close', function() {
        $('.import-edit-history-wrap .hide_view_detail').html('');
    });


    // add by hkk 2022/5/24 显示隐藏列 仪器库管理和我的仪器库专有 2022/8/10弃用
    $('body').on('click', '.exp_conetnt.active .instrumentShowHideColumnDeleteVersion', function() {
        var tableId = $(this).attr('data-tableId'); //instrument_manage_table_showHideColumn

        // 读取回收站数据
        var recycleCols = localStorage.getItem(tableId + '_cols_index_recycle');
        recycleCols = recycleCols == null ? [] : recycleCols.split(',');

        var basicHtml = `<div class="instrumentsShowHideModal title">
                            <input type="checkbox" class="beauty-checkbox-big" id="basic_title" />
                            <label for="basic_title">${mainLang('basic_info')}</label>
                        </div>
                        <ul class="ml30 exp-list-cols clear basic">`;
        var manageHtml = `<div class="instrumentsShowHideModal title">
                            <input type="checkbox" class="beauty-checkbox-big" id="manage_title" />
                            <label for="manage_title">${mainLang('manage_info')}</label>
                        </div>
                        <ul class="ml30 exp-list-cols clear manage">`;

        var recycleHtml = `<div class="instrumentsShowHideRecycle title relative" style="margin-left: 30px">${mainLang('recycle_bin')}
                            <div class="fold collapse" title="${mainLang('expand')}" ></div>
<!--                            style="margin-left: -85px"-->
                        </div>
                        <ul class="ml30 exp-list-cols clear recycle instrument_hide">`;

        var th = $(`.exp_conetnt.active #${tableId}`).find('tr th');
        th.each(function(index, item) {
            var checked = ($(this).is(':visible') || index === 0) ? 'checked' : '';

            if (recycleCols.includes((index + 1).toString())) {
                var liClass = $(item).hasClass('basic_field') ? 'basic' : 'manage';
                recycleHtml += `<li class="col-item ${liClass}" data-index="${index}" ${index === 0 ? 'style="display:none"' : ''}>
                                    <input type="checkbox" class="instrument_hide" id="idForShowColumn${index}" ${checked} />
                                     <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                                         <i class="instrument-move-up moveOutRecycle" title="${mainLang('move_recycle_out')}"> </i>
                                     </label>
                                 </li>`
            } else {
                if ($(item).hasClass('basic_field')) {
                    basicHtml += `<li class="col-item basic" data-index="${index}" ${index === 0 ? 'style="display:none"' : ''}>
                                     <input type="checkbox" class="beauty-checkbox-big " id="idForShowColumn${index}" ${checked} />
                                     <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                                        <i class="instrument-move-down moveInRecycle" title="${mainLang('move_recycle')}"> </i>
                                     </label>
                                  </li>`
                }
                if ($(item).hasClass('manage_field')) {
                    manageHtml += `<li class="col-item manage" data-index="${index}">
                                     <input type="checkbox" class="beauty-checkbox-big" id="idForShowColumn${index}" ${checked} />
                                     <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                                       <i class="instrument-move-down moveInRecycle" title="${mainLang('move_recycle')}"> </i>
                                     </label>
                                    </li>`
                }
            }


        });
        basicHtml +='</ul>';
        manageHtml +='</ul>';
        recycleHtml +='</ul>';
        var html = `<div class="instrumentShowModalContents">${basicHtml}${manageHtml}${recycleHtml}<div>`;

        $.popContent(html, mainLang('show_hidden'), function() {

            var table =  $(`.exp_conetnt.active #${tableId}`);

            // 存储隐藏列的索引
            var colIndexArray = [];
            $('.instrumentShowModalContents .exp-list-cols .col-item .beauty-checkbox-big:checkbox').each(function(index, item) {
                var colIndex = parseInt($(item).parent('li').attr('data-index'), 10) + 1;
                if ($(this).is(':checked')) {
                    table.find(`td:nth-child(${colIndex })`).show();
                    table.find(`th:nth-child(${colIndex })`).show();
                } else {
                    table.find(`td:nth-child(${colIndex })`).hide();
                    table.find(`th:nth-child(${colIndex })`).hide();
                    colIndexArray.push(colIndex); // 存储隐藏列索引
                }
            });
            localStorage.setItem(tableId + '_cols_index', colIndexArray.join());

            // 存储回收站的索引
            var recycleIndexArray = []; // 存储隐藏列的索引
            $('.exp-list-cols.recycle li.col-item').each(function (index, item) {
                var colIndex =  parseInt($(item).attr('data-index'),10) + 1;
                table.find(`td:nth-child(${colIndex})`).hide(); // 回收站默认都不显示
                table.find(`th:nth-child(${colIndex})`).hide(); // 回收站默认都不显示
                recycleIndexArray.push(colIndex);
            });
            localStorage.setItem(tableId + '_cols_index_recycle', recycleIndexArray.join());

            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        },function () {

            // 判断是否全选基础信息字段
            if ($('.instrumentShowModalContents ul.basic input').length === $('.instrumentShowModalContents ul.basic input:checked').length) { // 判断子节点是否都选中则选中标题全选框
                $('.instrumentShowModalContents input#basic_title').prop('checked', true)
            }
            // 判断是否全选管理信息字段
            var allNumber = $('.instrumentShowModalContents ul.manage input').length;
            var allCheckedNumber = $('.instrumentShowModalContents ul.manage input:checked').length;
            if (allCheckedNumber > 0 && allCheckedNumber === allNumber) {
                $('.instrumentShowModalContents input#manage_title').prop('checked', true)
            }
        });
    });

    // add by hkk 2022/5/24 显示隐藏列 移入回收站 2022/8/10弃用
    $('body').on('click', '.instrumentShowModalContents i.moveInRecycle', function() {

        var moveNode = $(this).parents('li');

        moveNode.find('input').prop('checked', false);// 取消勾选
        moveNode.find('input').addClass('instrument_hide'); // 隐藏打勾
        moveNode.find('input').removeClass('beauty-checkbox-big');// 隐藏打勾
        moveNode.find('i').addClass('instrument-move-up'); // 改移入图标
        moveNode.find('i').removeClass('instrument-move-down'); // 改移入图标
        moveNode.find('i').addClass('moveOutRecycle'); // 改移入事件
        moveNode.find('i').removeClass('moveInRecycle'); // 改移入事件
        moveNode.find('i').attr('title',mainLang('move_recycle_out')); // 悬浮提示

        var recycleUl = $('.instrumentShowModalContents ul.recycle');
        recycleUl.append(moveNode);
        sortLi(recycleUl)

    });

    // add by hkk 2022/5/24 显示隐藏列 还原移出回收站 2022/8/10弃用
    $('body').on('click', '.instrumentShowModalContents i.moveOutRecycle', function() {

        var moveNode = $(this).parents('li');

        moveNode.find('input').removeClass('instrument_hide'); // 显示打勾
        moveNode.find('input').addClass('beauty-checkbox-big');// 显示打勾
        moveNode.find('i').addClass('instrument-move-down');
        moveNode.find('i').removeClass('instrument-move-up');
        moveNode.find('i').addClass('moveInRecycle');
        moveNode.find('i').removeClass('moveOutRecycle');
        moveNode.find('i').attr('title',mainLang('move_recycle'));
        if (moveNode.hasClass('basic')) { // 还原到基础信息,还原后排序
            var basicUl =  $('.instrumentShowModalContents ul.basic');
            basicUl.append(moveNode);
            sortLi(basicUl)
        } else { // 还原到管理信息
            var manageUl =  $('.instrumentShowModalContents ul.manage');
            manageUl.append(moveNode);
            sortLi(manageUl)
        }
    });

    // add by hkk 2022/5/25 显示隐藏列 勾选事件绑定1
    $('body').on('change', '.instrumentShowModalContents input#basic_title', function() {
        if ($(this).prop('checked')) { // 设置全选
            $('.instrumentShowModalContents ul.basic input').prop('checked',true)  // 管理基础信息下的所有都选中
        } else { // 取消全选
            $('.instrumentShowModalContents ul.basic input:not(#idForShowColumn0)').prop('checked',false)  // 隐藏的首字段‘全选’始终勾选
        }
    });

    // add by hkk 2022/5/25 显示隐藏列 勾选事件绑定2
    $('body').on('change', '.instrumentShowModalContents input#manage_title', function() {
        if ($(this).prop('checked')) { // 设置全选
            $('.instrumentShowModalContents ul.manage input').prop('checked',true) // 管理基础信息下的所有都选中
        } else { // 取消全选
            $('.instrumentShowModalContents ul.manage input').prop('checked',false)
        }
    });

    // add by hkk 2022/5/25 显示隐藏列 勾选事件绑定3
    $('body').on('change', '.instrumentShowModalContents ul.basic input', function () {
        var allNumber = $('.instrumentShowModalContents ul.basic input').length;
        var allCheckedNumber = $('.instrumentShowModalContents ul.basic input:checked').length;
        if ($(this).prop('checked')) { // 判断子节点是否都选中则选中标题全选框
            if (allNumber === allCheckedNumber) {
                $('.instrumentShowModalContents input#basic_title').prop('checked', true)
            }
        } else { // 取消标题全选框
            $('.instrumentShowModalContents input#basic_title').prop('checked', false)
        }
    });

    // add by hkk 2022/5/25 显示隐藏列 勾选事件绑定4
    $('body').on('change', '.instrumentShowModalContents ul.manage input', function () {
        var allNumber = $('.instrumentShowModalContents ul.manage input').length;
        var allCheckedNumber = $('.instrumentShowModalContents ul.manage input:checked').length;
        if ($(this).prop('checked')) { // 判断子节点是否都选中则选中标题全选框
            if (allNumber === allCheckedNumber) {
                $('.instrumentShowModalContents input#manage_title').prop('checked', true)
            }
        } else {  // 判断子节点是否未选中则取消标题全选框
          $('.instrumentShowModalContents input#manage_title').prop('checked', false)
        }
    });

    // add by hkk 2022/5/25 显示隐藏列 展开收起回收站 2022/8/10弃用
    $('body').on('click', '.instrumentShowModalContents .collapse', function () {
        if($(this).hasClass('fold')){
            $(this).removeClass('fold');
            $(this).addClass('unfold');
            $(this).attr('title',mainLang('fold'));
            $('.instrumentShowModalContents ul.recycle').removeClass('instrument_hide');
        }else {
            $(this).removeClass('unfold');
            $(this).addClass('fold');
            $(this).attr('title',mainLang('expand'));
            $('.instrumentShowModalContents ul.recycle').addClass('instrument_hide');
        }
    });

    // add by zsm 2025/5/8 批量设置预约规则
    $('body').on('click', '.instruments_manage_style .reservationRuleSettings', function () {
        // 获取选中的仪器记录id
        var chooseIds = [];
        var chooseName = [];
        $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
            chooseName.push(`${$(this).parents('tr').children('td[data-field="name"]').text()}(${$(this).parents('tr').children('td[data-field="batch_number"]').text()})` );
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip23'));
            return
        }
        renderComponent('/vue-ui/dist/InstrumentBookingConfigDialog.js', '#instrument-booking-config-dialog', {
            instrument_booking_config_chooseIds: chooseIds,
            instrument_booking_config_chooseName: chooseName,
            cancel: function() {
                // unrenderComponent
                unrenderComponent('#instrument-booking-config-dialog');
                $('#instrument-booking-config-dialog').hide();
            }
        });
        $('#instrument-booking-config-dialog').show();


    })
    // add by hkk 2022/8/10 显示隐藏列 仪器库管理和我的仪器库专有
    $('body').on('click', '.exp_conetnt.active .instrumentShowHideColumn', function() {
        var tableId = $(this).attr('data-tableId'); // instrument_manage_table_showHideColumn
        var basicHtml = `<div class="instrumentsShowHideModal title">
                            <input type="checkbox" class="beauty-checkbox-big" id="basic_title" />
                            <label for="basic_title">${mainLang('check_all')}</label>
                        </div>
                        <ul class="ml30 exp-list-cols clear basic">`;
        // var manageHtml = `<div class="instrumentsShowHideModal title">
        //                     <input type="checkbox" class="beauty-checkbox-big" id="manage_title" />
        //                     <label for="manage_title">${mainLang('manage_info')}</label>
        //                 </div>
        //                 <ul class="ml30 exp-list-cols clear manage">`;
        var th = $(`.exp_conetnt.active #${tableId}`).find('tr th');
        th.each(function(index, item) {
            var checked = ($(this).is(':visible') || index === 0) ? 'checked' : '';
            var field = $(this).attr('data-field'); // 从索引改为记录具体显示的字段
            if ($(item).hasClass('basic_field')) {
                basicHtml += `<li class="col-item basic" data-index="${field}" ${index === 0 ? 'style="display:none"' : ''}>
                                 <input type="checkbox" class="beauty-checkbox-big " id="idForShowColumn${index}" ${checked} />
                                 <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                                 </label>
                              </li>`
            }
            // if ($(item).hasClass('manage_field')) {
            //     manageHtml += `<li class="col-item manage" data-index="${field}">
            //                      <input type="checkbox" class="beauty-checkbox-big" id="idForShowColumn${index}" ${checked} />
            //                      <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
            //                      </label>
            //                     </li>`
            // }
        });
        basicHtml +='</ul>';
        // manageHtml +='</ul>';
        var html = `<div class="instrumentShowModalContents">${basicHtml}<div>`;

        $.popContent(html, mainLang('show_hidden'), function() {

            var table =  $(`.exp_conetnt.active #${tableId}`);

            // 存储隐藏列的索引
            var colNameArray = [];
            $('.instrumentShowModalContents .exp-list-cols .col-item .beauty-checkbox-big:checkbox').each(function (index, item) {
                var colIndex = $(item).parent('li').attr('data-index')
                if ($(this).is(':checked')) {
                    table.find(`td[data-field="${colIndex}"]`).show();
                    table.find(`th[data-field="${colIndex}"]`).show();

                } else {
                    table.find(`td[data-field="${colIndex}"]`).hide();
                    table.find(`th[data-field="${colIndex}"]`).hide();
                    colNameArray.push(colIndex);
                }
            });
            localStorage.setItem(tableId + '_cols_index', colNameArray.join());

            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        },function () {

            // 判断是否全选基础信息字段
            if ($('.instrumentShowModalContents ul.basic input').length === $('.instrumentShowModalContents ul.basic input:checked').length) { // 判断子节点是否都选中则选中标题全选框
                $('.instrumentShowModalContents input#basic_title').prop('checked', true)
            }
            // 判断是否全选管理信息字段
            // var allNumber = $('.instrumentShowModalContents ul.manage input').length;
            // var allCheckedNumber = $('.instrumentShowModalContents ul.manage input:checked').length;
            // if (allCheckedNumber > 0 && allCheckedNumber === allNumber) {
            //     $('.instrumentShowModalContents input#manage_title').prop('checked', true)
            // }
        });
    });

    // add by hkk 2022/8/9 字段配置
    $('body').on('click', '.exp_conetnt.active #search-instrument-manage .fieldConfig', function() {

        // 发请求读取当前数据库字段配置
        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-instrument-field-config',
            data: [],
            success: function (data) {
                if (data.status ===1) {

                    var showFields = data.data.showFields.split(',');
                    var allFields = data.data.allFields.split(',');
                    var fieldMap = data.data.fieldMap;
                    var listHtml = '';
                    for (var i = 0; i < allFields.length; i++) {
                        var checked = showFields.includes(allFields[i]) || allFields[i] === 'name' || allFields[i] === 'status' ? 'checked' : '';
                        var filedName = fieldMap[allFields[i]];
                        var disabled = allFields[i] === 'name' || allFields[i] === 'status' ? 'disabled' : '';
                        if (filedName) {
                            listHtml += `<li class="col-item basic ${disabled}" data-index="${allFields[i]}"}>
                                     <input type="checkbox" class="beauty-checkbox-big " id="idForShowColumn${i}" ${checked} />
                                     <label for="idForShowColumn${i}" title="${filedName}">
                                     <span>${filedName}</span>
                                     </label>
                                  </li>`
                        }
                    }

                    var html = `<div class=" title" style="margin-bottom: 10px;font-size: 16px;">
                             ${mainLang('set_field_config_tip')}</div>
                            <ul class="instrumentsFieldsConfig ml30 exp-list-cols clear basic">${listHtml}</ul> `;


                    $.popContent(html, mainLang('field_config'), function() {

                        // 获取当前字段配置存储数据库；
                        var fieldConfig = '';
                        $('.instrumentsFieldsConfig .col-item .beauty-checkbox-big:checkbox').each(function (index, item) {
                            if ($(this).is(':checked')) {
                                fieldConfig += ($(item).parent('li').attr('data-index') + ',' );
                            }
                        });

                        fieldConfig = fieldConfig.slice(0, -1);
                        $.ajaxFn({
                            url: ELN_URL + '?r=instrument/set-instrument-field-config',
                            data: {
                                fieldConfig: fieldConfig,
                            },
                            success: function (data) {
                                if (data.status === 1) {
                                    // 关闭删除弹框 更新列表
                                    $(".instrumentsFieldsConfig").parents('.pop_modal').modal('hide');
                                    require('tab').reloadActiveTag(); // 刷新页面
                                }
                            }
                        });



                    });
                }
            }
        });

    });


    // add by hkk 2021/6/1 批量删除弹框
    $('body').on('click', '.instruments_manage_style .batchDelete', function() {


        // 获取选中的仪器记录id
        var chooseIds = [];
        $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip23'));
            return
        }


        $.ajaxFn({
            url: ELN_URL + '?r=instrument/instrument-batch-delete',
            data: {
                instrumentIds: chooseIds,
            },
            type: 'POST',
            success: function (data) {
                if (data.status === 1) {
                    var html = `<div>` + mainLang('delete_instrument_tip_batch') + `</div>`;
                    if (data.data.hasApprovalInstrumentIds != '') {
                        html += `<div class="instrument_status_change_reason" ">` +
                            `<textarea class="textarea" style="width: 400px;height: 60px" placeholder="` + mainLang('please_input_reason') + `"></textarea>` +
                            `</div>`;
                    }
                    if (data.data.approvalUser != '') {
                        html += `<div class="approval_route" data-id="` + data.data.approvalUserIds + `" style=" line-height: 40px">` +
                            mainLang('approve') + `: <span class="approver marginLeft10">` + data.data.approvalUser + `</span>` +
                            `</div>`;
                    }
                    $.showContent(mainLang('delete_instrument'), mainLang('delete_instrument'), html, function () {
                        var instrument_status_change_reason= $('.instrument_status_change_reason .textarea').val();
                        var approvalUserId = $('.approval_route').attr('data-id');
                        $.ajaxFn({
                            url: ELN_URL + '?r=instrument/delete-submit',
                            data: {
                                instrumentIds: chooseIds,
                                instrument_status_change_reason: instrument_status_change_reason,
                                approvalUserId: approvalUserId
                            },
                            success: function (data) {
                                if (data.status === 1) {
                                    $.closeModal();
                                    if(approvalUserId){
                                        $.showAlert(mainLang('submit_wait_sign'));
                                    }else{
                                        $.showAlert(mainLang('delete_successful'));
                                    }
                                    $('.exp_conetnt.active .search-instrument-manage[data-type = "instruments_manage"]').click();
                                }
                            }
                        })
                    })
                }
            }
        });


        return false // 防止冒泡


    });

    // add by hkk 2021/6/1 批量设置(仪器记录复核时间设置)，确定事件在原来的eln_setting.js instrument_conform_btn事件里
    $('body').on('click', '.instruments_manage_style .recordBatchSetting', function() {

        // 获取选中的仪器记录id
        var chooseIds = [];
        $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip23'));
            return
        }

        var data = {};
        if (chooseIds.length === 1) { // 相当于单个设置，渲染逻辑不一样，页面会渲染已有设置
            data.instrumentId = chooseIds[0];
            data.type = 'record_setting';
        } else {
            data.type = 'record_setting_batch';
        }

        $.ajaxFn({
            url: ELN_URL + '?r=instrument/get-page',
            data:data,
            success: function (data) {
                if (data.status === 1) {
                    $(".instrument_page").remove();
                    $("body").append(data.data.file);
                    $(".instrument_page").modal('show');

                }
            }
        });


    });

    // add by hkk 2022/6/23 批量调整弹框
    $('body').on('click', '.instruments_manage_style .batch-adjust-menu li.batch-change', function() {

        // 获取选中的仪器记录id
        var chooseIds = [];
        $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
            chooseIds.push($(this).parents('tr').attr('data-id'));
        });
        if (chooseIds.length === 0) {
            $.showAlert(mainLang('instrument_reminder_tip23'));
            return
        }

        var type = $(this).attr('data-type');
        var html = '';
        switch (type) {
            case 'group': // 鹰群
                html = `<label class="label mt5" style="line-height: 28px;margin-left: 20px">${mainLang('belong_group')}：</label>
                      <div class="data-box-ineln iblock data-box-ineln-new">
                           <div class="view-checked-box checkbox_box iblock" style="width: 300px;">
                                 <input  idbox="" value="" readonly
                                    style="height: 28px;"
                                    type="text" name="group_ids" id="group_ids"
                                    class="input pr34 name-box check_input mouse check_department"
                                    placeholder="${mainLang('select_group')}"
                                 >
                                 <span class="font-ico clear-ico clear_check_btn" style="display: none;"></span>
                           </div>
                      </div> `
                break;
            case 'department':// 部门
                html = `<label class="label mt5" style="line-height: 28px">${mainLang('belong_department')}：</label>
                          <div style="position:relative;display: inline-block;width: 280px;" class="dept_select_wrapper">
                            <input class="selected_depts" type="hidden" value="" />
                            <span class="clear_select" style="position: absolute;height: 100%;color: #aaa; font-size: 18px;top: 0;right: 10px;cursor: pointer;display:none">
                                <span style="line-height: 100%">✕</span>
                            </span>
                            <input type="text" class="dept_select"
                                   readonly multi  empty
                                   style="width: 100%;height: 100%;overflow: hidden;text-overflow: ellipsis;border: 1px solid #dcdcdc;white-space: nowrap;padding-right: 32px;cursor:pointer;"
                                   name="instrument_belong_department"
                                   placeholder="${mainLang('please_select_department')}"
                                   title=""
                                   value=""
                                   data=""
                                   data-freeNode = "1"
                                   y=""
                                   n=""
                            />
                           </div> `

                break;
            case 'instrument_type': // 分类
                var typeList = $(this).attr('data-typeList').split(',');
                var optionHtml = `<option value="0" >${mainLang('select')}</option>`;
                for (var i = 0; i < typeList.length; i++) {
                    optionHtml += `<option value="${typeList[i]}">${typeList[i]}</option>`;
                }
                html = `<label class="label mt5" style="line-height: 28px;margin-left: 25px">${mainLang('instrument_type')}：</label>
                        <select class="angle_input iblock beauty-disabled-select"
                        style="width:250px;overflow: hidden"
                        name="instrument_type" id="instrument_type">
                            ${optionHtml}
                        </select>`;
                break;
            case 'person_in_charge': // 维护人

                html = `<label class="label mt5" style="line-height: 28px;margin-left: 25px">${mainLang('in_charge_user')}：</label>
                         <div class="iblock visible-user-selector-box  multiple in_charge_user " style="width: 300px">
                            <input class="input visible-user-input " type="text"
                                   name="user_ids"
                                   id="user_ids"
                                   idbox=""
                                   title=""
                                   value=""
                                   initIdBox=""
                                   initValue=""
                                   exceptUsersIds=""
                                   placeholder="${mainLang('please_select_user')}"
                                   userStatus="1,2"
                                   readonly>
                        </div> `
                break;
            case 'response_person': //  负责人
                html = `<label class="label mt5" style="line-height: 28px;margin-left: 25px">${mainLang('responsible_person')}：</label>
                         <div class="iblock visible-user-selector-box  multiple responsible_person" style="width: 300px">
                            <input class="input visible-user-input " type="text"
                                   name="user_ids"
                                   id="user_ids"
                                   idbox=""
                                   title=""
                                   value=""
                                   initIdBox=""
                                   initValue=""
                                   exceptUsersIds=""
                                   placeholder="${mainLang('please_select_user')}"
                                   userStatus="1,2"
                                   readonly>
                         </div>
                      `
                break;
            case 'maintainer': // 维修人
                html = `<label class="label mt5" style="line-height: 28px;margin-left: 60px">${mainLang('maintenance_person')}：</label>
                         <div class="iblock visible-user-selector-box  multiple maintenance_person" style="width: 300px">
                            <input class="input visible-user-input " type="text"
                                   name="user_ids"
                                   id="user_ids"
                                   idbox=""
                                   title=""
                                   value=""
                                   initIdBox=""
                                   initValue=""
                                   exceptUsersIds=""
                                   placeholder="${mainLang('please_select_user')}"
                                   userStatus="1,2"
                                   readonly>
                        </div>`
                break;
            default:
                break;
        }


        $.showContent('batch-change', mainLang('batch_adjust'), html, function () {
            var value = '';
            switch (type) {
                case 'group': // 所属鹰群
                    value = $('div.batch-change .data-box-ineln-new input#group_ids').attr('idbox');
                    break;
                case 'department':
                    value = $('div.batch-change .dept_select_wrapper input.dept_select').attr('data'); // add by hkk 2022/6/27
                    break;
                case 'instrument_type':
                    value = $('div.batch-change select[name="instrument_type"]').val();
                    break;
                case 'person_in_charge':
                case 'response_person':
                case 'maintainer':
                    value = $('div.batch-change input.visible-user-input#user_ids').attr('idbox');
                    break;
                default:
                    break;
            }

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/batch-adjust',
                data: {
                    instrumentIds:chooseIds,
                    type,
                    value,
                },
                success: function (data) {
                    if (data.status === 1) {
                        $.closeModal();

                        $.showAlert(mainLang('success'));

                        searchInstrumentManage(); // 刷新页面

                    }
                }

            })

        })

        return false // 防止冒泡


    });

    // add by hkk 2022/5/24 显示隐藏列 子节点排序函数
    function sortLi(ulNode){
        var children = ulNode.find('li');
        var arr = [];
        for (var i = 0; i < children.length; i++) {
            arr[i] = children[i];
        }
        arr.sort(function (li1, li2) {
            var n1 = parseInt(li1.getAttribute('data-index').match(/\d+$/)[0]);
            var n2 = parseInt(li2.getAttribute('data-index').match(/\d+$/)[0]);
            return n1 - n2;
        })

        for (var i = 0; i < arr.length; i++) {
            ulNode.append(arr[i]);   //排序之后再写入
        }

    }

    // add by hkk 2022/5/26 仪器库排序事件
    $('body').on('click', '.instruments_table .sort-field .sort-caret', function(){


        // 获取查询条件和页数信息
        var box = $(".exp_conetnt.active #search-instrument-manage");
        var data = $.formSerializeFn(box);
        data['instrument_create_user'] = box.find('input[name=instrument_create_user]').attr('idbox');
        data['instrument_maintenance_user'] = box.find('input[name=instrument_maintenance_user]').attr('idbox');
        data['instrument_in_charge_user'] = box.find('input[name=instrument_in_charge_user]').attr('idbox');
        data['instrument_responsible_user'] = box.find('input[name=instrument_responsible_user]').attr('idbox');
        data['instrument_belong_group'] = $('.exp_conetnt.active #search-instrument-manage input[name=group_ids]').attr('idbox');
        data['instrument_belong_department'] = $('.exp_conetnt.active #search-instrument-manage input.dept_select').attr('data'); // add by hkk 2022/7/11

        data['page'] = 1;
        data['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
        data['needUpdateAllPage'] = 0;
        data['type'] = $('.exp_conetnt.active .search-instrument-manage').attr('data-type');

        data['order_type'] = 'default';
        if ($(this).hasClass('ascending')) { // 点击升序
            if (!$(this).parents('th').hasClass('ascending')) {
                data['order_type'] = 'asc'
            }
        } else { // 点击降序
            if (!$(this).parents('th').hasClass('descending')) {
                data['order_type'] = 'desc'
            }
        }
        data['order_field'] = $(this).parents('th').attr('data-field');
        if ($(this).parents('th').hasClass('basic_define_field')) {
            data['order_field_type'] = 'define';
        } else if ($(this).parents('th').hasClass('manage_field')) {
            data['order_field_type'] = 'manage';
        } else {
            data['order_field_type'] = 'basic';
        }


        $.ajaxFn({
            url: ELN_URL + '?r=instrument/manage',
            data: data,
            type: 'POST',
            success: function (data) {
                if (data.status === 1) {
                    $('.exp_conetnt').removeClass('search');
                    $('.exp_conetnt.active .instruments_table').html(data.data.file);

                }
            }
        });

    });

    // add by hkk 2020/7/24 维修记录 运行记录 校验记录 删除文件
    $('body').on('click', '.instrument_add_repair_record_page .del_file_with_record,' +
        '.instrument_begin_operate_record_page .del_file_with_record,' +
        '.instrument_add_check_record_page .del_file_with_record', function() {
        var box = $(this);
        $.showContent('warning', mainLang('del_tips'), mainLang('del_file'), function() {
            var part = box.parents('.single_detail_file');
            part.remove();
            $('.pop_modal').modal('hide');
            window.noLoadTip = false;
        });

    });

    // add by hkk 2020/7/24 维修记录 运行记录 校验记录 上传文件
    $('body').on('click', '.instrument_add_repair_record_page .detail_upload_file,' +
        '.instrument_begin_operate_record_page .detail_upload_file,' +
        '.instrument_add_check_record_page .detail_upload_file', function() {
        var input = $(this);
        var addFileWidth = "width: 150px;max-width:200px";
        input.ajaxfileupload({
            action: ELN_URL + '?r=upload/upload-file',
            params: {
                type: '2'
            },

            validate_extensions: false,
            onComplete: function(res) {
                $.closeLoading();
                //edge浏览器下，即使下面的onstart return false了。依然会走到这里来。
                //会影响start中的判断提示。
                if (!res) {
                    return;
                }
                if (res.status !== 1) {
                    input[0].outerHTML = input[0].outerHTML;
                    $.showAlert(res.info || mainLang('upload_file_error'));
                    return;
                }
                window.noLoadTip = false;

                var fileNameString = '';
                for(var i=0; i<res.data.length; i++) {
                    var fileData = res.data[i];
                    var html = '<div style="text-align: left;margin: 4px;" class="single_detail_file"> <span class="ref_file_part file_up_box"><span class="file_name" title="' + fileData.file_name + '" style="' + addFileWidth + '">' + fileData.file_name + '</span>' +
                        '<a style="margin-left:5px"  class="_btn del_file_with_record del_ico" title="' + mainLang('del') + '"></a>' +
                        '<a class="download_ico" target="_blank" title="' + mainLang('download') + '" href=?r=download/file&path=' + fileData.dep_path + '&name=' + fileData.save_name + '&file_name=' + encodeURIComponent(fileData.file_name) + '></a>' +
                        '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                        '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                        '<input type="hidden" name="file_name" value="' + fileData.file_name + '" /></span></div>';
                    input.parents('.upload_file').next().append(html)

                    fileNameString += fileData.file_name + ',';
                }
                input.val('');
            },
            onStart: function(a) {

                var dom = this;

                if (dom[0].files && dom[0].files[0]) {
                    if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                        $.showAlert(mainLang('file_too_big'));
                        setTimeout(function() {
                            $.closeLoading();
                        }, 100);
                        return false;
                    }
                }
                $.loading();
            },
            onCancel: function() {
                $.closeLoading();
                input.val('');
                // console.log('onCancel');
            }
        });
    });

    $('body').on('click', '.exp_conetnt.active .instrumentRecordShowHideColumn', function () {
        var tableId = $(this).attr('data-tableId');
        var th = $(`.exp_conetnt.active #${tableId}`).find('tr th');
        var html = '<ul class="ml30 exp-list-cols clear">';
        th.each(function (index, item) {
            var colFlag = $(this).attr('data-field');
            var checked = $(this).is(':visible') ? 'checked' : '';

            // 去掉全选后为空，不显示到隐藏列
            var showItem = $(this).data('field') ? '' : 'style="display: none"';
            html += '<li class="col-item" ' + showItem + '>' +
                '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '"' + checked + '/>' +
                '<label for="' + colFlag + '" title="' + $(this).text() + '">' + $(this).text() + '</label>' +
                '</li>';
        });

        html += '</ul>';
        $.popContent(html, mainLang('show_hidden'), function () {
            var colIndexArray = []; // 存储隐藏列的索引
            $('.exp-list-cols .col-item :checkbox').each(function (index, item) {
                if ($(this).is(':checked')) {
                    $(`.exp_conetnt.active #${tableId}`).find('td[data-field = "' + item.id + '"]').show();
                    $(`.exp_conetnt.active #${tableId}`).find('th[data-field = "' + item.id + '"]').show();
                } else {
                    $(`.exp_conetnt.active #${tableId}`).find('td[data-field = "' + item.id + '"]').hide();
                    $(`.exp_conetnt.active #${tableId}`).find('th[data-field = "' + item.id + '"]').hide();
                    colIndexArray.push(item.id); // 存储隐藏列索引
                }
            });
            localStorage.setItem(tableId + '_cols_index', colIndexArray);
            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        });
    });

    // 批量上传
    require(['bin/ajaxfileupload'], function () {

        // add by hkk 2020/8/5 批量上传仪器
        $("body").on('click', '.upload-batch-instruments', function (event) {
            $(this).ajaxfileupload({
                action: ELN_URL + '?r=upload/upload-file',
                type: 'post',
                params: {
                    type: 2
                },
                validate_extensions: false,
                onComplete: function (data) {
                    $.closeLoading();
                    if (data.status == '1') {
                        var file = data.data;
                        // 判断文件类型
                        var fileType = file.file_type;
                        var filename = file.save_name;
                        var index = filename.lastIndexOf(".");
                        var suffix = filename.substring(index+1);
                        // 判断用后缀不用类型防止 wps生成的文件头 application/vnd.ms-office application/octet-stream 不被识别
                        if (suffix != 'xls' && suffix != 'xlsx') {
                            $.showAlert(mainLang('only_access_xls'));
                            return false;
                        }

                        //判断文件大小
                        if (Math.ceil(file.file_size / 1024) > 5120) {
                            $.showAlert(mainLang('file_more_5m'));
                            return false;
                        }

                        $(".upload_form_for_instrument_batchAdd #dep_path").val(file.dep_path);
                        $(".upload_form_for_instrument_batchAdd #save_name").val(file.save_name);
                        $(".upload_form_for_instrument_batchAdd #file_name").val(file.file_name);
                        $("#temp_file_name").html(file.file_name);
                        // that.url = file.img_url;
                        // that.uploadFileData = file;
                    }
                },
                onStart: function (a) {
                    var dom = this;

                    if (dom[0].files && dom[0].files[0]) {
                        if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                            $.showAlert(mainLang('file_too_big'));
                            setTimeout(function () {
                                $.closeLoading();
                            }, 100);
                            return false;
                        }
                    }
                    $.loading();
                },
            });
        });

        // add by hkk 2021/6/3 批量上传编辑仪器
        $("body").on('click', '.upload-batch-edit-instruments', function (event) {
            $(this).ajaxfileupload({
                action: ELN_URL + '?r=upload/upload-file',
                type: 'post',
                params: {
                    type: 2
                },
                validate_extensions: false,
                onComplete: function (data) {
                    $.closeLoading();
                    if (data.status == '1') {
                        var file = data.data;
                        //判断文件类型
                        var fileType = file.file_type;
                        var filename = file.save_name;
                        var index = filename.lastIndexOf(".");
                        var suffix = filename.substring(index+1);
                        //判断用后缀不用类型防止 wps生成的文件头 application/vnd.ms-office application/octet-stream 不被识别
                        if( suffix != 'xls' && suffix != 'xlsx'){
                            $.showAlert(mainLang('only_access_xls'));
                            return false;
                        }

                        //判断文件大小
                        if (Math.ceil(file.file_size / 1024) > 5120) {
                            $.showAlert(mainLang('file_more_5m'));
                            return false;
                        }


                        // 开始处理上传的文件
                        if(!file['file_name']){
                            $.showAlert(mainLang('please_upload_file'));
                            return
                        }

                        file['unCheckedField'] = localStorage.getItem('instrument_batch_edit_fields');
                        $.loading();
                        $.ajaxFn({
                            url: ELN_URL + '?r=instrument/submit-batch-edit',
                            data: file,
                            success: function (data) {
                                $('.upload-batch-edit-instruments').val('');
                                if (1 == data.status) {

                                    $.closeLoading();
                                    $.showAlert(mainLang('batch_edit_successful'));

                                    //刷新页面
                                    require('tab').reloadActiveTag(); // 刷新页面

                                }
                            }
                        });


                        // that.url = file.img_url;
                        // that.uploadFileData = file;
                    }
                },
                onStart: function (a) {
                    var dom = this;

                    if (dom[0].files && dom[0].files[0]) {
                        if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                            $.showAlert(mainLang('file_too_big'));
                            setTimeout(function () {
                                $.closeLoading();
                            }, 100);
                            return false;
                        }
                    }
                    $.loading();
                },
            });
        });


        // add by hkk 2022/8/8 配置导出字段 勾选事件绑定1
        $('body').on('change', '#export_config', function() {
            console.log('111');
            if ($(this).prop('checked')) { // 设置全选
                $('.instrumentShowModalContents ul .col-item input').prop('checked',true)
            } else { // 取消全选
                $('.instrumentShowModalContents ul .col-item input:not(#idForShowColumn0)').prop('checked',false)
            }
        });

        // add by hkk 2022/8/8 配置导出字段 勾选事件绑定2
        $('body').on('change', '.instrumentShowModalContents ul .col-item input', function () {
            var allNumber = $('.instrumentShowModalContents ul .col-item input').length;
            var allCheckedNumber = $('.instrumentShowModalContents ul .col-item input:checked').length;
            if ($(this).prop('checked')) { // 判断子节点是否都选中则选中标题全选框
                if (allNumber === allCheckedNumber) {
                    $('#export_config').prop('checked', true)
                }
            } else { // 取消标题全选框
                $('#export_config').prop('checked', false)
            }
        });

        // 仪器状态改变添加审批
        $('body').on('click', '#status_change_need_approval_submit', function () {
            var that = this;
            var data = {
                instrument_status_change_reason: $('.instrument_status_change_reason').val(),
                instrument_status: $('.approval_route').attr('data-id'),
                instrument_id: $(this).attr('data-id'),
            }

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/create-status-approval',
                data: data,
                success: function (data) {
                    if (1 == data.status) {

                        $(".instrument_page").remove();
                        $(".instrument_status_change_approval").remove();
                        $('body').removeClass('modal-open');
                    }
                }
            });
        })

        // 仪器删除按钮提交
        $('body').on('click', '#instrument_delete_submit', function () {
            var that = this;
            var type = $(this).attr('data-type');
            var instrumentIds = $(this).attr('data-id');
            var approvalUserId = $('.approval_route').attr('data-id');
            var instrument_status_change_reason= $('.instrument_status_change_reason').val();

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/delete-submit',
                data: {
                    instrumentIds:instrumentIds,
                    type: type,
                    approvalUserId: approvalUserId,
                    instrument_status_change_reason:instrument_status_change_reason
                },
                success: function (data) {
                    if (1 == data.status) {
                        $(".instrument_delete").remove();
                        $('body').removeClass('modal-open');
                        if(approvalUserId){
                            $.showAlert(mainLang('submit_wait_sign'));
                        }else{
                            $.showAlert(mainLang('delete_successful'));
                        }
                        $('.exp_conetnt.active .search-instrument-manage[data-type = "instruments_manage"]').click();
                    }
                }
            });
        })

        //批量调整仪器状态
        $('body').on('click', '.instruments_manage_style .batch-adjust-menu li.batch-change-status', function() {
            // 获取选中的仪器记录id
            var chooseIds = [];
            $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
                chooseIds.push($(this).parents('tr').attr('data-id'));
            });
            if (chooseIds.length === 0) {
                $.showAlert(mainLang('instrument_reminder_tip23'));
                return
            }

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/instrument-status-batch-change',
                data: {
                    instrumentIds: chooseIds,
                },
                type: 'POST',
                success: function (data) {
                    if (data.status === 1) {
                        $(".instrument_batch_adjust_status").remove();
                        $("body").append(data.data.file);
                        $(".instrument_batch_adjust_status").modal('show');

                    }
                }
            });
        })

        //批量调整仪器状态时查询改变后状态是否需要审批
        $('body').on('change', '.instrument_batch_adjust_status select', function() {
            var instrumentIds = $(this).closest('select').attr('data-id');
            var instrumentStatus = $(this).val();

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/instrument-status-batch-need-approval',
                data: {
                    instrumentIds: instrumentIds,
                    instrumentStatus: instrumentStatus
                },
                type: 'POST',
                success: function (data) {
                    if (data.status === 1) {
                        $('.instrument_batch_adjust_status_submit').attr('data-id',data.data.needApprovalInstrumentIds)
                        if($('.instrument_batch_adjust_status .instrument_batch_adjust_status_submit').attr('data-id')!=''){
                            $('.instrument_status_change_reason').removeClass('hide');
                        }else{
                            $('.instrument_status_change_reason').addClass('hide');
                        }
                        if(data.data.approvalUser != ''){
                            $('.approval_route').removeClass('hide');
                            $('.approver').html(data.data.approvalUser);
                            $('.approver').attr('data-id',data.data.approvalUserIds);
                        }else{
                            $('.approval_route').addClass('hide');
                        }
                    }
                }
            });
        })

        //批量调整仪器状态提交
        $('body').on('click', '.instrument_batch_adjust_status .instrument_batch_adjust_status_submit', function () {
            var instrumentIds = $('.instrument_batch_adjust_status select').attr('data-id');
            var needApprovalInstrumentIds = $(this).attr('data-id');
            var instrumentStatus = $('.instrument_batch_adjust_status select option:selected').val();
            var statusChangeReason = $('.instrument_status_change_reason textarea').val();
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/instrument-batch-adjust-status-submit',
                data: {
                    instrumentIds: instrumentIds,
                    instrumentStatus: instrumentStatus,
                    needApprovalInstrumentIds: needApprovalInstrumentIds,
                    statusChangeReason: statusChangeReason
                },
                type: 'POST',
                success: function (data) {
                    if (data.status === 1) {
                        $(".instrument_batch_adjust_status").remove();
                        $('body').removeClass('modal-open');
                        console.log(data.data.needApprovalInstruments);
                        if ( needApprovalInstrumentIds != '') {
                            var tips = mainLang('instrument_need_approval');
                            var html = '<div class="ml20 clear"><div class="mb20">' + tips + '</div>';
                            for (var i = 0; i < data.data.needApprovalInstruments.length; i++) {
                                html += '<div style=" max-width: 500px; max-height: 500px; line-height: 30px; overflow-y: auto;">' + data.data.needApprovalInstruments[i] + '</div>';
                            }
                            html += '</div>';
                            $.popContent(html, mainLang('prompt'), function () {
                            }, function () {
                            }, false, null, function () {
                                searchInstrumentManage()
                            });
                        } else {
                            searchInstrumentManage()
                        }
                    }
                }
            });
        })

        /*查看大图*/
        $('body').on('mousemove', '.show-big-img', function (event) {
            var obj = $(this);
            var width = obj.width();
            var height = obj.height();
            if ($('.show-img-box').length == 0) {
                var box = '<div class="show-img-box absolute" style="z-index: 2;"><img style="max-width:500px;max-height:500px;background-color: white;border: 1px solid #dcdcdc" src="' + obj.attr("src") + '" /></div>';
                $('body').append(box);
            }
            var img = $('.show-img-box img');
            var imgH = img.height();
            var imgW = img.width();
            var event = event || window.event;
            var x = event.pageX ? event.pageX : event.clientX - document.body.scrollLeft
            var y = event.pageX ? event.pageY : event.clientY - document.body.scrollTop

            var left = x + 10,
                top = y - imgH - 10;
            if (left < 0) {
                left = 0;
            }
            if (top < 0) {
                top = 0
            }
            $('.show-img-box').css({
                'left': left,
                'top': top,
                'z-index': 100042,

            });
        });
        $('body').on('mouseout', '.show-big-img', function () {
            $('.show-img-box').remove();
        });

    });

    $('body').on('mouseenter', '.instrument_record_table .ico-drag', function () {
        const table = document.querySelector('.exp_conetnt.active .instrument_record_table');
        const dataType = table.getAttribute("data-type");
        instrumentApi.instrumentSaveSortField(dataType);
    });

    $('body').on('mouseenter', '.instruments_manage_page .ico-drag', function () {
        $(document).ready(function () {
            require(['bin/sortable.min'], function (Sortable) {
                    let tableHeader;
                    let table;
                    tableHeader = $('.exp_conetnt.active .instruments_manage_page thead tr');
                    table = $('.exp_conetnt.active .project-view-tbody-table ');
                    new Sortable(tableHeader[0], {
                        ghostClass: 'sortable-ghost',
                        dragClass: 'sortable-drag',
                        animation: 150,
                        throttleTime: 200,
                        handle: '.ico-drag',
                        onEnd: function (event) {
                            var oldIndex = event.oldIndex;
                            var newIndex = event.newIndex;

                            // 判断拖拽的方向和位置，如果需要还原则移回原始位置
                            if (oldIndex > newIndex && newIndex === 0) {
                                event.from.insertBefore(event.item, event.from.children[oldIndex].nextSibling);
                                return;
                            } else if (newIndex === event.from.children.length - 1) {
                                event.from.insertBefore(event.item, event.from.children[oldIndex]);
                                return;
                            }

                            if (oldIndex !== newIndex) {
                                var rows = table[0].tBodies[0].rows;
                                const element = rows[0].querySelector('td.no_instruments_result_td');
                                if (!element) {
                                    for (var i = 0; i < rows.length; i++) {
                                        var cells = rows[i].cells;
                                        var fromCell = cells[oldIndex];
                                        var toCell = cells[newIndex];

                                        if (oldIndex > newIndex) {
                                            rows[i].insertBefore(fromCell, toCell);
                                        } else {
                                            rows[i].insertBefore(fromCell, toCell.nextSibling);
                                        }
                                    }
                                }
                                var fieldConfig = '';
                                tableHeader.find('th').each(function () {
                                    var field = $(this).data('field');
                                    if (field && field != "operation") {
                                        fieldConfig += field + ',';
                                    }
                                });
                                // 在循环结束后，删除最后一个逗号
                                if (fieldConfig !== '') {
                                    fieldConfig = fieldConfig.slice(0, -1);
                                }

                                $.ajaxFn({
                                    url: ELN_URL + '?r=instrument/save-sort-field',
                                    data: {
                                        type: 'instruments',
                                        fieldConfig: fieldConfig,
                                        instrumentId: '',
                                    }
                                });
                            }
                        }
                    });
                }
            )
        })
    });

    // inscada 批量插入
    $('body').on('click', '.batch_add_inscada_data', function (e) {
        var config = $(this).attr('data-type');
        if(config!='row'&&config!='col'){
            return;
        }
        insertInstrumentDataBatch(config);
    });

    // 自动插入
    $('body').on('click', '.auto_insert_inscada', function (e) {
        let start_time = new Date();
        let timeDifference = start_time.getTime();
        const config = $(this).attr('data-type');
        if (config !== 'row' && config !== 'col') {
            return;
        }

        const instrument = getInstrumentInfo();
        if (instrument.instrument_type !== '1') {
            $.showAlert(mainLang('only_num_tip'));
            return;
        }
        const modal = $('.instrument-data-modal:visible');
        const insertConfig = modal.find('.instrument-data-fields div').not('.hidden').find('input:checked');
        const hasDetail = modal.find('.instrument-fields-data.raw_data input')[0].checked;
        // 将弹窗的数据获取到后移除弹窗，把自动插入的小弹框展示
        $(".instrument-data-modal").remove();
        var html = `<div class="auto_insert_inscada_box">
                         <input class="instrument_info hidden">
                         <div class="iblock auto_insert_inscada_icon" id="auto_insert_inscada_icon"></div>
                    </div>`;

        $("body").append(html);
        $.showAlert(mainLang('inscada_auto_insert_tip'));
        const instrumentInfo = $('.instrument_info');

        // 自动插入动画
        var animation = bodymovin.loadAnimation({
            container: document.getElementById('auto_insert_inscada_icon'),
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: '../../animations/auto_Rob.json'
        })

        // 获取当前仪器的数据
        instrumentInfo.val(instrument.name)
            .data('instrument_id', instrument.instrument_id)
            .data('batch_number', instrument.batch_number)
            .data('instrument_type', instrument.instrument_type);
        // 发起自动插入请求
        sendAutoInscadaRequest(timeDifference, config, instrument, modal, hasDetail, insertConfig);
    });

    // 获取仪器信息
    function getInstrumentInfo() {
        const instrument = $('.instrument-data-modal:visible .instrument-select');
        return {
            name: instrument.val(),
            instrument_id: instrument.data('instrument_id'),
            batch_number: instrument.data('batch_number'),
            instrument_type: instrument.data('instrument_type')
        };
    }

    // 发送自动插入请求
    function sendAutoInscadaRequest(timeDifference, config, instrument, modal, hasDetail, insertConfig) {
        console.log('开启定时器' + Date.now());

        function autoInsertInscada() {

            var startTime = timeDifference; // 开始时间
            var endTime = new Date().getTime(); // 结束时间

            // 计算时间差（以毫秒为单位）
            var timeDiff = endTime - startTime;

            // 将时间差转换为分钟
            var timeDiffInMinutes = timeDiff / (1000 * 60);

            if (timeDiffInMinutes <= 10) {
                $.ajaxFn({
                    noLoad: true,
                    type: 'post',
                    url: ELN_URL + '?r=instrument/auto-insert-inscada-data',
                    data: {
                        instrument_id: instrument.instrument_id,
                        batch_number: instrument.batch_number,
                        data_type: instrument.instrument_type,
                        last_fetch_time: timeDifference,
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            if (res.data.new_data_list.length > 0) {
                                handleSuccessResponse(res.data.new_data_list, config, instrument, modal, hasDetail, insertConfig);
                                // 如果传回来有数据并且成功插入数据
                                if (res.data.last_data_timestamp) {
                                    // 将最近一条数据的时间赋给newTimeDifference，下次取数据就取这条数据之后的
                                    timeDifference = res.data.last_data_timestamp;
                                }
                            }
                        } else {
                            console.log('定时器已清除' + Date.now());
                            $(".auto_insert_inscada_box").remove();
                        }

                        if ($(".auto_insert_inscada_box").length > 0) {
                            // 设置下一次的定时器
                            setTimeout(autoInsertInscada, 5000);
                        }

                    },
                });
            } else {
                console.log('定时器已清除' + Date.now());
                $(".auto_insert_inscada_box").remove();
            }
        }

        // 最初调用该函数
        autoInsertInscada();
    }

    // 处理成功的响应数据
    async function handleSuccessResponse(newDataList, config, instrument, modal, hasDetail, insertConfig) {
        // 找不到模块或模块不是intable/文本编辑器
        if (!window.instrumentDataBox ||
            (window.instrumentDataBox.type !== 'intable' &&
             window.instrumentDataBox.type !== 'ueditor')) {
            $.popContent(mainLang('inscada_auto_insert_not_found_box_tip'), mainLang('tip'), undefined, undefined, false);
            return { status: 0};
        }

        const dataIdArray = [];
        const dataInfoArray = [];
        let selectInfo = window.instrumentDataBox;
        const selectBox = selectInfo.box;
        newDataList.forEach(function (element) {
            const dataItem = element;
            const dataId = dataItem.id;

            let insertValue = getInsertValue(dataItem, modal, hasDetail, instrument, insertConfig);

            const dataInfo = {
                value: insertValue,
                data_id: dataId,
                instrument_type: instrument.instrument_type,
                batch_number: instrument.batch_number,
            };
            dataInfoArray.push(dataInfo);
        });

        switch (selectInfo.type) {
            case 'intable':
                const data = {
                    'config': config,
                    'dataInfo': dataInfoArray
                };
                dataInfoArray.forEach(dataInfo => dataIdArray.push(dataInfo.data_id));
                if (selectBox.contentWindow.xs) {
                    selectBox.contentWindow.xs.sheet.setInstrumentDataBatch(data);
                }
                else {
                    selectBox.contentWindow.setInstrumentDataBatch(data);
                }
                break;
            case 'ueditor':
                handleUEditorData(dataInfoArray, selectBox, hasDetail, dataIdArray, config);
                break;
        }

        await saveDetail(dataIdArray, instrument);
    }

    // 获取插入的值
    function getInsertValue(dataItem, modal, hasDetail, instrument, insertConfig) {
        let insertValue = '';
        let basicValue = '';
        insertConfig.each(function () {
            const field = $(this).val();
            if (field !== 'numerical_value' && field !== 'unit') {
                insertValue += hasDetail ? '\n' : ' ';
            }
            if (field === 'batch_number') {
                insertValue += instrument.batch_number;
            } else if (field === 'instrument_name') {
                insertValue += instrument.name;
            } else if (field === 'raw_data') {
                insertValue += dataItem.raw_data;
            } else if(field === 'numerical_value'){
                basicValue += dataItem.numerical_value;
            } else if (field == 'unit') {
                basicValue += dataItem.unit;
            }
        });
        insertValue = basicValue + (insertValue == '' ? '' : `(${insertValue})`);
        if (window.instrumentDataBox.type === 'intable' && hasDetail) {
            insertValue = insertValue.split('\n').reduce((str, val) => `${str}<div>${val}</div>`, '');
        }
        return insertValue;
    }

    // 处理文本编辑器批量插入数据
    function handleUEditorData(dataInfoArray, selectBox, hasDetail, dataIdArray = [], config) {
        const editorKey = selectBox.attr('id');
        const editor = UE.getEditor(editorKey);
        for (let index = 0; index < dataInfoArray.length; index++) {
            const dataInfo = dataInfoArray[index];
            // 获取当前选区（光标位置）的 Range 对象
            const range = editor.selection.getRange();
            // 获取当前选区的元素
            let ancestorNode = range.getCommonAncestor();
            if (ancestorNode.nodeType === Node.TEXT_NODE) {
                ancestorNode = ancestorNode.parentNode;
            }
            let dataHtml;
            if (UE.dom.domUtils.findParentByTagName(ancestorNode, ["table"])) {
                // 如果父级能找到table,就是文本编辑器表格内批量插入，否则就是非表格
                let insertValue = hasDetail ? dataInfo.value.split('\n') : [dataInfo.value];
                dataHtml = `<p><a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a></p>`

                UE.getEditor(editorKey).execCommand('insertHtml', dataHtml);
                dataIdArray.push(dataInfo.data_id);
                // 移动光标到插入后的位置
                if (ancestorNode) {
                    // 通过 document 对象查找包含插入内容的 td或th 父节点
                    const parentTd = UE.dom.domUtils.findParentByTagName(ancestorNode, ["td", "th"], true);

                    if (config === 'row') {
                        // 插入到下一个 td
                        const parentTr = parentTd.parentNode;
                        const nextTd = parentTr.querySelector('td:nth-child(' + (parentTd.cellIndex + 2) + ')');
                        if (nextTd) {
                            range.setStart(nextTd, 0);
                            range.collapse(true);
                        }else{
                            // 如果没有下一个格子就退出循环
                            $.popContent(mainLang('inscada_auto_insert_row_tip'), mainLang('tip'), undefined, undefined, false);
                            break;
                        }
                    } else if (config === 'col') {
                        // 插入到下一个 tr 的同列 td
                        const parentTr = parentTd.parentNode;
                        const nextTr = parentTr.nextElementSibling;
                        if (nextTr) {
                            const sameColTd = nextTr.querySelector('td:nth-child(' + (parentTd.cellIndex + 1) + ')');
                            if (sameColTd) {
                                range.setStart(sameColTd, 0);
                                range.collapse(true);
                            }
                        }else{
                            // 如果没有下一个格子就退出循环
                            $.popContent(mainLang('inscada_auto_insert_col_tip'), mainLang('tip'), undefined, undefined, false);
                            break;
                        }
                    }
                    range.setStartAfter(ancestorNode).setCursor(true);
                }
            } else {
                let insertValue = hasDetail ? dataInfo.value.split('\n') : [dataInfo.value];
                // bug 6499 Plug：在文本编辑器中插入仪器数据，数据前会有多个空格，需要手动删除
                let addNbspIfMultiple = index === 0 ? '' : `&nbsp;&nbsp;&nbsp;&nbsp;`;
                if (config === 'row') {
                    dataHtml = `${addNbspIfMultiple}<a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a>`
                    UE.getEditor(editorKey).execCommand('insertHtml', dataHtml);
                } else if (config === 'col') {
                    dataHtml = `<p><a type="_link" target="_link" title="${dataInfo.value}" _href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}"
                    href="${dataInfo.batch_number}|_|${dataInfo.data_id}|_|${dataInfo.instrument_type}" class="remark editor_instrument_data" cate="5_0" textvalue="">${dataInfo.value.replaceAll(' ', '&nbsp')}</a></p>`
                    UE.getEditor(editorKey).execCommand('insertHtml', dataHtml);
                    let newAncestorNode = range.getCommonAncestor();
                    const parentP = UE.dom.domUtils.findParentByTagName(newAncestorNode, ["p"], true);
                    $(parentP).after('<p><br></p>');
                }
                dataIdArray.push(dataInfo.data_id);
            }
        }
    }

    // 保存详细信息
    function saveDetail(dataIdArray, instrument) {
        $.ajax({
            url: ELN_URL + '?r=instrument/update-inscada',
            type: 'post',
            data: {
                type: 'instant_receive',
                inscadaId: dataIdArray,
                dataType: instrument.instrument_type,
                exp_pages: $('.exp_title .tag.on .name').text(),
                remark: '',
            },
            success: function() {
                $.showAlert(mainLang('insert_data_success'));
            }
        });
    }

    // 批量插入，自动插入切换横向，纵向插入数据
    $('body').on('click', '.insert_data_direction', function (e) {
        var config = $(this).attr('data-type');
        $('.instrument-data-modal .batch_add_inscada_data').attr('data-type',config);
        $('.instrument-data-modal .auto_insert_inscada').attr('data-type',config);
        $('.instrument-data-modal .inscada_receive_btn').attr('data-insert-type',config);
        $(this).find('.select-inscada-direction').addClass('select_inscada_direction');
        $(this).siblings().find('.select-inscada-direction').removeClass('select_inscada_direction');
    });

    // 鼠标移入自动插入弹框
    $('body').on('mouseenter', '.auto_insert_inscada_box', function (e) {
        var html = `<div class="iblock auto_insert_inscada_tip">
                    <span style="padding-left: 8px">` + mainLang('auto_entering') + `</span>
                    <span class="auto_insert_inscada_close" title="` + mainLang('close_automatic') + `">×</span>
                    </div>`;
        $(this).width('184px');
        $(".auto_insert_inscada_box").append(html);
    });

    // 鼠标移出自动插入弹框
    $('body').on('mouseleave', '.auto_insert_inscada_box', function (e) {
        $(this).width('60px');
        $(".auto_insert_inscada_box .auto_insert_inscada_tip").remove();
    });

    // 关闭自动插入弹框
    $('body').on('click', '.auto_insert_inscada_close', function (e) {
        const instrumentInfoBox = $('.instrument_info');
        // 获取自动插入的仪器数据
        var instrumentInfo = {
            batch_number: instrumentInfoBox.data('batch_number'),
            instrument_type: instrumentInfoBox.data('instrument_type')
        };
        $(".auto_insert_inscada_box").remove();
        // 加载弹窗
        window.loadInstrumentPopup(instrumentInfo);
    });

    // 点击备注事件
    $('body').on('click', 'td[data-field="remark"]', function (e) {
        const $parentElement = $(this);
        $(".inscada_remark_input", $parentElement).show().focus();
        $(".inscada_remark", $parentElement).hide();
    });

    // 快速修改备注
    $('body').on('keydown blur', '.inscada_remark_input', function (e) {
        const $parentElement = $(this).parent();
        var oldValue = $parentElement.find('.inscada_remark').text().trim();
        var newValue = $(this).val().trim();
        var shouldSave = false;
        const parentTr = $(this).closest('tr');

        if (e.type === 'keydown') {
            switch (e.keyCode) {
                case 38: // 上键
                    if (oldValue != newValue) {
                        shouldSave = true;
                    }
                    $parentElement.find('.inscada_remark_input').hide();
                    $parentElement.find('.inscada_remark').setTextAndTitle(newValue).show();
                    const previousTr = $(parentTr[0].previousElementSibling);
                    previousTr.find('.inscada_remark_input').show().focus();
                    previousTr.find('.inscada_remark').hide();
                    break;
                case 13: // 回车键
                case 40: // 下键
                    if (oldValue != newValue) {
                        shouldSave = true;
                    }
                    $parentElement.find('.inscada_remark_input').hide();
                    $parentElement.find('.inscada_remark').setTextAndTitle(newValue).show();
                    const nextTr = $(parentTr[0].nextElementSibling);
                    nextTr.find('.inscada_remark_input').show().focus();
                    nextTr.find('.inscada_remark').hide();
                    break;
                default:
                    // 其他键
                    break;
            }
        } else if (e.type === 'focusout') {
            if (oldValue != newValue) {
                shouldSave = true;
            }
            $parentElement.find('.inscada_remark_input').hide();
            $parentElement.find('.inscada_remark').setTextAndTitle(newValue).show();
        }

        if (shouldSave) {
            var data_id = parentTr.attr('data-id');
            var data_type = parentTr.attr('data-data-type');
            $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/save-inscada-remark-data',
                noLoad: true,
                data: {
                    data_id,
                    newValue,
                    data_type
                }
            }, false, true, true);
        }
    });

    // 选择插入数据方向弹窗显示
    $('body').on('mouseover', '.select-insert-direction, .batch-add-inscada-data-fields', function(e) {
        $('.instrument_insert_model .batch-add-inscada-data-fields').show();
    });

    // 选择插入数据方向弹窗隐藏
    $('body').on('mouseleave', '.select-insert-direction, .batch-add-inscada-data-fields', function(e) {
        $('.instrument_insert_model .batch-add-inscada-data-fields').hide();
    });

    // 选择插入数据方向弹窗隐藏
    $('body').on('click', '.exp_conetnt.active .filter_unclaimed', function (e) {
        var filter_unclaimed_is_checked = $(this).prop('checked') ? 1 : 0;
        localStorage.setItem('filter_unclaimed_is_checked', filter_unclaimed_is_checked);
    });

    var instrumentApi = {

        // add by hkk 2020/6/28 打开仪器库痕迹
        openInstrumentTrace: function (page=1,needUpdateAllPage="yes") {
            var that = this;
            var data = {
                instrumentId: instrumentApi.insId || $('.exp_conetnt.active .search-instrument-trace').attr('data-id'),
                needUpdateAllPage:needUpdateAllPage,
                page: page || 1,
                limit:$('.exp_conetnt.active .pager-select:visible').val() || that.default_page_size || undefined,
                action_details:$('.exp_conetnt.active .instrument_trace_page .operate_detail').val(), // 详情搜索
                action:$('.exp_conetnt.active .instrument_trace_page #instrument_trace_type').val(), // 类别筛选
                create_by:$('.exp_conetnt.active .instrument_trace_page input[name=instrument_trace_user]').attr('idbox'), // 创建人筛选
                start_time:$('.exp_conetnt.active .instrument_trace_page #start_time').val(),
                end_time:$('.exp_conetnt.active .instrument_trace_page #end_time').val(),

            };

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-trace-page',
                data:data,
                success: function (data) {
                    if (data.status === 1) {
                        if(needUpdateAllPage === 'yes'){

                            $('.layout_right_box .exp_conetnt').html(data.data.file);

                            // 标签添加class
                            $('.exp_title a.on').removeClass('on');
                            var findTab = $('.my_exp_detial.instrument_trace[data-ins="' + instrumentApi.insId + '"]');
                            if (findTab.length !== 0) {
                                findTab.addClass('on');
                            } else {
                                var tabName = mainLang('instrument_trace') ;
                                var newTab = '<a class="iblock on my_exp_detial instrument_trace" data-ins="' + instrumentApi.insId + '" title="' + tabName + '">' + tabName + '<span class="close"></span></a>';
                                $('.exp_title').append(newTab);
                            }
                            handleExpTitle();
                            if (!that.default_page_size) {
                                that.default_page_size = $(".page_box").attr('data-limit');
                            }

                            /*初始化日历*/
                            var dateOpts = {
                                format: 'yyyy-mm-dd',
                                autoclose: true,
                                minView: 2,
                                clearBtn: true,
                            };
                            $.fn.datetimepicker ? $('.instrument_trace_page .datetimepicker').datetimepicker(dateOpts).on('click', function () {
                                if ($('.instrument_trace_page [name="end_time"]').val() != '') {
                                    var startTimer = $('.instrument_trace_page [name="end_time"]').val() + ' 01:00';
                                    $('.instrument_trace_page [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                }

                                if ($('.instrument_trace_page [name="start_time"]').val() != '') {
                                    var endTimer = $('.instrument_trace_page [name="start_time"]').val() + ' 01:00';
                                    $('.instrument_trace_page [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                }
                            }) : '';
                        }else{
                            $('.exp_conetnt.active .instruments_trace_table').html(data.data.file);

                        }

                        that.instrumentTracePageFn();  // 调用分页插件
                    }
                }
            });

        },

        // add by hkk 2020/6/28 仪器库痕迹分页
        instrumentTracePageFn:function () {
            var that = this;
            var pageBox = $('.exp_conetnt.active .instrument_trace_page .page_box');
            var page = that.pageTrace || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page-1,
                callback: function (page_index, jq) {
                    that.pageTrace = page_index + 1;
                    that.openInstrumentTrace((page_index + 1), "no")
                },
                items_per_page:  pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        },

        // add by hkk 2020/6/28
        instrumentsManage:function (type) {

           if(type ==='instruments_manage'){
               require('get_html').genInstrumentListPage();
           }else{ // 我的仪器库
               require('get_html').genMyInstrumentListPage();
           }

        },

        // add by hkk 2020/6/28 我的仪器库第一个子标签，不改标签改内容
        myInstrumentsTab:function (type) {
            
            var tagId = $('.exp_title .tag.on').attr('data-id');
            if(type === 1 ){ // 我的仪器库-查看界面
                require('get_html').getMyInstrumentListContent().then(function (res) {
                    require('tab').switchTag(tagId,res.data.contentHtml);  // 不换tag换内容
                });

            }else{ // 我的仪器库- 我的预约界面
                this.getMyInstrumentBookContent().then(function (res) {
                    require('tab').switchTag(tagId,res.data.contentHtml);  // 不换tag换内容
                    mountVueApp('/vue-ui/dist/instrumentsBookMine.js', '#instrument-book-mine', {
                        bookList: res.data.data,
                        closeBookInstruments: function() {
                            // 使用unmountDynamicApp销毁应用
                            unmountVueApp('#instrument-book-mine');
                            $('#instrument-book-mine').hide();
                        }
                    });
                    $('#instrument-book-mine').show();
                });

            }
        },

        // 切换 inscada仪器对接汇总标签页
        inscadaSummaryTab: function (type, dataType, numericalInstrumentType) {
            var tagId = $('.exp_title .tag.on').attr('data-id');
            require('get_html').getInscadaSummaryContent(type, dataType, numericalInstrumentType).then(function (res) {
                require('tab').switchTag(tagId, res.data.contentHtml);
            });
        },

        // add by hkk 2020/6/28 获取我的仪器库内容-预约内容
        getMyInstrumentBookContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/my-book',
                data: {
                    needUpdateAllPage: 1,
                    type: 'my_instruments',
                }
            });
        },

        // 仪器库管理 我的仪器库 我的仪器库预约分页
        instrumentsManagePageFn: function(type) {
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;

            var per_page;
            if(type === 'my_instruments' && $('.exp_conetnt.active .top-group-tabs .on').attr('data-id') === "2"  ){
                per_page = that.bookLimit;
            }else if(type === 'my_instruments'){
                per_page = that.myLimit;
            }else{
                per_page = that.manageLimit;
            }
            var localLimitKey = type === 'my_instruments' ? 'getMyInstrumentListContent' : 'getInstrumentListContent'

            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                items_per_page: pageBox.attr('data-limit') || getPageLimit(localLimitKey) || 15,
                callback: function(page_index, jq) {

                    var url,tableSelector,needOrder;

                    if (type === 'my_instruments' && $('.exp_conetnt.active .top-group-tabs .on').attr('data-id') === "2") {// 我的仪器预约界面
                        url = ELN_URL + '?r=instrument/my-book';
                        tableSelector = '.exp_conetnt.active .instruments_book_table';
                        that.bookLimit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                    } else {
                        url = ELN_URL + '?r=instrument/manage';
                        tableSelector = '.exp_conetnt.active .instruments_table';
                        if (type === 'my_instruments') {
                            that.myLimit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                        } else {
                            that.manageLimit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                        }
                        needOrder = true;
                    }


                    //获取查询条件和页数
                    var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage"));
                    data['page'] = page_index + 1;
                    data['limit']= $('.exp_conetnt.active .pager-select:visible').val() || getPageLimit(localLimitKey) || this.default_page_size;
                    data['needUpdateAllPage'] = 0;
                    data['type'] = type; // add by hkk 2019/11/1

                    var box = $(".exp_conetnt.active #search-instrument-manage");
                    data['instrument_create_user'] = box.find('input[name=instrument_create_user]').attr('idbox');
                    data['instrument_maintenance_user'] = box.find('input[name=instrument_maintenance_user]').attr('idbox');
                    data['instrument_in_charge_user'] = box.find('input[name=instrument_in_charge_user]').attr('idbox');
                    data['instrument_responsible_user'] = box.find('input[name=instrument_responsible_user]').attr('idbox');
                    data['instrument_belong_group'] = box.find('input[name=group_ids]').attr('idbox');
                    data['instrument_belong_department'] = box.find('input.dept_select').attr('data');

                    // add by hkk 2022/5/26 传递排序参数
                    if (needOrder) {
                        data['order_field'] = $(this).parents('th').attr('data-field');
                        if ($(this).parents('th').hasClass('basic_define_field')) {
                            data['order_field_type'] = 'define';
                        } else if ($(this).parents('th').hasClass('manage_field')) {
                            data['order_field_type'] = 'manage';
                        } else {
                            data['order_field_type'] = 'basic';
                        }
                        var orderObj = $('.exp_conetnt.active .instruments_table th.ascending,.exp_conetnt.active .instruments_table th.descending');
                        if (orderObj && orderObj.length > 0) {
                            data['order_field'] = orderObj.attr('data-field');
                            if (orderObj.hasClass('ascending')) {
                                data['order_type'] = 'asc'
                            } else {
                                data['order_type'] = 'desc'
                            }
                            if (orderObj.hasClass('basic_define_field')) {
                                data['order_field_type'] = 'define';
                            } else if (orderObj.hasClass('manage_field')) {
                                data['order_field_type'] = 'manage';
                            } else {
                                data['order_field_type'] = 'basic';
                            }
                        }
                    }
                    var pageLimit = data.limit;


                    $.ajaxFn({
                        url: url,
                        data: data,
                        type: 'POST',
                        success: function (data) {
                            setLimitToLocal(localLimitKey, pageLimit);
                            if (1 == data.status) {
                                $(tableSelector).html(data.data.file);
                                that.pageData = page_index + 1;
                                that.instrumentsManagePageFn(type);
                            }
                        }
                    });
                },
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        // inscada 功能分页
        inscadaManagePageFn: function(pageType) {
            var that = this;
            var isPopup = pageType == 'popup'; // 弹窗内表格判断
            var pageBox = isPopup ? $('.instrument-data-modal .inscada_data_page_box') : $('.exp_conetnt.active .inscada_table .inscada_data_page_box');
            var page = that.pageData || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: isPopup ? [5, 10, 15, 20] : [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: isPopup ? ' ' : mainLang('prev_page'),
                next_text: isPopup ? ' ' : mainLang('next_page'),
                first_text: isPopup ? ' ' : mainLang('first page'),
                last_text: isPopup ? ' ' : mainLang('last page'),
                num_edge_entries: isPopup ? 1 : 2,
                num_display_entries: isPopup ? 0 : 4,
                ellipse_text: isPopup ? '' : '...',
                current_page: page - 1,
                items_per_page: pageBox.attr('data-limit') || 15,
                current_page_progress: isPopup ? '<span style="color:#1388ff;padding: 0;">' + (page == 0 ? 1 : page) + '</span> /' + Math.ceil(pageBox.attr('data-num') / pageBox.attr('data-limit') || 1) + '页' : '',
                callback: function(page_index, jq) {

                    var url = ELN_URL + '?r=instrument/get-inscada-page';
                    var needOrder = false;
                    // 获取查询条件和页数信息
                    var box = $(".search-inscada-box:visible");
                    if (box.length === 0 && 'popup' === pageType) {//bug#31546,获取弹框中的box
                        box = $('.modal .search-inscada-box');
                    }
                    // var box = $('.instrument-select-item.selected');
                    // var box = $('.modal-body>.inscada_data_page_box').length? $('.instrument-select-item.selected') : $(".search-inscada-box:visible") ;
                    var data = $.formSerializeFn(box);

                    data['page'] = page_index + 1;
                    data['limit']= $('.pager-select:visible').val() || this.default_page_size || undefined;

                    data['dataType'] = box.attr('data-type') ? box.attr('data-type') : $(".project-view-tbody tr").attr('data-data-type');
                    data['instrumentId'] = box.attr('data-id') ? box.attr('data-id') : $('.instrument-select-wrap .instrument-select').attr('data-id');
                    // data['dataType'] = box.attr('data-type')
                    // data['instrumentId'] = box.attr('data-id')
                    data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
                    data['needUpdateAllPage'] = '0';
                    data['source'] = pageType;

                    $.ajaxFn({
                        url: url,
                        data: data,
                        type: 'POST',
                        success: function (res) {
                            if (1 == res.status) {
                                if(pageType==='popup'){
                                    const modal = $('.instrument-data-modal:visible');
                                    modal.find('.instrument-data-detail').html(res.data.file);
                                }
                                else {
                                    $('.inscada_table').html(res.data.file);
                                    console.log(res.data);
                                    that.pageData = page_index + 1;
                                }
                            }
                        }
                    });
                },
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        // inscada汇总页面分页功能
        inscadaSummaryPageFn: function() {
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;

            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                items_per_page: pageBox.attr('data-limit') || 15,
                callback: function(page_index, jq) {

                    var url = ELN_URL + '?r=instrument/get-inscada-summary-page';
                    var tableSelector = '.exp_conetnt.active .inscada_table';
                    var needOrder = false;
                    // 获取查询条件和页数信息
                    var box = $(".exp_conetnt.active .search-inscada-box");
                    var data = $.formSerializeFn(box);

                    data['page'] = page_index + 1;
                    data['limit']= $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
                    data['dataType'] = box.attr('data-type');
                    data['needUpdateAllPage'] = '0';
                    data['filterUnclaimed'] = $('.exp_conetnt.active .instrument_receive').prop('checked') ? 1 : 0;
                    data['numerical_instrument_type'] = $(".exp_conetnt.active .top-group-tabs li.on").attr('data-numerical-type') || 0;

                    $.ajaxFn({
                        url: url,
                        data: data,
                        type: 'POST',
                        success: function (data) {
                            if (1 == data.status) {
                                $(tableSelector).html(data.data.file);
                                that.pageData = page_index + 1;
                                that.inscadaSummaryPageFn();
                            }
                        }
                    });
                },
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        //inscada痕迹分页
        inscadaHistoryPageFn: function() {
            var that = this;
            var pageBox = $('.inscada_operate_page .page_box');
            var page = that.pageData || 1;

            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                items_per_page: pageBox.attr('data-limit') || 15,
                callback: function(page_index, jq) {
                    var box = $(".exp_conetnt.active .search-inscada-box");
                    var url = ELN_URL + '?r=instrument/get-inscada-operate-page';
                    var needOrder = false;
                    const limit= $('.inscada_operate_page .pager-select:visible').val() || this.default_page_size || undefined;

                    var inscadaId = []
                    inscadaId.push(pageBox.attr('data-inscada-id'))

                    $.ajaxFn({
                        url: url,
                        data: {
                            type:'history',
                            limit : limit,
                            page:page_index + 1,
                            inscadaId:inscadaId,
                            dataType: box.attr('data-type')
                        },
                        type: 'POST',
                        success: function (res) {
                            if (1 == res.status) {
                                $('.inscada_operate_page .modal-dialog').html(res.data.file);
                                that.pageData = page_index + 1;
                                that.inscadaHistoryPageFn();
                            }
                        }
                    });
                },
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        // add by hkk 2020/7/19 打开仪器维修记录
        openInstrumentRepairRecord: function (page=1,needUpdateAllPage="yes") {
            var that = this;
            var data = {
                instrumentId: instrumentApi.insId || $('.exp_conetnt.active .addRepairRecord').attr('data-id'),
                needUpdateAllPage:needUpdateAllPage,
                page: page || 1,
                limit:$('.exp_conetnt.active .pager-select:visible').val() || that.default_page_size || undefined,

            };

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-repair-record-page',
                data:data,
                success: function (data) {
                    if (data.status === 1) {
                        $('.exp_conetnt.active .instruments_repair_record_table').html(data.data.file);
                        that.instrumentRepairRecordPageFn();  // 调用分页插件
                    }
                }
            });

        },

        // add by hkk 2020/7/19 仪器维修记录分页
        instrumentRepairRecordPageFn:function () {
            var that = this;
            var pageBox = $('.exp_conetnt.active .instrument_repair_record_page .page_box');
            var page = that.pageRecord || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page-1,
                callback: function (page_index, jq) {
                    that.pageRecord = page_index + 1;

                    that.openInstrumentRepairRecord((page_index + 1), "no")
                },
                items_per_page:  pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        },

        // add by hkk 2020/7/22 打开仪器运行页面
        openInstrumentOperateRecord: function (page=1,needUpdateAllPage="yes") {
            var that = this;
            var experimentId = $('#exp_id').val() ?? null;
            var data = {
                instrumentId: instrumentApi.insId || $('.exp_conetnt.active .beginOrEndOperateRecord').attr('data-id'),
                needUpdateAllPage:needUpdateAllPage,
                page: page || 1,
                limit:$('.exp_conetnt.active .pager-select:visible').val() || that.default_page_size || undefined,
                expId: experimentId
            };

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-operate-record-page',
                data:data,
                success: function (data) {
                    if (data.status === 1) {
                        $('.exp_conetnt.active .instruments_operate_record_table').html(data.data.file);
                        that.instrumentOperateRecordPageFn();  // 调用分页插件
                    }
                }
            });

        },

        // add by hkk 2020/7/22 仪器运行页面分页
        instrumentOperateRecordPageFn:function () {
            var that = this;
            var pageBox = $('.exp_conetnt.active .instrument_operate_record_page .page_box');
            var page = that.pageOperateRecord || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page-1,
                callback: function (page_index, jq) {
                    that.pageOperateRecord = page_index + 1;

                    that.openInstrumentOperateRecord((page_index + 1), "no")
                },
                items_per_page:  pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        },

        // add by hkk 2020/7/23 打开仪器校验记录页面
        openInstrumentCheckRecord: function (page=1,needUpdateAllPage="yes") {
            var that = this;
            var data = {
                instrumentId: instrumentApi.insId || $('.exp_conetnt.active .addCheckRecord').attr('data-id'),
                needUpdateAllPage:needUpdateAllPage,
                page: page || 1,
                limit:$('.exp_conetnt.active .pager-select:visible').val() || that.default_page_size || undefined,

            };

            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-check-record-page',
                data:data,
                success: function (data) {
                    if (data.status === 1) {
                        $('.exp_conetnt.active .instruments_check_record_table').html(data.data.file);
                        that.instrumentCheckRecordPageFn();  // 调用分页插件
                    }
                }
            });

        },

        // add by hkk 2020/7/23 仪器校验记录页面分页
        instrumentCheckRecordPageFn:function () {
            var that = this;
            var pageBox = $('.exp_conetnt.active .instrument_check_record_page .page_box');
            var page = that.pageCheckRecord || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page-1,
                callback: function (page_index, jq) {
                    that.pageCheckRecord = page_index + 1;

                    that.openInstrumentCheckRecord((page_index + 1), "no")
                },
                items_per_page:  pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        },



        instrumentSaveSortField: function (dataType) {
            $(document).ready(function () {
                require(['bin/sortable.min'], function (Sortable) {
                    let tableHeader;
                    let table;
                    let type;
                    switch (dataType) {
                        case '3':
                            type= 'getInstrumentOperateRecordContent';
                            tableHeader = $('.exp_conetnt.active .instrument_operate_record_page table thead tr');
                            table = $('.exp_conetnt.active .instrument_operate_record_page table');
                            break;
                        case '1':
                            type = 'getInstrumentRepairRecordContent';
                            tableHeader = $('.exp_conetnt.active .instrument_repair_record_page table thead tr');
                            table = $('.exp_conetnt.active .instrument_repair_record_page table');
                            break;
                        case '2':
                            type = 'getInstrumentCheckRecordContent';
                            tableHeader = $('.exp_conetnt.active .instrument_check_record_page table thead tr');
                            table = $('.exp_conetnt.active .instrument_check_record_page table');
                            break;
                    }

                    new Sortable(tableHeader[0], {
                        ghostClass: 'sortable-ghost',
                        dragClass: 'sortable-drag',
                        animation: 150,
                        throttleTime: 200,
                        handle: '.ico-drag',
                        onEnd: function (event) {
                            var oldIndex = event.oldIndex;
                            var newIndex = event.newIndex;

                            // 判断拖拽的方向和位置，如果需要还原则移回原始位置
                            if (oldIndex > newIndex && (newIndex === 0||newIndex === 1)) {
                                event.from.insertBefore(event.item, event.from.children[oldIndex].nextSibling);
                                return;
                            } else if (newIndex === event.from.children.length - 1) {
                                event.from.insertBefore(event.item, event.from.children[oldIndex]);
                                return;
                            }

                            if (oldIndex !== newIndex) {
                                var rows = table[0].tBodies[0].rows;

                                for (var i = 0; i < rows.length; i++) {
                                    var cells = rows[i].cells;
                                    var fromCell = cells[oldIndex];
                                    var toCell = cells[newIndex];

                                    if (oldIndex > newIndex) {
                                        rows[i].insertBefore(fromCell, toCell);
                                    } else {
                                        rows[i].insertBefore(fromCell, toCell.nextSibling);
                                    }
                                }

                                var fieldArray = [];
                                tableHeader.find('th').each(function () {
                                    var field = $(this).data('field');
                                    if (field) {
                                        fieldArray.push(field);
                                    }
                                });


                                $.ajaxFn({
                                    url: ELN_URL + '?r=instrument/save-sort-field',
                                    data: {
                                        type: type,
                                        fieldConfig: fieldArray,
                                        instrumentId: table.data('id'),
                                    }
                                });
                            }

                        }
                    });
                });
            });
        },

        // 签字原因字数计算
        updateCharacterCount: function () {
            var textarea = $('textarea[name="instrument_sign_remark"]');
            var limit = 255;
            var charLimitMessage = $(".charLimitContainer .charLimitMessage");


            if (textarea.val().length >= limit) {
                textarea.val(textarea.val().substring(0, limit));
                charLimitMessage.text("255/255");
                charLimitMessage.css("color", "red");
            } else {
                charLimitMessage.text(textarea.val().length + "/255");
                charLimitMessage.css("color", "#999");
            }
        }
    };

    return instrumentApi;

})

/**
 * 领取scada数据返回的数据格式
 * @typedef IReceiveScadaFileResp
 * @property {string} expId
 * @property {number} receiveState
 * @property {IReceiveScadaFileAddFilesRes} addFilesRes
 */
/**
 * 实验领取scada文件的结果
 * @typedef IReceiveScadaFileAddFilesRes
 * @property {string} expId
 * @property {number} receiveState
 * @property {number} status
 * @property {IReceiveScadaAddedFile[]} fileList
 */
/**
 * 领取scada文件到实验的返回的已添加的文件
 * @typedef IReceiveScadaAddedFile
 * @property {number} upload_id - 实验领取scada文件后, 文件保存到upload表的id
 * @property {string} id - scada上传的文件在instrument_data_file中的id
 * @property {string} batch_number - 仪器id
 * @property {string} filename - scada上传的文件名
 * @property {string} save_name - instrument_data_file表保存的文件名
 * @property {string} file_host - instrument_data_file表保存的文件所在的文件服务器地址
 * @property {string} filepath - instrument_data_file保存的文件在文件服务器中保存的路径
 * @property {string} remark - instrument_data_file保存的scada上传文件备注
 * @property {string} exp_pages - 领取scada文件的实验的页码
 * @property {string} operate_users - 领取scada文件的操作人
 * @property {string} dep_path - 领取文件后, 文件在upload表保存的文件路径
 * @property {string} eln_save_name - 领取文件后, 文件在upload表保存的文件名
 */

/**
 * 插入仪器数据的数据详情
 * @typedef InsertInstrumentDataDetail
 * @property {string} [batch_number] 仪器id
 * @property {string} [instrument_name] 仪器名称
 * @property {string} [raw_data] 仪器原始数据
 * @property {string} [numerical_value] 仪器解析后的数据
 * @property {string} [unit] 数据单位
 * @property {string} [filename] 插入文件时的文件名
 */