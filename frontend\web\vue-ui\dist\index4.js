import { ax as Wt, ay as Ye, c as et, az as Rl, e as Zr, aA as Jr, aB as ea, aC as Tl, aD as kl, aE as Al, aF as Ml, aG as Sn, i as ut, aH as It, aI as ta, aJ as en, d as Pe, aK as xn, aL as Mt, aM as Ll, b as na, aN as tt, S as Bn, aO as Ol, aP as Nl, aQ as tn, aR as la, aS as ra, aT as aa, aU as oa, Q as qe, ah as sa, a2 as Dt, u as Lt, L as En, j as Fl, k as $l, R as Wn, $ as Vt, m as ae, B as ct, _ as Ke, z as Rn, A as zt, g as me, T as De, h as Oe, H as Pl, U as Kt, a7 as ke, n as Hl, G as nn, f as J, aV as Ue, F as dt, aW as _l, aX as ia, aY as Bl, aZ as ft, p as Te, D as Wl, t as ua, y as Il, M as Ot, J as ca, P as Dl, a1 as Vl, a9 as rt, af as ht, ae as Nt, ao as In, r as Dn } from "./index3.js";
import { watch as de, unref as A, computed as O, inject as se, ref as R, useSlots as Tn, Text as da, defineComponent as ne, createBlock as we, openBlock as B, resolveDynamicComponent as at, mergeProps as fa, withCtx as Re, createElementBlock as q, createCommentVNode as xe, Fragment as vt, renderSlot as ge, normalizeClass as _, provide as kn, reactive as zl, toRef as Vn, getCurrentInstance as ie, nextTick as ze, toRaw as zn, createElementVNode as ce, withDirectives as Ve, withModifiers as Ft, isRef as pt, vModelCheckbox as $t, createTextVNode as jt, toDisplayString as We, normalizeStyle as Xe, toRefs as Kl, createVNode as Me, render as Kn, isVNode as ha, resolveComponent as Ee, resolveDirective as jl, renderList as jn, onBeforeMount as Gl, onMounted as gt, onUpdated as va, onUnmounted as An, h as W, watchEffect as ot, vShow as Gn, onBeforeUnmount as Ul, Comment as pa } from "vue";
import { k as ql, r as ga, t as ma, f as Yl } from "./index2.js";
var ln = Wt(Ye, "WeakMap"), Un = Object.create, ba = /* @__PURE__ */ function() {
  function e() {
  }
  return function(t) {
    if (!et(t))
      return {};
    if (Un)
      return Un(t);
    e.prototype = t;
    var n = new e();
    return e.prototype = void 0, n;
  };
}();
function ya(e, t) {
  var n = -1, l = e.length;
  for (t || (t = Array(l)); ++n < l; )
    t[n] = e[n];
  return t;
}
function Ca(e, t, n, l) {
  var a = !n;
  n || (n = {});
  for (var r = -1, s = t.length; ++r < s; ) {
    var o = t[r], i = void 0;
    i === void 0 && (i = e[o]), a ? Rl(n, o, i) : Zr(n, o, i);
  }
  return n;
}
function wa(e, t) {
  return Jr(ea(e, t, Tl), e + "");
}
function nt(e) {
  return e != null && kl(e.length) && !Al(e);
}
function Sa(e, t, n) {
  if (!et(n))
    return !1;
  var l = typeof t;
  return (l == "number" ? nt(n) && Ml(t, n.length) : l == "string" && t in n) ? Sn(n[t], e) : !1;
}
function xa(e) {
  return wa(function(t, n) {
    var l = -1, a = n.length, r = a > 1 ? n[a - 1] : void 0, s = a > 2 ? n[2] : void 0;
    for (r = e.length > 3 && typeof r == "function" ? (a--, r) : void 0, s && Sa(n[0], n[1], s) && (r = a < 3 ? void 0 : r, a = 1), t = Object(t); ++l < a; ) {
      var o = n[l];
      o && e(t, o, l, r);
    }
    return t;
  });
}
var Ea = Object.prototype;
function Mn(e) {
  var t = e && e.constructor, n = typeof t == "function" && t.prototype || Ea;
  return e === n;
}
function Ra(e, t) {
  for (var n = -1, l = Array(e); ++n < e; )
    l[n] = t(n);
  return l;
}
function Ta() {
  return !1;
}
var Xl = typeof exports == "object" && exports && !exports.nodeType && exports, qn = Xl && typeof module == "object" && module && !module.nodeType && module, ka = qn && qn.exports === Xl, Yn = ka ? Ye.Buffer : void 0, Aa = Yn ? Yn.isBuffer : void 0, Pt = Aa || Ta, Ma = "[object Arguments]", La = "[object Array]", Oa = "[object Boolean]", Na = "[object Date]", Fa = "[object Error]", $a = "[object Function]", Pa = "[object Map]", Ha = "[object Number]", _a = "[object Object]", Ba = "[object RegExp]", Wa = "[object Set]", Ia = "[object String]", Da = "[object WeakMap]", Va = "[object ArrayBuffer]", za = "[object DataView]", Ka = "[object Float32Array]", ja = "[object Float64Array]", Ga = "[object Int8Array]", Ua = "[object Int16Array]", qa = "[object Int32Array]", Ya = "[object Uint8Array]", Xa = "[object Uint8ClampedArray]", Qa = "[object Uint16Array]", Za = "[object Uint32Array]", j = {};
j[Ka] = j[ja] = j[Ga] = j[Ua] = j[qa] = j[Ya] = j[Xa] = j[Qa] = j[Za] = !0;
j[Ma] = j[La] = j[Va] = j[Oa] = j[za] = j[Na] = j[Fa] = j[$a] = j[Pa] = j[Ha] = j[_a] = j[Ba] = j[Wa] = j[Ia] = j[Da] = !1;
function Ja(e) {
  return ut(e) && kl(e.length) && !!j[It(e)];
}
function eo(e) {
  return function(t) {
    return e(t);
  };
}
var Ql = typeof exports == "object" && exports && !exports.nodeType && exports, st = Ql && typeof module == "object" && module && !module.nodeType && module, to = st && st.exports === Ql, Ut = to && ta.process, Xn = function() {
  try {
    var e = st && st.require && st.require("util").types;
    return e || Ut && Ut.binding && Ut.binding("util");
  } catch {
  }
}(), Qn = Xn && Xn.isTypedArray, Ln = Qn ? eo(Qn) : Ja, no = Object.prototype, lo = no.hasOwnProperty;
function Zl(e, t) {
  var n = Pe(e), l = !n && en(e), a = !n && !l && Pt(e), r = !n && !l && !a && Ln(e), s = n || l || a || r, o = s ? Ra(e.length, String) : [], i = o.length;
  for (var u in e)
    (t || lo.call(e, u)) && !(s && // Safari 9 has enumerable `arguments.length` in strict mode.
    (u == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
    a && (u == "offset" || u == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
    r && (u == "buffer" || u == "byteLength" || u == "byteOffset") || // Skip index properties.
    Ml(u, i))) && o.push(u);
  return o;
}
function Jl(e, t) {
  return function(n) {
    return e(t(n));
  };
}
var ro = Jl(Object.keys, Object), ao = Object.prototype, oo = ao.hasOwnProperty;
function so(e) {
  if (!Mn(e))
    return ro(e);
  var t = [];
  for (var n in Object(e))
    oo.call(e, n) && n != "constructor" && t.push(n);
  return t;
}
function On(e) {
  return nt(e) ? Zl(e) : so(e);
}
function io(e) {
  var t = [];
  if (e != null)
    for (var n in Object(e))
      t.push(n);
  return t;
}
var uo = Object.prototype, co = uo.hasOwnProperty;
function fo(e) {
  if (!et(e))
    return io(e);
  var t = Mn(e), n = [];
  for (var l in e)
    l == "constructor" && (t || !co.call(e, l)) || n.push(l);
  return n;
}
function er(e) {
  return nt(e) ? Zl(e, !0) : fo(e);
}
var tr = Jl(Object.getPrototypeOf, Object), ho = "[object Object]", vo = Function.prototype, po = Object.prototype, nr = vo.toString, go = po.hasOwnProperty, mo = nr.call(Object);
function bo(e) {
  if (!ut(e) || It(e) != ho)
    return !1;
  var t = tr(e);
  if (t === null)
    return !0;
  var n = go.call(t, "constructor") && t.constructor;
  return typeof n == "function" && n instanceof n && nr.call(n) == mo;
}
function Iu() {
  if (!arguments.length)
    return [];
  var e = arguments[0];
  return Pe(e) ? e : [e];
}
function yo() {
  this.__data__ = new xn(), this.size = 0;
}
function Co(e) {
  var t = this.__data__, n = t.delete(e);
  return this.size = t.size, n;
}
function wo(e) {
  return this.__data__.get(e);
}
function So(e) {
  return this.__data__.has(e);
}
var xo = 200;
function Eo(e, t) {
  var n = this.__data__;
  if (n instanceof xn) {
    var l = n.__data__;
    if (!Mt || l.length < xo - 1)
      return l.push([e, t]), this.size = ++n.size, this;
    n = this.__data__ = new Ll(l);
  }
  return n.set(e, t), this.size = n.size, this;
}
function Ne(e) {
  var t = this.__data__ = new xn(e);
  this.size = t.size;
}
Ne.prototype.clear = yo;
Ne.prototype.delete = Co;
Ne.prototype.get = wo;
Ne.prototype.has = So;
Ne.prototype.set = Eo;
var lr = typeof exports == "object" && exports && !exports.nodeType && exports, Zn = lr && typeof module == "object" && module && !module.nodeType && module, Ro = Zn && Zn.exports === lr, Jn = Ro ? Ye.Buffer : void 0, el = Jn ? Jn.allocUnsafe : void 0;
function To(e, t) {
  if (t)
    return e.slice();
  var n = e.length, l = el ? el(n) : new e.constructor(n);
  return e.copy(l), l;
}
function ko(e, t) {
  for (var n = -1, l = e == null ? 0 : e.length, a = 0, r = []; ++n < l; ) {
    var s = e[n];
    t(s, n, e) && (r[a++] = s);
  }
  return r;
}
function Ao() {
  return [];
}
var Mo = Object.prototype, Lo = Mo.propertyIsEnumerable, tl = Object.getOwnPropertySymbols, Oo = tl ? function(e) {
  return e == null ? [] : (e = Object(e), ko(tl(e), function(t) {
    return Lo.call(e, t);
  }));
} : Ao;
function No(e, t, n) {
  var l = t(e);
  return Pe(e) ? l : na(l, n(e));
}
function nl(e) {
  return No(e, On, Oo);
}
var rn = Wt(Ye, "DataView"), an = Wt(Ye, "Promise"), on = Wt(Ye, "Set"), ll = "[object Map]", Fo = "[object Object]", rl = "[object Promise]", al = "[object Set]", ol = "[object WeakMap]", sl = "[object DataView]", $o = tt(rn), Po = tt(Mt), Ho = tt(an), _o = tt(on), Bo = tt(ln), Be = It;
(rn && Be(new rn(new ArrayBuffer(1))) != sl || Mt && Be(new Mt()) != ll || an && Be(an.resolve()) != rl || on && Be(new on()) != al || ln && Be(new ln()) != ol) && (Be = function(e) {
  var t = It(e), n = t == Fo ? e.constructor : void 0, l = n ? tt(n) : "";
  if (l)
    switch (l) {
      case $o:
        return sl;
      case Po:
        return ll;
      case Ho:
        return rl;
      case _o:
        return al;
      case Bo:
        return ol;
    }
  return t;
});
var Ht = Ye.Uint8Array;
function Wo(e) {
  var t = new e.constructor(e.byteLength);
  return new Ht(t).set(new Ht(e)), t;
}
function Io(e, t) {
  var n = t ? Wo(e.buffer) : e.buffer;
  return new e.constructor(n, e.byteOffset, e.length);
}
function Do(e) {
  return typeof e.constructor == "function" && !Mn(e) ? ba(tr(e)) : {};
}
var Vo = "__lodash_hash_undefined__";
function zo(e) {
  return this.__data__.set(e, Vo), this;
}
function Ko(e) {
  return this.__data__.has(e);
}
function _t(e) {
  var t = -1, n = e == null ? 0 : e.length;
  for (this.__data__ = new Ll(); ++t < n; )
    this.add(e[t]);
}
_t.prototype.add = _t.prototype.push = zo;
_t.prototype.has = Ko;
function jo(e, t) {
  for (var n = -1, l = e == null ? 0 : e.length; ++n < l; )
    if (t(e[n], n, e))
      return !0;
  return !1;
}
function Go(e, t) {
  return e.has(t);
}
var Uo = 1, qo = 2;
function rr(e, t, n, l, a, r) {
  var s = n & Uo, o = e.length, i = t.length;
  if (o != i && !(s && i > o))
    return !1;
  var u = r.get(e), c = r.get(t);
  if (u && c)
    return u == t && c == e;
  var h = -1, v = !0, b = n & qo ? new _t() : void 0;
  for (r.set(e, t), r.set(t, e); ++h < o; ) {
    var d = e[h], p = t[h];
    if (l)
      var g = s ? l(p, d, h, t, e, r) : l(d, p, h, e, t, r);
    if (g !== void 0) {
      if (g)
        continue;
      v = !1;
      break;
    }
    if (b) {
      if (!jo(t, function(C, T) {
        if (!Go(b, T) && (d === C || a(d, C, n, l, r)))
          return b.push(T);
      })) {
        v = !1;
        break;
      }
    } else if (!(d === p || a(d, p, n, l, r))) {
      v = !1;
      break;
    }
  }
  return r.delete(e), r.delete(t), v;
}
function Yo(e) {
  var t = -1, n = Array(e.size);
  return e.forEach(function(l, a) {
    n[++t] = [a, l];
  }), n;
}
function Xo(e) {
  var t = -1, n = Array(e.size);
  return e.forEach(function(l) {
    n[++t] = l;
  }), n;
}
var Qo = 1, Zo = 2, Jo = "[object Boolean]", es = "[object Date]", ts = "[object Error]", ns = "[object Map]", ls = "[object Number]", rs = "[object RegExp]", as = "[object Set]", os = "[object String]", ss = "[object Symbol]", is = "[object ArrayBuffer]", us = "[object DataView]", il = Bn ? Bn.prototype : void 0, qt = il ? il.valueOf : void 0;
function cs(e, t, n, l, a, r, s) {
  switch (n) {
    case us:
      if (e.byteLength != t.byteLength || e.byteOffset != t.byteOffset)
        return !1;
      e = e.buffer, t = t.buffer;
    case is:
      return !(e.byteLength != t.byteLength || !r(new Ht(e), new Ht(t)));
    case Jo:
    case es:
    case ls:
      return Sn(+e, +t);
    case ts:
      return e.name == t.name && e.message == t.message;
    case rs:
    case os:
      return e == t + "";
    case ns:
      var o = Yo;
    case as:
      var i = l & Qo;
      if (o || (o = Xo), e.size != t.size && !i)
        return !1;
      var u = s.get(e);
      if (u)
        return u == t;
      l |= Zo, s.set(e, t);
      var c = rr(o(e), o(t), l, a, r, s);
      return s.delete(e), c;
    case ss:
      if (qt)
        return qt.call(e) == qt.call(t);
  }
  return !1;
}
var ds = 1, fs = Object.prototype, hs = fs.hasOwnProperty;
function vs(e, t, n, l, a, r) {
  var s = n & ds, o = nl(e), i = o.length, u = nl(t), c = u.length;
  if (i != c && !s)
    return !1;
  for (var h = i; h--; ) {
    var v = o[h];
    if (!(s ? v in t : hs.call(t, v)))
      return !1;
  }
  var b = r.get(e), d = r.get(t);
  if (b && d)
    return b == t && d == e;
  var p = !0;
  r.set(e, t), r.set(t, e);
  for (var g = s; ++h < i; ) {
    v = o[h];
    var C = e[v], T = t[v];
    if (l)
      var S = s ? l(T, C, v, t, e, r) : l(C, T, v, e, t, r);
    if (!(S === void 0 ? C === T || a(C, T, n, l, r) : S)) {
      p = !1;
      break;
    }
    g || (g = v == "constructor");
  }
  if (p && !g) {
    var f = e.constructor, m = t.constructor;
    f != m && "constructor" in e && "constructor" in t && !(typeof f == "function" && f instanceof f && typeof m == "function" && m instanceof m) && (p = !1);
  }
  return r.delete(e), r.delete(t), p;
}
var ps = 1, ul = "[object Arguments]", cl = "[object Array]", wt = "[object Object]", gs = Object.prototype, dl = gs.hasOwnProperty;
function ms(e, t, n, l, a, r) {
  var s = Pe(e), o = Pe(t), i = s ? cl : Be(e), u = o ? cl : Be(t);
  i = i == ul ? wt : i, u = u == ul ? wt : u;
  var c = i == wt, h = u == wt, v = i == u;
  if (v && Pt(e)) {
    if (!Pt(t))
      return !1;
    s = !0, c = !1;
  }
  if (v && !c)
    return r || (r = new Ne()), s || Ln(e) ? rr(e, t, n, l, a, r) : cs(e, t, i, n, l, a, r);
  if (!(n & ps)) {
    var b = c && dl.call(e, "__wrapped__"), d = h && dl.call(t, "__wrapped__");
    if (b || d) {
      var p = b ? e.value() : e, g = d ? t.value() : t;
      return r || (r = new Ne()), a(p, g, n, l, r);
    }
  }
  return v ? (r || (r = new Ne()), vs(e, t, n, l, a, r)) : !1;
}
function Gt(e, t, n, l, a) {
  return e === t ? !0 : e == null || t == null || !ut(e) && !ut(t) ? e !== e && t !== t : ms(e, t, n, l, Gt, a);
}
var bs = 1, ys = 2;
function Cs(e, t, n, l) {
  var a = n.length, r = a;
  if (e == null)
    return !r;
  for (e = Object(e); a--; ) {
    var s = n[a];
    if (s[2] ? s[1] !== e[s[0]] : !(s[0] in e))
      return !1;
  }
  for (; ++a < r; ) {
    s = n[a];
    var o = s[0], i = e[o], u = s[1];
    if (s[2]) {
      if (i === void 0 && !(o in e))
        return !1;
    } else {
      var c = new Ne(), h;
      if (!(h === void 0 ? Gt(u, i, bs | ys, l, c) : h))
        return !1;
    }
  }
  return !0;
}
function ar(e) {
  return e === e && !et(e);
}
function ws(e) {
  for (var t = On(e), n = t.length; n--; ) {
    var l = t[n], a = e[l];
    t[n] = [l, a, ar(a)];
  }
  return t;
}
function or(e, t) {
  return function(n) {
    return n == null ? !1 : n[e] === t && (t !== void 0 || e in Object(n));
  };
}
function Ss(e) {
  var t = ws(e);
  return t.length == 1 && t[0][2] ? or(t[0][0], t[0][1]) : function(n) {
    return n === e || Cs(n, e, t);
  };
}
var xs = 1, Es = 2;
function Rs(e, t) {
  return Ol(e) && ar(t) ? or(Nl(e), t) : function(n) {
    var l = tn(n, e);
    return l === void 0 && l === t ? la(n, e) : Gt(t, l, xs | Es);
  };
}
function Ts(e) {
  return function(t) {
    return t == null ? void 0 : t[e];
  };
}
function ks(e) {
  return function(t) {
    return ra(t, e);
  };
}
function As(e) {
  return Ol(e) ? Ts(Nl(e)) : ks(e);
}
function Ms(e) {
  return typeof e == "function" ? e : e == null ? Tl : typeof e == "object" ? Pe(e) ? Rs(e[0], e[1]) : Ss(e) : As(e);
}
function Ls(e) {
  return function(t, n, l) {
    for (var a = -1, r = Object(t), s = l(t), o = s.length; o--; ) {
      var i = s[++a];
      if (n(r[i], i, r) === !1)
        break;
    }
    return t;
  };
}
var sr = Ls();
function Os(e, t) {
  return e && sr(e, t, On);
}
function Ns(e, t) {
  return function(n, l) {
    if (n == null)
      return n;
    if (!nt(n))
      return e(n, l);
    for (var a = n.length, r = -1, s = Object(n); ++r < a && l(s[r], r, s) !== !1; )
      ;
    return n;
  };
}
var Fs = Ns(Os);
function sn(e, t, n) {
  (n !== void 0 && !Sn(e[t], n) || n === void 0 && !(t in e)) && Rl(e, t, n);
}
function $s(e) {
  return ut(e) && nt(e);
}
function un(e, t) {
  if (!(t === "constructor" && typeof e[t] == "function") && t != "__proto__")
    return e[t];
}
function Ps(e) {
  return Ca(e, er(e));
}
function Hs(e, t, n, l, a, r, s) {
  var o = un(e, n), i = un(t, n), u = s.get(i);
  if (u) {
    sn(e, n, u);
    return;
  }
  var c = r ? r(o, i, n + "", e, t, s) : void 0, h = c === void 0;
  if (h) {
    var v = Pe(i), b = !v && Pt(i), d = !v && !b && Ln(i);
    c = i, v || b || d ? Pe(o) ? c = o : $s(o) ? c = ya(o) : b ? (h = !1, c = To(i, !0)) : d ? (h = !1, c = Io(i, !0)) : c = [] : bo(i) || en(i) ? (c = o, en(o) ? c = Ps(o) : (!et(o) || Al(o)) && (c = Do(i))) : h = !1;
  }
  h && (s.set(i, c), a(c, i, l, r, s), s.delete(i)), sn(e, n, c);
}
function ir(e, t, n, l, a) {
  e !== t && sr(t, function(r, s) {
    if (a || (a = new Ne()), et(r))
      Hs(e, t, s, n, ir, l, a);
    else {
      var o = l ? l(un(e, s), r, s + "", e, t, a) : void 0;
      o === void 0 && (o = r), sn(e, s, o);
    }
  }, er);
}
function _s(e, t) {
  var n = -1, l = nt(e) ? Array(e.length) : [];
  return Fs(e, function(a, r, s) {
    l[++n] = t(a, r, s);
  }), l;
}
function Bs(e, t) {
  var n = Pe(e) ? aa : _s;
  return n(e, Ms(t));
}
function Ws(e, t) {
  return oa(Bs(e, t));
}
function Is(e, t) {
  return Gt(e, t);
}
function mt(e) {
  return e === null;
}
var ur = xa(function(e, t, n) {
  ir(e, t, n);
});
const Ds = (e) => qe ? window.requestAnimationFrame(e) : setTimeout(e, 16), cr = Symbol("buttonGroupContextKey"), Et = ({ from: e, replacement: t, scope: n, version: l, ref: a, type: r = "API" }, s) => {
  de(() => A(s), (o) => {
  }, {
    immediate: !0
  });
}, Vs = (e, t) => {
  Et({
    from: "type.text",
    replacement: "link",
    version: "3.0.0",
    scope: "props",
    ref: "https://element-plus.org/en-US/component/button.html#button-attributes"
  }, O(() => e.type === "text"));
  const n = se(cr, void 0), l = sa("button"), { form: a } = Dt(), r = Lt(O(() => n == null ? void 0 : n.size)), s = En(), o = R(), i = Tn(), u = O(() => e.type || (n == null ? void 0 : n.type) || ""), c = O(() => {
    var d, p, g;
    return (g = (p = e.autoInsertSpace) != null ? p : (d = l.value) == null ? void 0 : d.autoInsertSpace) != null ? g : !1;
  }), h = O(() => e.tag === "button" ? {
    ariaDisabled: s.value || e.loading,
    disabled: s.value || e.loading,
    autofocus: e.autofocus,
    type: e.nativeType
  } : {}), v = O(() => {
    var d;
    const p = (d = i.default) == null ? void 0 : d.call(i);
    if (c.value && (p == null ? void 0 : p.length) === 1) {
      const g = p[0];
      if ((g == null ? void 0 : g.type) === da) {
        const C = g.children;
        return new RegExp("^\\p{Unified_Ideograph}{2}$", "u").test(C.trim());
      }
    }
    return !1;
  });
  return {
    _disabled: s,
    _size: r,
    _type: u,
    _ref: o,
    _props: h,
    shouldAddSpace: v,
    handleClick: (d) => {
      if (s.value || e.loading) {
        d.stopPropagation();
        return;
      }
      e.nativeType === "reset" && (a == null || a.resetFields()), t("click", d);
    }
  };
}, zs = [
  "default",
  "primary",
  "success",
  "warning",
  "info",
  "danger",
  "text",
  ""
], Ks = ["button", "submit", "reset"], cn = Fl({
  size: Vt,
  disabled: Boolean,
  type: {
    type: String,
    values: zs,
    default: ""
  },
  icon: {
    type: Wn
  },
  nativeType: {
    type: String,
    values: Ks,
    default: "button"
  },
  loading: Boolean,
  loadingIcon: {
    type: Wn,
    default: () => ql
  },
  plain: Boolean,
  text: Boolean,
  link: Boolean,
  bg: Boolean,
  autofocus: Boolean,
  round: Boolean,
  circle: Boolean,
  color: String,
  dark: Boolean,
  autoInsertSpace: {
    type: Boolean,
    default: void 0
  },
  tag: {
    type: $l([String, Object]),
    default: "button"
  }
}), js = {
  click: (e) => e instanceof MouseEvent
};
function re(e, t) {
  Gs(e) && (e = "100%");
  var n = Us(e);
  return e = t === 360 ? e : Math.min(t, Math.max(0, parseFloat(e))), n && (e = parseInt(String(e * t), 10) / 100), Math.abs(e - t) < 1e-6 ? 1 : (t === 360 ? e = (e < 0 ? e % t + t : e % t) / parseFloat(String(t)) : e = e % t / parseFloat(String(t)), e);
}
function St(e) {
  return Math.min(1, Math.max(0, e));
}
function Gs(e) {
  return typeof e == "string" && e.indexOf(".") !== -1 && parseFloat(e) === 1;
}
function Us(e) {
  return typeof e == "string" && e.indexOf("%") !== -1;
}
function dr(e) {
  return e = parseFloat(e), (isNaN(e) || e < 0 || e > 1) && (e = 1), e;
}
function xt(e) {
  return e <= 1 ? "".concat(Number(e) * 100, "%") : e;
}
function Ge(e) {
  return e.length === 1 ? "0" + e : String(e);
}
function qs(e, t, n) {
  return {
    r: re(e, 255) * 255,
    g: re(t, 255) * 255,
    b: re(n, 255) * 255
  };
}
function fl(e, t, n) {
  e = re(e, 255), t = re(t, 255), n = re(n, 255);
  var l = Math.max(e, t, n), a = Math.min(e, t, n), r = 0, s = 0, o = (l + a) / 2;
  if (l === a)
    s = 0, r = 0;
  else {
    var i = l - a;
    switch (s = o > 0.5 ? i / (2 - l - a) : i / (l + a), l) {
      case e:
        r = (t - n) / i + (t < n ? 6 : 0);
        break;
      case t:
        r = (n - e) / i + 2;
        break;
      case n:
        r = (e - t) / i + 4;
        break;
    }
    r /= 6;
  }
  return { h: r, s, l: o };
}
function Yt(e, t, n) {
  return n < 0 && (n += 1), n > 1 && (n -= 1), n < 1 / 6 ? e + (t - e) * (6 * n) : n < 1 / 2 ? t : n < 2 / 3 ? e + (t - e) * (2 / 3 - n) * 6 : e;
}
function Ys(e, t, n) {
  var l, a, r;
  if (e = re(e, 360), t = re(t, 100), n = re(n, 100), t === 0)
    a = n, r = n, l = n;
  else {
    var s = n < 0.5 ? n * (1 + t) : n + t - n * t, o = 2 * n - s;
    l = Yt(o, s, e + 1 / 3), a = Yt(o, s, e), r = Yt(o, s, e - 1 / 3);
  }
  return { r: l * 255, g: a * 255, b: r * 255 };
}
function hl(e, t, n) {
  e = re(e, 255), t = re(t, 255), n = re(n, 255);
  var l = Math.max(e, t, n), a = Math.min(e, t, n), r = 0, s = l, o = l - a, i = l === 0 ? 0 : o / l;
  if (l === a)
    r = 0;
  else {
    switch (l) {
      case e:
        r = (t - n) / o + (t < n ? 6 : 0);
        break;
      case t:
        r = (n - e) / o + 2;
        break;
      case n:
        r = (e - t) / o + 4;
        break;
    }
    r /= 6;
  }
  return { h: r, s: i, v: s };
}
function Xs(e, t, n) {
  e = re(e, 360) * 6, t = re(t, 100), n = re(n, 100);
  var l = Math.floor(e), a = e - l, r = n * (1 - t), s = n * (1 - a * t), o = n * (1 - (1 - a) * t), i = l % 6, u = [n, s, r, r, o, n][i], c = [o, n, n, s, r, r][i], h = [r, r, o, n, n, s][i];
  return { r: u * 255, g: c * 255, b: h * 255 };
}
function vl(e, t, n, l) {
  var a = [
    Ge(Math.round(e).toString(16)),
    Ge(Math.round(t).toString(16)),
    Ge(Math.round(n).toString(16))
  ];
  return l && a[0].startsWith(a[0].charAt(1)) && a[1].startsWith(a[1].charAt(1)) && a[2].startsWith(a[2].charAt(1)) ? a[0].charAt(0) + a[1].charAt(0) + a[2].charAt(0) : a.join("");
}
function Qs(e, t, n, l, a) {
  var r = [
    Ge(Math.round(e).toString(16)),
    Ge(Math.round(t).toString(16)),
    Ge(Math.round(n).toString(16)),
    Ge(Zs(l))
  ];
  return a && r[0].startsWith(r[0].charAt(1)) && r[1].startsWith(r[1].charAt(1)) && r[2].startsWith(r[2].charAt(1)) && r[3].startsWith(r[3].charAt(1)) ? r[0].charAt(0) + r[1].charAt(0) + r[2].charAt(0) + r[3].charAt(0) : r.join("");
}
function Zs(e) {
  return Math.round(parseFloat(e) * 255).toString(16);
}
function pl(e) {
  return Ce(e) / 255;
}
function Ce(e) {
  return parseInt(e, 16);
}
function Js(e) {
  return {
    r: e >> 16,
    g: (e & 65280) >> 8,
    b: e & 255
  };
}
var dn = {
  aliceblue: "#f0f8ff",
  antiquewhite: "#faebd7",
  aqua: "#00ffff",
  aquamarine: "#7fffd4",
  azure: "#f0ffff",
  beige: "#f5f5dc",
  bisque: "#ffe4c4",
  black: "#000000",
  blanchedalmond: "#ffebcd",
  blue: "#0000ff",
  blueviolet: "#8a2be2",
  brown: "#a52a2a",
  burlywood: "#deb887",
  cadetblue: "#5f9ea0",
  chartreuse: "#7fff00",
  chocolate: "#d2691e",
  coral: "#ff7f50",
  cornflowerblue: "#6495ed",
  cornsilk: "#fff8dc",
  crimson: "#dc143c",
  cyan: "#00ffff",
  darkblue: "#00008b",
  darkcyan: "#008b8b",
  darkgoldenrod: "#b8860b",
  darkgray: "#a9a9a9",
  darkgreen: "#006400",
  darkgrey: "#a9a9a9",
  darkkhaki: "#bdb76b",
  darkmagenta: "#8b008b",
  darkolivegreen: "#556b2f",
  darkorange: "#ff8c00",
  darkorchid: "#9932cc",
  darkred: "#8b0000",
  darksalmon: "#e9967a",
  darkseagreen: "#8fbc8f",
  darkslateblue: "#483d8b",
  darkslategray: "#2f4f4f",
  darkslategrey: "#2f4f4f",
  darkturquoise: "#00ced1",
  darkviolet: "#9400d3",
  deeppink: "#ff1493",
  deepskyblue: "#00bfff",
  dimgray: "#696969",
  dimgrey: "#696969",
  dodgerblue: "#1e90ff",
  firebrick: "#b22222",
  floralwhite: "#fffaf0",
  forestgreen: "#228b22",
  fuchsia: "#ff00ff",
  gainsboro: "#dcdcdc",
  ghostwhite: "#f8f8ff",
  goldenrod: "#daa520",
  gold: "#ffd700",
  gray: "#808080",
  green: "#008000",
  greenyellow: "#adff2f",
  grey: "#808080",
  honeydew: "#f0fff0",
  hotpink: "#ff69b4",
  indianred: "#cd5c5c",
  indigo: "#4b0082",
  ivory: "#fffff0",
  khaki: "#f0e68c",
  lavenderblush: "#fff0f5",
  lavender: "#e6e6fa",
  lawngreen: "#7cfc00",
  lemonchiffon: "#fffacd",
  lightblue: "#add8e6",
  lightcoral: "#f08080",
  lightcyan: "#e0ffff",
  lightgoldenrodyellow: "#fafad2",
  lightgray: "#d3d3d3",
  lightgreen: "#90ee90",
  lightgrey: "#d3d3d3",
  lightpink: "#ffb6c1",
  lightsalmon: "#ffa07a",
  lightseagreen: "#20b2aa",
  lightskyblue: "#87cefa",
  lightslategray: "#778899",
  lightslategrey: "#778899",
  lightsteelblue: "#b0c4de",
  lightyellow: "#ffffe0",
  lime: "#00ff00",
  limegreen: "#32cd32",
  linen: "#faf0e6",
  magenta: "#ff00ff",
  maroon: "#800000",
  mediumaquamarine: "#66cdaa",
  mediumblue: "#0000cd",
  mediumorchid: "#ba55d3",
  mediumpurple: "#9370db",
  mediumseagreen: "#3cb371",
  mediumslateblue: "#7b68ee",
  mediumspringgreen: "#00fa9a",
  mediumturquoise: "#48d1cc",
  mediumvioletred: "#c71585",
  midnightblue: "#191970",
  mintcream: "#f5fffa",
  mistyrose: "#ffe4e1",
  moccasin: "#ffe4b5",
  navajowhite: "#ffdead",
  navy: "#000080",
  oldlace: "#fdf5e6",
  olive: "#808000",
  olivedrab: "#6b8e23",
  orange: "#ffa500",
  orangered: "#ff4500",
  orchid: "#da70d6",
  palegoldenrod: "#eee8aa",
  palegreen: "#98fb98",
  paleturquoise: "#afeeee",
  palevioletred: "#db7093",
  papayawhip: "#ffefd5",
  peachpuff: "#ffdab9",
  peru: "#cd853f",
  pink: "#ffc0cb",
  plum: "#dda0dd",
  powderblue: "#b0e0e6",
  purple: "#800080",
  rebeccapurple: "#663399",
  red: "#ff0000",
  rosybrown: "#bc8f8f",
  royalblue: "#4169e1",
  saddlebrown: "#8b4513",
  salmon: "#fa8072",
  sandybrown: "#f4a460",
  seagreen: "#2e8b57",
  seashell: "#fff5ee",
  sienna: "#a0522d",
  silver: "#c0c0c0",
  skyblue: "#87ceeb",
  slateblue: "#6a5acd",
  slategray: "#708090",
  slategrey: "#708090",
  snow: "#fffafa",
  springgreen: "#00ff7f",
  steelblue: "#4682b4",
  tan: "#d2b48c",
  teal: "#008080",
  thistle: "#d8bfd8",
  tomato: "#ff6347",
  turquoise: "#40e0d0",
  violet: "#ee82ee",
  wheat: "#f5deb3",
  white: "#ffffff",
  whitesmoke: "#f5f5f5",
  yellow: "#ffff00",
  yellowgreen: "#9acd32"
};
function ei(e) {
  var t = { r: 0, g: 0, b: 0 }, n = 1, l = null, a = null, r = null, s = !1, o = !1;
  return typeof e == "string" && (e = li(e)), typeof e == "object" && ($e(e.r) && $e(e.g) && $e(e.b) ? (t = qs(e.r, e.g, e.b), s = !0, o = String(e.r).substr(-1) === "%" ? "prgb" : "rgb") : $e(e.h) && $e(e.s) && $e(e.v) ? (l = xt(e.s), a = xt(e.v), t = Xs(e.h, l, a), s = !0, o = "hsv") : $e(e.h) && $e(e.s) && $e(e.l) && (l = xt(e.s), r = xt(e.l), t = Ys(e.h, l, r), s = !0, o = "hsl"), Object.prototype.hasOwnProperty.call(e, "a") && (n = e.a)), n = dr(n), {
    ok: s,
    format: e.format || o,
    r: Math.min(255, Math.max(t.r, 0)),
    g: Math.min(255, Math.max(t.g, 0)),
    b: Math.min(255, Math.max(t.b, 0)),
    a: n
  };
}
var ti = "[-\\+]?\\d+%?", ni = "[-\\+]?\\d*\\.\\d+%?", Ie = "(?:".concat(ni, ")|(?:").concat(ti, ")"), Xt = "[\\s|\\(]+(".concat(Ie, ")[,|\\s]+(").concat(Ie, ")[,|\\s]+(").concat(Ie, ")\\s*\\)?"), Qt = "[\\s|\\(]+(".concat(Ie, ")[,|\\s]+(").concat(Ie, ")[,|\\s]+(").concat(Ie, ")[,|\\s]+(").concat(Ie, ")\\s*\\)?"), Ae = {
  CSS_UNIT: new RegExp(Ie),
  rgb: new RegExp("rgb" + Xt),
  rgba: new RegExp("rgba" + Qt),
  hsl: new RegExp("hsl" + Xt),
  hsla: new RegExp("hsla" + Qt),
  hsv: new RegExp("hsv" + Xt),
  hsva: new RegExp("hsva" + Qt),
  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
};
function li(e) {
  if (e = e.trim().toLowerCase(), e.length === 0)
    return !1;
  var t = !1;
  if (dn[e])
    e = dn[e], t = !0;
  else if (e === "transparent")
    return { r: 0, g: 0, b: 0, a: 0, format: "name" };
  var n = Ae.rgb.exec(e);
  return n ? { r: n[1], g: n[2], b: n[3] } : (n = Ae.rgba.exec(e), n ? { r: n[1], g: n[2], b: n[3], a: n[4] } : (n = Ae.hsl.exec(e), n ? { h: n[1], s: n[2], l: n[3] } : (n = Ae.hsla.exec(e), n ? { h: n[1], s: n[2], l: n[3], a: n[4] } : (n = Ae.hsv.exec(e), n ? { h: n[1], s: n[2], v: n[3] } : (n = Ae.hsva.exec(e), n ? { h: n[1], s: n[2], v: n[3], a: n[4] } : (n = Ae.hex8.exec(e), n ? {
    r: Ce(n[1]),
    g: Ce(n[2]),
    b: Ce(n[3]),
    a: pl(n[4]),
    format: t ? "name" : "hex8"
  } : (n = Ae.hex6.exec(e), n ? {
    r: Ce(n[1]),
    g: Ce(n[2]),
    b: Ce(n[3]),
    format: t ? "name" : "hex"
  } : (n = Ae.hex4.exec(e), n ? {
    r: Ce(n[1] + n[1]),
    g: Ce(n[2] + n[2]),
    b: Ce(n[3] + n[3]),
    a: pl(n[4] + n[4]),
    format: t ? "name" : "hex8"
  } : (n = Ae.hex3.exec(e), n ? {
    r: Ce(n[1] + n[1]),
    g: Ce(n[2] + n[2]),
    b: Ce(n[3] + n[3]),
    format: t ? "name" : "hex"
  } : !1)))))))));
}
function $e(e) {
  return !!Ae.CSS_UNIT.exec(String(e));
}
var ri = (
  /** @class */
  function() {
    function e(t, n) {
      t === void 0 && (t = ""), n === void 0 && (n = {});
      var l;
      if (t instanceof e)
        return t;
      typeof t == "number" && (t = Js(t)), this.originalInput = t;
      var a = ei(t);
      this.originalInput = t, this.r = a.r, this.g = a.g, this.b = a.b, this.a = a.a, this.roundA = Math.round(100 * this.a) / 100, this.format = (l = n.format) !== null && l !== void 0 ? l : a.format, this.gradientType = n.gradientType, this.r < 1 && (this.r = Math.round(this.r)), this.g < 1 && (this.g = Math.round(this.g)), this.b < 1 && (this.b = Math.round(this.b)), this.isValid = a.ok;
    }
    return e.prototype.isDark = function() {
      return this.getBrightness() < 128;
    }, e.prototype.isLight = function() {
      return !this.isDark();
    }, e.prototype.getBrightness = function() {
      var t = this.toRgb();
      return (t.r * 299 + t.g * 587 + t.b * 114) / 1e3;
    }, e.prototype.getLuminance = function() {
      var t = this.toRgb(), n, l, a, r = t.r / 255, s = t.g / 255, o = t.b / 255;
      return r <= 0.03928 ? n = r / 12.92 : n = Math.pow((r + 0.055) / 1.055, 2.4), s <= 0.03928 ? l = s / 12.92 : l = Math.pow((s + 0.055) / 1.055, 2.4), o <= 0.03928 ? a = o / 12.92 : a = Math.pow((o + 0.055) / 1.055, 2.4), 0.2126 * n + 0.7152 * l + 0.0722 * a;
    }, e.prototype.getAlpha = function() {
      return this.a;
    }, e.prototype.setAlpha = function(t) {
      return this.a = dr(t), this.roundA = Math.round(100 * this.a) / 100, this;
    }, e.prototype.isMonochrome = function() {
      var t = this.toHsl().s;
      return t === 0;
    }, e.prototype.toHsv = function() {
      var t = hl(this.r, this.g, this.b);
      return { h: t.h * 360, s: t.s, v: t.v, a: this.a };
    }, e.prototype.toHsvString = function() {
      var t = hl(this.r, this.g, this.b), n = Math.round(t.h * 360), l = Math.round(t.s * 100), a = Math.round(t.v * 100);
      return this.a === 1 ? "hsv(".concat(n, ", ").concat(l, "%, ").concat(a, "%)") : "hsva(".concat(n, ", ").concat(l, "%, ").concat(a, "%, ").concat(this.roundA, ")");
    }, e.prototype.toHsl = function() {
      var t = fl(this.r, this.g, this.b);
      return { h: t.h * 360, s: t.s, l: t.l, a: this.a };
    }, e.prototype.toHslString = function() {
      var t = fl(this.r, this.g, this.b), n = Math.round(t.h * 360), l = Math.round(t.s * 100), a = Math.round(t.l * 100);
      return this.a === 1 ? "hsl(".concat(n, ", ").concat(l, "%, ").concat(a, "%)") : "hsla(".concat(n, ", ").concat(l, "%, ").concat(a, "%, ").concat(this.roundA, ")");
    }, e.prototype.toHex = function(t) {
      return t === void 0 && (t = !1), vl(this.r, this.g, this.b, t);
    }, e.prototype.toHexString = function(t) {
      return t === void 0 && (t = !1), "#" + this.toHex(t);
    }, e.prototype.toHex8 = function(t) {
      return t === void 0 && (t = !1), Qs(this.r, this.g, this.b, this.a, t);
    }, e.prototype.toHex8String = function(t) {
      return t === void 0 && (t = !1), "#" + this.toHex8(t);
    }, e.prototype.toHexShortString = function(t) {
      return t === void 0 && (t = !1), this.a === 1 ? this.toHexString(t) : this.toHex8String(t);
    }, e.prototype.toRgb = function() {
      return {
        r: Math.round(this.r),
        g: Math.round(this.g),
        b: Math.round(this.b),
        a: this.a
      };
    }, e.prototype.toRgbString = function() {
      var t = Math.round(this.r), n = Math.round(this.g), l = Math.round(this.b);
      return this.a === 1 ? "rgb(".concat(t, ", ").concat(n, ", ").concat(l, ")") : "rgba(".concat(t, ", ").concat(n, ", ").concat(l, ", ").concat(this.roundA, ")");
    }, e.prototype.toPercentageRgb = function() {
      var t = function(n) {
        return "".concat(Math.round(re(n, 255) * 100), "%");
      };
      return {
        r: t(this.r),
        g: t(this.g),
        b: t(this.b),
        a: this.a
      };
    }, e.prototype.toPercentageRgbString = function() {
      var t = function(n) {
        return Math.round(re(n, 255) * 100);
      };
      return this.a === 1 ? "rgb(".concat(t(this.r), "%, ").concat(t(this.g), "%, ").concat(t(this.b), "%)") : "rgba(".concat(t(this.r), "%, ").concat(t(this.g), "%, ").concat(t(this.b), "%, ").concat(this.roundA, ")");
    }, e.prototype.toName = function() {
      if (this.a === 0)
        return "transparent";
      if (this.a < 1)
        return !1;
      for (var t = "#" + vl(this.r, this.g, this.b, !1), n = 0, l = Object.entries(dn); n < l.length; n++) {
        var a = l[n], r = a[0], s = a[1];
        if (t === s)
          return r;
      }
      return !1;
    }, e.prototype.toString = function(t) {
      var n = !!t;
      t = t ?? this.format;
      var l = !1, a = this.a < 1 && this.a >= 0, r = !n && a && (t.startsWith("hex") || t === "name");
      return r ? t === "name" && this.a === 0 ? this.toName() : this.toRgbString() : (t === "rgb" && (l = this.toRgbString()), t === "prgb" && (l = this.toPercentageRgbString()), (t === "hex" || t === "hex6") && (l = this.toHexString()), t === "hex3" && (l = this.toHexString(!0)), t === "hex4" && (l = this.toHex8String(!0)), t === "hex8" && (l = this.toHex8String()), t === "name" && (l = this.toName()), t === "hsl" && (l = this.toHslString()), t === "hsv" && (l = this.toHsvString()), l || this.toHexString());
    }, e.prototype.toNumber = function() {
      return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);
    }, e.prototype.clone = function() {
      return new e(this.toString());
    }, e.prototype.lighten = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.l += t / 100, n.l = St(n.l), new e(n);
    }, e.prototype.brighten = function(t) {
      t === void 0 && (t = 10);
      var n = this.toRgb();
      return n.r = Math.max(0, Math.min(255, n.r - Math.round(255 * -(t / 100)))), n.g = Math.max(0, Math.min(255, n.g - Math.round(255 * -(t / 100)))), n.b = Math.max(0, Math.min(255, n.b - Math.round(255 * -(t / 100)))), new e(n);
    }, e.prototype.darken = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.l -= t / 100, n.l = St(n.l), new e(n);
    }, e.prototype.tint = function(t) {
      return t === void 0 && (t = 10), this.mix("white", t);
    }, e.prototype.shade = function(t) {
      return t === void 0 && (t = 10), this.mix("black", t);
    }, e.prototype.desaturate = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.s -= t / 100, n.s = St(n.s), new e(n);
    }, e.prototype.saturate = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.s += t / 100, n.s = St(n.s), new e(n);
    }, e.prototype.greyscale = function() {
      return this.desaturate(100);
    }, e.prototype.spin = function(t) {
      var n = this.toHsl(), l = (n.h + t) % 360;
      return n.h = l < 0 ? 360 + l : l, new e(n);
    }, e.prototype.mix = function(t, n) {
      n === void 0 && (n = 50);
      var l = this.toRgb(), a = new e(t).toRgb(), r = n / 100, s = {
        r: (a.r - l.r) * r + l.r,
        g: (a.g - l.g) * r + l.g,
        b: (a.b - l.b) * r + l.b,
        a: (a.a - l.a) * r + l.a
      };
      return new e(s);
    }, e.prototype.analogous = function(t, n) {
      t === void 0 && (t = 6), n === void 0 && (n = 30);
      var l = this.toHsl(), a = 360 / n, r = [this];
      for (l.h = (l.h - (a * t >> 1) + 720) % 360; --t; )
        l.h = (l.h + a) % 360, r.push(new e(l));
      return r;
    }, e.prototype.complement = function() {
      var t = this.toHsl();
      return t.h = (t.h + 180) % 360, new e(t);
    }, e.prototype.monochromatic = function(t) {
      t === void 0 && (t = 6);
      for (var n = this.toHsv(), l = n.h, a = n.s, r = n.v, s = [], o = 1 / t; t--; )
        s.push(new e({ h: l, s: a, v: r })), r = (r + o) % 1;
      return s;
    }, e.prototype.splitcomplement = function() {
      var t = this.toHsl(), n = t.h;
      return [
        this,
        new e({ h: (n + 72) % 360, s: t.s, l: t.l }),
        new e({ h: (n + 216) % 360, s: t.s, l: t.l })
      ];
    }, e.prototype.onBackground = function(t) {
      var n = this.toRgb(), l = new e(t).toRgb(), a = n.a + l.a * (1 - n.a);
      return new e({
        r: (n.r * n.a + l.r * l.a * (1 - n.a)) / a,
        g: (n.g * n.a + l.g * l.a * (1 - n.a)) / a,
        b: (n.b * n.a + l.b * l.a * (1 - n.a)) / a,
        a
      });
    }, e.prototype.triad = function() {
      return this.polyad(3);
    }, e.prototype.tetrad = function() {
      return this.polyad(4);
    }, e.prototype.polyad = function(t) {
      for (var n = this.toHsl(), l = n.h, a = [this], r = 360 / t, s = 1; s < t; s++)
        a.push(new e({ h: (l + s * r) % 360, s: n.s, l: n.l }));
      return a;
    }, e.prototype.equals = function(t) {
      return this.toRgbString() === new e(t).toRgbString();
    }, e;
  }()
);
function He(e, t = 20) {
  return e.mix("#141414", t).toString();
}
function ai(e) {
  const t = En(), n = ae("button");
  return O(() => {
    let l = {}, a = e.color;
    if (a) {
      const r = a.match(/var\((.*?)\)/);
      r && (a = window.getComputedStyle(window.document.documentElement).getPropertyValue(r[1]));
      const s = new ri(a), o = e.dark ? s.tint(20).toString() : He(s, 20);
      if (e.plain)
        l = n.cssVarBlock({
          "bg-color": e.dark ? He(s, 90) : s.tint(90).toString(),
          "text-color": a,
          "border-color": e.dark ? He(s, 50) : s.tint(50).toString(),
          "hover-text-color": `var(${n.cssVarName("color-white")})`,
          "hover-bg-color": a,
          "hover-border-color": a,
          "active-bg-color": o,
          "active-text-color": `var(${n.cssVarName("color-white")})`,
          "active-border-color": o
        }), t.value && (l[n.cssVarBlockName("disabled-bg-color")] = e.dark ? He(s, 90) : s.tint(90).toString(), l[n.cssVarBlockName("disabled-text-color")] = e.dark ? He(s, 50) : s.tint(50).toString(), l[n.cssVarBlockName("disabled-border-color")] = e.dark ? He(s, 80) : s.tint(80).toString());
      else {
        const i = e.dark ? He(s, 30) : s.tint(30).toString(), u = s.isDark() ? `var(${n.cssVarName("color-white")})` : `var(${n.cssVarName("color-black")})`;
        if (l = n.cssVarBlock({
          "bg-color": a,
          "text-color": u,
          "border-color": a,
          "hover-bg-color": i,
          "hover-text-color": u,
          "hover-border-color": i,
          "active-bg-color": o,
          "active-border-color": o
        }), t.value) {
          const c = e.dark ? He(s, 50) : s.tint(50).toString();
          l[n.cssVarBlockName("disabled-bg-color")] = c, l[n.cssVarBlockName("disabled-text-color")] = e.dark ? "rgba(255, 255, 255, 0.5)" : `var(${n.cssVarName("color-white")})`, l[n.cssVarBlockName("disabled-border-color")] = c;
        }
      }
    }
    return l;
  });
}
const oi = ne({
  name: "ElButton"
}), si = /* @__PURE__ */ ne({
  ...oi,
  props: cn,
  emits: js,
  setup(e, { expose: t, emit: n }) {
    const l = e, a = ai(l), r = ae("button"), { _ref: s, _size: o, _type: i, _disabled: u, _props: c, shouldAddSpace: h, handleClick: v } = Vs(l, n), b = O(() => [
      r.b(),
      r.m(i.value),
      r.m(o.value),
      r.is("disabled", u.value),
      r.is("loading", l.loading),
      r.is("plain", l.plain),
      r.is("round", l.round),
      r.is("circle", l.circle),
      r.is("text", l.text),
      r.is("link", l.link),
      r.is("has-bg", l.bg)
    ]);
    return t({
      ref: s,
      size: o,
      type: i,
      disabled: u,
      shouldAddSpace: h
    }), (d, p) => (B(), we(at(d.tag), fa({
      ref_key: "_ref",
      ref: s
    }, A(c), {
      class: A(b),
      style: A(a),
      onClick: A(v)
    }), {
      default: Re(() => [
        d.loading ? (B(), q(vt, { key: 0 }, [
          d.$slots.loading ? ge(d.$slots, "loading", { key: 0 }) : (B(), we(A(ct), {
            key: 1,
            class: _(A(r).is("loading"))
          }, {
            default: Re(() => [
              (B(), we(at(d.loadingIcon)))
            ]),
            _: 1
          }, 8, ["class"]))
        ], 64)) : d.icon || d.$slots.icon ? (B(), we(A(ct), { key: 1 }, {
          default: Re(() => [
            d.icon ? (B(), we(at(d.icon), { key: 0 })) : ge(d.$slots, "icon", { key: 1 })
          ]),
          _: 3
        })) : xe("v-if", !0),
        d.$slots.default ? (B(), q("span", {
          key: 2,
          class: _({ [A(r).em("text", "expand")]: A(h) })
        }, [
          ge(d.$slots, "default")
        ], 2)) : xe("v-if", !0)
      ]),
      _: 3
    }, 16, ["class", "style", "onClick"]));
  }
});
var ii = /* @__PURE__ */ Ke(si, [["__file", "button.vue"]]);
const ui = {
  size: cn.size,
  type: cn.type
}, ci = ne({
  name: "ElButtonGroup"
}), di = /* @__PURE__ */ ne({
  ...ci,
  props: ui,
  setup(e) {
    const t = e;
    kn(cr, zl({
      size: Vn(t, "size"),
      type: Vn(t, "type")
    }));
    const n = ae("button");
    return (l, a) => (B(), q("div", {
      class: _(A(n).b("group"))
    }, [
      ge(l.$slots, "default")
    ], 2));
  }
});
var fr = /* @__PURE__ */ Ke(di, [["__file", "button-group.vue"]]);
const Du = Rn(ii, {
  ButtonGroup: fr
});
zt(fr);
const hr = {
  modelValue: {
    type: [Number, String, Boolean],
    default: void 0
  },
  label: {
    type: [String, Boolean, Number, Object],
    default: void 0
  },
  value: {
    type: [String, Boolean, Number, Object],
    default: void 0
  },
  indeterminate: Boolean,
  disabled: Boolean,
  checked: Boolean,
  name: {
    type: String,
    default: void 0
  },
  trueValue: {
    type: [String, Number],
    default: void 0
  },
  falseValue: {
    type: [String, Number],
    default: void 0
  },
  trueLabel: {
    type: [String, Number],
    default: void 0
  },
  falseLabel: {
    type: [String, Number],
    default: void 0
  },
  id: {
    type: String,
    default: void 0
  },
  border: Boolean,
  size: Vt,
  tabindex: [String, Number],
  validateEvent: {
    type: Boolean,
    default: !0
  },
  ...Pl(["ariaControls"])
}, vr = {
  [Kt]: (e) => me(e) || De(e) || Oe(e),
  change: (e) => me(e) || De(e) || Oe(e)
}, lt = Symbol("checkboxGroupContextKey"), fi = ({
  model: e,
  isChecked: t
}) => {
  const n = se(lt, void 0), l = O(() => {
    var r, s;
    const o = (r = n == null ? void 0 : n.max) == null ? void 0 : r.value, i = (s = n == null ? void 0 : n.min) == null ? void 0 : s.value;
    return !ke(o) && e.value.length >= o && !t.value || !ke(i) && e.value.length <= i && t.value;
  });
  return {
    isDisabled: En(O(() => (n == null ? void 0 : n.disabled.value) || l.value)),
    isLimitDisabled: l
  };
}, hi = (e, {
  model: t,
  isLimitExceeded: n,
  hasOwnLabel: l,
  isDisabled: a,
  isLabeledByFormItem: r
}) => {
  const s = se(lt, void 0), { formItem: o } = Dt(), { emit: i } = ie();
  function u(d) {
    var p, g, C, T;
    return [!0, e.trueValue, e.trueLabel].includes(d) ? (g = (p = e.trueValue) != null ? p : e.trueLabel) != null ? g : !0 : (T = (C = e.falseValue) != null ? C : e.falseLabel) != null ? T : !1;
  }
  function c(d, p) {
    i(nn, u(d), p);
  }
  function h(d) {
    if (n.value)
      return;
    const p = d.target;
    i(nn, u(p.checked), d);
  }
  async function v(d) {
    n.value || !l.value && !a.value && r.value && (d.composedPath().some((C) => C.tagName === "LABEL") || (t.value = u([!1, e.falseValue, e.falseLabel].includes(t.value)), await ze(), c(t.value, d)));
  }
  const b = O(() => (s == null ? void 0 : s.validateEvent) || e.validateEvent);
  return de(() => e.modelValue, () => {
    b.value && (o == null || o.validate("change").catch((d) => Hl()));
  }), {
    handleChange: h,
    onClickRoot: v
  };
}, vi = (e) => {
  const t = R(!1), { emit: n } = ie(), l = se(lt, void 0), a = O(() => ke(l) === !1), r = R(!1), s = O({
    get() {
      var o, i;
      return a.value ? (o = l == null ? void 0 : l.modelValue) == null ? void 0 : o.value : (i = e.modelValue) != null ? i : t.value;
    },
    set(o) {
      var i, u;
      a.value && J(o) ? (r.value = ((i = l == null ? void 0 : l.max) == null ? void 0 : i.value) !== void 0 && o.length > (l == null ? void 0 : l.max.value) && o.length > s.value.length, r.value === !1 && ((u = l == null ? void 0 : l.changeEvent) == null || u.call(l, o))) : (n(Kt, o), t.value = o);
    }
  });
  return {
    model: s,
    isGroup: a,
    isLimitExceeded: r
  };
}, pi = (e, t, { model: n }) => {
  const l = se(lt, void 0), a = R(!1), r = O(() => Ue(e.value) ? e.label : e.value), s = O(() => {
    const c = n.value;
    return Oe(c) ? c : J(c) ? dt(r.value) ? c.map(zn).some((h) => Is(h, r.value)) : c.map(zn).includes(r.value) : c != null ? c === e.trueValue || c === e.trueLabel : !!c;
  }), o = Lt(O(() => {
    var c;
    return (c = l == null ? void 0 : l.size) == null ? void 0 : c.value;
  }), {
    prop: !0
  }), i = Lt(O(() => {
    var c;
    return (c = l == null ? void 0 : l.size) == null ? void 0 : c.value;
  })), u = O(() => !!t.default || !Ue(r.value));
  return {
    checkboxButtonSize: o,
    isChecked: s,
    isFocused: a,
    checkboxSize: i,
    hasOwnLabel: u,
    actualValue: r
  };
}, pr = (e, t) => {
  const { formItem: n } = Dt(), { model: l, isGroup: a, isLimitExceeded: r } = vi(e), {
    isFocused: s,
    isChecked: o,
    checkboxButtonSize: i,
    checkboxSize: u,
    hasOwnLabel: c,
    actualValue: h
  } = pi(e, t, { model: l }), { isDisabled: v } = fi({ model: l, isChecked: o }), { inputId: b, isLabeledByFormItem: d } = _l(e, {
    formItemContext: n,
    disableIdGeneration: c,
    disableIdManagement: a
  }), { handleChange: p, onClickRoot: g } = hi(e, {
    model: l,
    isLimitExceeded: r,
    hasOwnLabel: c,
    isDisabled: v,
    isLabeledByFormItem: d
  });
  return (() => {
    function T() {
      var S, f;
      J(l.value) && !l.value.includes(h.value) ? l.value.push(h.value) : l.value = (f = (S = e.trueValue) != null ? S : e.trueLabel) != null ? f : !0;
    }
    e.checked && T();
  })(), Et({
    from: "label act as value",
    replacement: "value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, O(() => a.value && Ue(e.value))), Et({
    from: "true-label",
    replacement: "true-value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, O(() => !!e.trueLabel)), Et({
    from: "false-label",
    replacement: "false-value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, O(() => !!e.falseLabel)), {
    inputId: b,
    isLabeledByFormItem: d,
    isChecked: o,
    isDisabled: v,
    isFocused: s,
    checkboxButtonSize: i,
    checkboxSize: u,
    hasOwnLabel: c,
    model: l,
    actualValue: h,
    handleChange: p,
    onClickRoot: g
  };
}, gi = ne({
  name: "ElCheckbox"
}), mi = /* @__PURE__ */ ne({
  ...gi,
  props: hr,
  emits: vr,
  setup(e) {
    const t = e, n = Tn(), {
      inputId: l,
      isLabeledByFormItem: a,
      isChecked: r,
      isDisabled: s,
      isFocused: o,
      checkboxSize: i,
      hasOwnLabel: u,
      model: c,
      actualValue: h,
      handleChange: v,
      onClickRoot: b
    } = pr(t, n), d = ae("checkbox"), p = O(() => [
      d.b(),
      d.m(i.value),
      d.is("disabled", s.value),
      d.is("bordered", t.border),
      d.is("checked", r.value)
    ]), g = O(() => [
      d.e("input"),
      d.is("disabled", s.value),
      d.is("checked", r.value),
      d.is("indeterminate", t.indeterminate),
      d.is("focus", o.value)
    ]);
    return (C, T) => (B(), we(at(!A(u) && A(a) ? "span" : "label"), {
      class: _(A(p)),
      "aria-controls": C.indeterminate ? C.ariaControls : null,
      onClick: A(b)
    }, {
      default: Re(() => {
        var S, f, m, w;
        return [
          ce("span", {
            class: _(A(g))
          }, [
            C.trueValue || C.falseValue || C.trueLabel || C.falseLabel ? Ve((B(), q("input", {
              key: 0,
              id: A(l),
              "onUpdate:modelValue": (y) => pt(c) ? c.value = y : null,
              class: _(A(d).e("original")),
              type: "checkbox",
              indeterminate: C.indeterminate,
              name: C.name,
              tabindex: C.tabindex,
              disabled: A(s),
              "true-value": (f = (S = C.trueValue) != null ? S : C.trueLabel) != null ? f : !0,
              "false-value": (w = (m = C.falseValue) != null ? m : C.falseLabel) != null ? w : !1,
              onChange: A(v),
              onFocus: (y) => o.value = !0,
              onBlur: (y) => o.value = !1,
              onClick: Ft(() => {
              }, ["stop"])
            }, null, 42, ["id", "onUpdate:modelValue", "indeterminate", "name", "tabindex", "disabled", "true-value", "false-value", "onChange", "onFocus", "onBlur", "onClick"])), [
              [$t, A(c)]
            ]) : Ve((B(), q("input", {
              key: 1,
              id: A(l),
              "onUpdate:modelValue": (y) => pt(c) ? c.value = y : null,
              class: _(A(d).e("original")),
              type: "checkbox",
              indeterminate: C.indeterminate,
              disabled: A(s),
              value: A(h),
              name: C.name,
              tabindex: C.tabindex,
              onChange: A(v),
              onFocus: (y) => o.value = !0,
              onBlur: (y) => o.value = !1,
              onClick: Ft(() => {
              }, ["stop"])
            }, null, 42, ["id", "onUpdate:modelValue", "indeterminate", "disabled", "value", "name", "tabindex", "onChange", "onFocus", "onBlur", "onClick"])), [
              [$t, A(c)]
            ]),
            ce("span", {
              class: _(A(d).e("inner"))
            }, null, 2)
          ], 2),
          A(u) ? (B(), q("span", {
            key: 0,
            class: _(A(d).e("label"))
          }, [
            ge(C.$slots, "default"),
            C.$slots.default ? xe("v-if", !0) : (B(), q(vt, { key: 0 }, [
              jt(We(C.label), 1)
            ], 64))
          ], 2)) : xe("v-if", !0)
        ];
      }),
      _: 3
    }, 8, ["class", "aria-controls", "onClick"]));
  }
});
var bi = /* @__PURE__ */ Ke(mi, [["__file", "checkbox.vue"]]);
const yi = ne({
  name: "ElCheckboxButton"
}), Ci = /* @__PURE__ */ ne({
  ...yi,
  props: hr,
  emits: vr,
  setup(e) {
    const t = e, n = Tn(), {
      isFocused: l,
      isChecked: a,
      isDisabled: r,
      checkboxButtonSize: s,
      model: o,
      actualValue: i,
      handleChange: u
    } = pr(t, n), c = se(lt, void 0), h = ae("checkbox"), v = O(() => {
      var d, p, g, C;
      const T = (p = (d = c == null ? void 0 : c.fill) == null ? void 0 : d.value) != null ? p : "";
      return {
        backgroundColor: T,
        borderColor: T,
        color: (C = (g = c == null ? void 0 : c.textColor) == null ? void 0 : g.value) != null ? C : "",
        boxShadow: T ? `-1px 0 0 0 ${T}` : void 0
      };
    }), b = O(() => [
      h.b("button"),
      h.bm("button", s.value),
      h.is("disabled", r.value),
      h.is("checked", a.value),
      h.is("focus", l.value)
    ]);
    return (d, p) => {
      var g, C, T, S;
      return B(), q("label", {
        class: _(A(b))
      }, [
        d.trueValue || d.falseValue || d.trueLabel || d.falseLabel ? Ve((B(), q("input", {
          key: 0,
          "onUpdate:modelValue": (f) => pt(o) ? o.value = f : null,
          class: _(A(h).be("button", "original")),
          type: "checkbox",
          name: d.name,
          tabindex: d.tabindex,
          disabled: A(r),
          "true-value": (C = (g = d.trueValue) != null ? g : d.trueLabel) != null ? C : !0,
          "false-value": (S = (T = d.falseValue) != null ? T : d.falseLabel) != null ? S : !1,
          onChange: A(u),
          onFocus: (f) => l.value = !0,
          onBlur: (f) => l.value = !1,
          onClick: Ft(() => {
          }, ["stop"])
        }, null, 42, ["onUpdate:modelValue", "name", "tabindex", "disabled", "true-value", "false-value", "onChange", "onFocus", "onBlur", "onClick"])), [
          [$t, A(o)]
        ]) : Ve((B(), q("input", {
          key: 1,
          "onUpdate:modelValue": (f) => pt(o) ? o.value = f : null,
          class: _(A(h).be("button", "original")),
          type: "checkbox",
          name: d.name,
          tabindex: d.tabindex,
          disabled: A(r),
          value: A(i),
          onChange: A(u),
          onFocus: (f) => l.value = !0,
          onBlur: (f) => l.value = !1,
          onClick: Ft(() => {
          }, ["stop"])
        }, null, 42, ["onUpdate:modelValue", "name", "tabindex", "disabled", "value", "onChange", "onFocus", "onBlur", "onClick"])), [
          [$t, A(o)]
        ]),
        d.$slots.default || d.label ? (B(), q("span", {
          key: 2,
          class: _(A(h).be("button", "inner")),
          style: Xe(A(a) ? A(v) : void 0)
        }, [
          ge(d.$slots, "default", {}, () => [
            jt(We(d.label), 1)
          ])
        ], 6)) : xe("v-if", !0)
      ], 2);
    };
  }
});
var gr = /* @__PURE__ */ Ke(Ci, [["__file", "checkbox-button.vue"]]);
const wi = Fl({
  modelValue: {
    type: $l(Array),
    default: () => []
  },
  disabled: Boolean,
  min: Number,
  max: Number,
  size: Vt,
  fill: String,
  textColor: String,
  tag: {
    type: String,
    default: "div"
  },
  validateEvent: {
    type: Boolean,
    default: !0
  },
  ...Pl(["ariaLabel"])
}), Si = {
  [Kt]: (e) => J(e),
  change: (e) => J(e)
}, xi = ne({
  name: "ElCheckboxGroup"
}), Ei = /* @__PURE__ */ ne({
  ...xi,
  props: wi,
  emits: Si,
  setup(e, { emit: t }) {
    const n = e, l = ae("checkbox"), { formItem: a } = Dt(), { inputId: r, isLabeledByFormItem: s } = _l(n, {
      formItemContext: a
    }), o = async (u) => {
      t(Kt, u), await ze(), t(nn, u);
    }, i = O({
      get() {
        return n.modelValue;
      },
      set(u) {
        o(u);
      }
    });
    return kn(lt, {
      ...ia(Kl(n), [
        "size",
        "min",
        "max",
        "disabled",
        "validateEvent",
        "fill",
        "textColor"
      ]),
      modelValue: i,
      changeEvent: o
    }), de(() => n.modelValue, () => {
      n.validateEvent && (a == null || a.validate("change").catch((u) => Hl()));
    }), (u, c) => {
      var h;
      return B(), we(at(u.tag), {
        id: A(r),
        class: _(A(l).b("group")),
        role: "group",
        "aria-label": A(s) ? void 0 : u.ariaLabel || "checkbox-group",
        "aria-labelledby": A(s) ? (h = A(a)) == null ? void 0 : h.labelId : void 0
      }, {
        default: Re(() => [
          ge(u.$slots, "default")
        ]),
        _: 3
      }, 8, ["id", "class", "aria-label", "aria-labelledby"]);
    };
  }
});
var mr = /* @__PURE__ */ Ke(Ei, [["__file", "checkbox-group.vue"]]);
const Ze = Rn(bi, {
  CheckboxButton: gr,
  CheckboxGroup: mr
});
zt(gr);
zt(mr);
const _e = /* @__PURE__ */ new Map();
if (qe) {
  let e;
  document.addEventListener("mousedown", (t) => e = t), document.addEventListener("mouseup", (t) => {
    if (e) {
      for (const n of _e.values())
        for (const { documentHandler: l } of n)
          l(t, e);
      e = void 0;
    }
  });
}
function gl(e, t) {
  let n = [];
  return J(t.arg) ? n = t.arg : Bl(t.arg) && n.push(t.arg), function(l, a) {
    const r = t.instance.popperRef, s = l.target, o = a == null ? void 0 : a.target, i = !t || !t.instance, u = !s || !o, c = e.contains(s) || e.contains(o), h = e === s, v = n.length && n.some((d) => d == null ? void 0 : d.contains(s)) || n.length && n.includes(o), b = r && (r.contains(s) || r.contains(o));
    i || u || c || h || v || b || t.value(l, a);
  };
}
const Ri = {
  beforeMount(e, t) {
    _e.has(e) || _e.set(e, []), _e.get(e).push({
      documentHandler: gl(e, t),
      bindingFn: t.value
    });
  },
  updated(e, t) {
    _e.has(e) || _e.set(e, []);
    const n = _e.get(e), l = n.findIndex((r) => r.bindingFn === t.oldValue), a = {
      documentHandler: gl(e, t),
      bindingFn: t.value
    };
    l >= 0 ? n.splice(l, 1, a) : n.push(a);
  },
  unmounted(e) {
    _e.delete(e);
  }
}, Zt = function(e) {
  var t;
  return (t = e.target) == null ? void 0 : t.closest("td");
}, Ti = function(e, t, n, l, a) {
  if (!t && !l && (!a || J(a) && !a.length))
    return e;
  me(n) ? n = n === "descending" ? -1 : 1 : n = n && n < 0 ? -1 : 1;
  const r = l ? null : function(o, i) {
    return a ? (J(a) || (a = [a]), a.map((u) => me(u) ? tn(o, u) : u(o, i, e))) : (t !== "$key" && dt(o) && "$value" in o && (o = o.$value), [dt(o) ? tn(o, t) : o]);
  }, s = function(o, i) {
    if (l)
      return l(o.value, i.value);
    for (let u = 0, c = o.key.length; u < c; u++) {
      if (o.key[u] < i.key[u])
        return -1;
      if (o.key[u] > i.key[u])
        return 1;
    }
    return 0;
  };
  return e.map((o, i) => ({
    value: o,
    index: i,
    key: r ? r(o, i) : null
  })).sort((o, i) => {
    let u = s(o, i);
    return u || (u = o.index - i.index), u * +n;
  }).map((o) => o.value);
}, br = function(e, t) {
  let n = null;
  return e.columns.forEach((l) => {
    l.id === t && (n = l);
  }), n;
}, ki = function(e, t) {
  let n = null;
  for (let l = 0; l < e.columns.length; l++) {
    const a = e.columns[l];
    if (a.columnKey === t) {
      n = a;
      break;
    }
  }
  return n || ua("ElTable", `No column matching with column-key: ${t}`), n;
}, ml = function(e, t, n) {
  const l = (t.className || "").match(new RegExp(`${n}-table_[^\\s]+`, "gm"));
  return l ? br(e, l[0]) : null;
}, oe = (e, t) => {
  if (!e)
    throw new Error("Row is required when get row identity");
  if (me(t)) {
    if (!t.includes("."))
      return `${e[t]}`;
    const n = t.split(".");
    let l = e;
    for (const a of n)
      l = l[a];
    return `${l}`;
  } else if (Te(t))
    return t.call(null, e);
}, Qe = function(e, t, n = !1, l = "children") {
  const a = e || [], r = {};
  return a.forEach((s, o) => {
    if (r[oe(s, t)] = { row: s, index: o }, n) {
      const i = s[l];
      J(i) && Object.assign(r, Qe(i, t, !0, l));
    }
  }), r;
};
function Ai(e, t) {
  const n = {};
  let l;
  for (l in e)
    n[l] = e[l];
  for (l in t)
    if (ft(t, l)) {
      const a = t[l];
      ke(a) || (n[l] = a);
    }
  return n;
}
function Nn(e) {
  return e === "" || ke(e) || (e = Number.parseInt(e, 10), Number.isNaN(e) && (e = "")), e;
}
function yr(e) {
  return e === "" || ke(e) || (e = Nn(e), Number.isNaN(e) && (e = 80)), e;
}
function Mi(e) {
  return De(e) ? e : me(e) ? /^\d+(?:px)?$/.test(e) ? Number.parseInt(e, 10) : e : null;
}
function Li(...e) {
  return e.length === 0 ? (t) => t : e.length === 1 ? e[0] : e.reduce((t, n) => (...l) => t(n(...l)));
}
function Bt(e, t, n, l, a, r) {
  let s = r ?? 0, o = !1;
  const i = e.indexOf(t), u = i !== -1, c = a == null ? void 0 : a.call(null, t, s), h = (b) => {
    b === "add" ? e.push(t) : e.splice(i, 1), o = !0;
  }, v = (b) => {
    let d = 0;
    const p = (l == null ? void 0 : l.children) && b[l.children];
    return p && J(p) && (d += p.length, p.forEach((g) => {
      d += v(g);
    })), d;
  };
  return (!a || c) && (Oe(n) ? n && !u ? h("add") : !n && u && h("remove") : h(u ? "remove" : "add")), !(l != null && l.checkStrictly) && (l != null && l.children) && J(t[l.children]) && t[l.children].forEach((b) => {
    const d = Bt(e, b, n ?? !u, l, a, s + 1);
    s += v(b) + 1, d && (o = d);
  }), o;
}
function Oi(e, t, n = "children", l = "hasChildren") {
  const a = (s) => !(J(s) && s.length);
  function r(s, o, i) {
    t(s, o, i), o.forEach((u) => {
      if (u[l]) {
        t(u, null, i + 1);
        return;
      }
      const c = u[n];
      a(c) || r(u, c, i + 1);
    });
  }
  e.forEach((s) => {
    if (s[l]) {
      t(s, null, 0);
      return;
    }
    const o = s[n];
    a(o) || r(s, o, 0);
  });
}
const Ni = (e, t, n, l) => {
  const a = {
    strategy: "fixed",
    ...e.popperOptions
  }, r = Te(l.tooltipFormatter) ? l.tooltipFormatter({
    row: n,
    column: l,
    cellValue: Il(n, l.property).value
  }) : void 0;
  return ha(r) ? {
    slotContent: r,
    content: null,
    ...e,
    popperOptions: a
  } : {
    slotContent: null,
    content: r ?? t,
    ...e,
    popperOptions: a
  };
};
let pe = null;
function Fi(e, t, n, l, a, r) {
  const s = Ni(e, t, n, l), o = {
    ...s,
    slotContent: void 0
  };
  if ((pe == null ? void 0 : pe.trigger) === a) {
    const b = pe.vm.component;
    ur(b.props, o), s.slotContent && (b.slots.content = () => [s.slotContent]);
    return;
  }
  pe == null || pe();
  const i = r == null ? void 0 : r.refs.tableWrapper, u = i == null ? void 0 : i.dataset.prefix, c = Me(Wl, {
    virtualTriggering: !0,
    virtualRef: a,
    appendTo: i,
    placement: "top",
    transition: "none",
    offset: 0,
    hideAfter: 0,
    ...o
  }, s.slotContent ? {
    content: () => s.slotContent
  } : void 0);
  c.appContext = { ...r.appContext, ...r };
  const h = document.createElement("div");
  Kn(c, h), c.component.exposed.onOpen();
  const v = i == null ? void 0 : i.querySelector(`.${u}-scrollbar__wrap`);
  pe = () => {
    Kn(null, h), v == null || v.removeEventListener("scroll", pe), pe = null;
  }, pe.trigger = a, pe.vm = c, v == null || v.addEventListener("scroll", pe);
}
function Cr(e) {
  return e.children ? Ws(e.children, Cr) : [e];
}
function bl(e, t) {
  return e + t.colSpan;
}
const wr = (e, t, n, l) => {
  let a = 0, r = e;
  const s = n.states.columns.value;
  if (l) {
    const i = Cr(l[e]);
    a = s.slice(0, s.indexOf(i[0])).reduce(bl, 0), r = a + i.reduce(bl, 0) - 1;
  } else
    a = e;
  let o;
  switch (t) {
    case "left":
      r < n.states.fixedLeafColumnsLength.value && (o = "left");
      break;
    case "right":
      a >= s.length - n.states.rightFixedLeafColumnsLength.value && (o = "right");
      break;
    default:
      r < n.states.fixedLeafColumnsLength.value ? o = "left" : a >= s.length - n.states.rightFixedLeafColumnsLength.value && (o = "right");
  }
  return o ? {
    direction: o,
    start: a,
    after: r
  } : {};
}, Fn = (e, t, n, l, a, r = 0) => {
  const s = [], { direction: o, start: i, after: u } = wr(t, n, l, a);
  if (o) {
    const c = o === "left";
    s.push(`${e}-fixed-column--${o}`), c && u + r === l.states.fixedLeafColumnsLength.value - 1 ? s.push("is-last-column") : !c && i - r === l.states.columns.value.length - l.states.rightFixedLeafColumnsLength.value && s.push("is-first-column");
  }
  return s;
};
function yl(e, t) {
  return e + (mt(t.realWidth) || Number.isNaN(t.realWidth) ? Number(t.width) : t.realWidth);
}
const $n = (e, t, n, l) => {
  const {
    direction: a,
    start: r = 0,
    after: s = 0
  } = wr(e, t, n, l);
  if (!a)
    return;
  const o = {}, i = a === "left", u = n.states.columns.value;
  return i ? o.left = u.slice(0, r).reduce(yl, 0) : o.right = u.slice(s + 1).reverse().reduce(yl, 0), o;
}, Je = (e, t) => {
  e && (Number.isNaN(e[t]) || (e[t] = `${e[t]}px`));
};
function $i(e) {
  const t = ie(), n = R(!1), l = R([]);
  return {
    updateExpandRows: () => {
      const i = e.data.value || [], u = e.rowKey.value;
      if (n.value)
        l.value = i.slice();
      else if (u) {
        const c = Qe(l.value, u);
        l.value = i.reduce((h, v) => {
          const b = oe(v, u);
          return c[b] && h.push(v), h;
        }, []);
      } else
        l.value = [];
    },
    toggleRowExpansion: (i, u) => {
      Bt(l.value, i, u) && t.emit("expand-change", i, l.value.slice());
    },
    setExpandRowKeys: (i) => {
      t.store.assertRowKey();
      const u = e.data.value || [], c = e.rowKey.value, h = Qe(u, c);
      l.value = i.reduce((v, b) => {
        const d = h[b];
        return d && v.push(d.row), v;
      }, []);
    },
    isRowExpanded: (i) => {
      const u = e.rowKey.value;
      return u ? !!Qe(l.value, u)[oe(i, u)] : l.value.includes(i);
    },
    states: {
      expandRows: l,
      defaultExpandAll: n
    }
  };
}
function Pi(e) {
  const t = ie(), n = R(null), l = R(null), a = (u) => {
    t.store.assertRowKey(), n.value = u, s(u);
  }, r = () => {
    n.value = null;
  }, s = (u) => {
    const { data: c, rowKey: h } = e;
    let v = null;
    h.value && (v = (A(c) || []).find((b) => oe(b, h.value) === u)), l.value = v, t.emit("current-change", l.value, null);
  };
  return {
    setCurrentRowKey: a,
    restoreCurrentRowKey: r,
    setCurrentRowByKey: s,
    updateCurrentRow: (u) => {
      const c = l.value;
      if (u && u !== c) {
        l.value = u, t.emit("current-change", l.value, c);
        return;
      }
      !u && c && (l.value = null, t.emit("current-change", null, c));
    },
    updateCurrentRowData: () => {
      const u = e.rowKey.value, c = e.data.value || [], h = l.value;
      if (!c.includes(h) && h) {
        if (u) {
          const v = oe(h, u);
          s(v);
        } else
          l.value = null;
        mt(l.value) && t.emit("current-change", null, h);
      } else n.value && (s(n.value), r());
    },
    states: {
      _currentRowKey: n,
      currentRow: l
    }
  };
}
function Hi(e) {
  const t = R([]), n = R({}), l = R(16), a = R(!1), r = R({}), s = R("hasChildren"), o = R("children"), i = R(!1), u = ie(), c = O(() => {
    if (!e.rowKey.value)
      return {};
    const f = e.data.value || [];
    return v(f);
  }), h = O(() => {
    const f = e.rowKey.value, m = Object.keys(r.value), w = {};
    return m.length && m.forEach((y) => {
      if (r.value[y].length) {
        const E = { children: [] };
        r.value[y].forEach((k) => {
          const F = oe(k, f);
          E.children.push(F), k[s.value] && !w[F] && (w[F] = { children: [] });
        }), w[y] = E;
      }
    }), w;
  }), v = (f) => {
    const m = e.rowKey.value, w = {};
    return Oi(f, (y, E, k) => {
      const F = oe(y, m);
      J(E) ? w[F] = {
        children: E.map((V) => oe(V, m)),
        level: k
      } : a.value && (w[F] = {
        children: [],
        lazy: !0,
        level: k
      });
    }, o.value, s.value), w;
  }, b = (f = !1, m = ((w) => (w = u.store) == null ? void 0 : w.states.defaultExpandAll.value)()) => {
    var w;
    const y = c.value, E = h.value, k = Object.keys(y), F = {};
    if (k.length) {
      const V = A(n), $ = [], z = (D, Y) => {
        if (f)
          return t.value ? m || t.value.includes(Y) : !!(m || D != null && D.expanded);
        {
          const Z = m || t.value && t.value.includes(Y);
          return !!(D != null && D.expanded || Z);
        }
      };
      k.forEach((D) => {
        const Y = V[D], Z = { ...y[D] };
        if (Z.expanded = z(Y, D), Z.lazy) {
          const { loaded: M = !1, loading: x = !1 } = Y || {};
          Z.loaded = !!M, Z.loading = !!x, $.push(D);
        }
        F[D] = Z;
      });
      const Q = Object.keys(E);
      a.value && Q.length && $.length && Q.forEach((D) => {
        const Y = V[D], Z = E[D].children;
        if ($.includes(D)) {
          if (F[D].children.length !== 0)
            throw new Error("[ElTable]children must be an empty array.");
          F[D].children = Z;
        } else {
          const { loaded: M = !1, loading: x = !1 } = Y || {};
          F[D] = {
            lazy: !0,
            loaded: !!M,
            loading: !!x,
            expanded: z(Y, D),
            children: Z,
            level: ""
          };
        }
      });
    }
    n.value = F, (w = u.store) == null || w.updateTableScrollY();
  };
  de(() => t.value, () => {
    b(!0);
  }), de(() => c.value, () => {
    b();
  }), de(() => h.value, () => {
    b();
  });
  const d = (f) => {
    t.value = f, b();
  }, p = (f) => a.value && f && "loaded" in f && !f.loaded, g = (f, m) => {
    u.store.assertRowKey();
    const w = e.rowKey.value, y = oe(f, w), E = y && n.value[y];
    if (y && E && "expanded" in E) {
      const k = E.expanded;
      m = ke(m) ? !E.expanded : m, n.value[y].expanded = m, k !== m && u.emit("expand-change", f, m), p(E) && T(f, y, E), u.store.updateTableScrollY();
    }
  }, C = (f) => {
    u.store.assertRowKey();
    const m = e.rowKey.value, w = oe(f, m), y = n.value[w];
    p(y) ? T(f, w, y) : g(f, void 0);
  }, T = (f, m, w) => {
    const { load: y } = u.props;
    y && !n.value[m].loaded && (n.value[m].loading = !0, y(f, w, (E) => {
      if (!J(E))
        throw new TypeError("[ElTable] data must be an array");
      n.value[m].loading = !1, n.value[m].loaded = !0, n.value[m].expanded = !0, E.length && (r.value[m] = E), u.emit("expand-change", f, !0);
    }));
  };
  return {
    loadData: T,
    loadOrToggle: C,
    toggleTreeExpansion: g,
    updateTreeExpandKeys: d,
    updateTreeData: b,
    updateKeyChildren: (f, m) => {
      const { lazy: w, rowKey: y } = u.props;
      if (w) {
        if (!y)
          throw new Error("[Table] rowKey is required in updateKeyChild");
        r.value[f] && (r.value[f] = m);
      }
    },
    normalize: v,
    states: {
      expandRowKeys: t,
      treeData: n,
      indent: l,
      lazy: a,
      lazyTreeNodeMap: r,
      lazyColumnIdentifier: s,
      childrenColumnName: o,
      checkStrictly: i
    }
  };
}
const _i = (e, t) => {
  const n = t.sortingColumn;
  return !n || me(n.sortable) ? e : Ti(e, t.sortProp, t.sortOrder, n.sortMethod, n.sortBy);
}, Rt = (e) => {
  const t = [];
  return e.forEach((n) => {
    n.children && n.children.length > 0 ? t.push.apply(t, Rt(n.children)) : t.push(n);
  }), t;
};
function Bi() {
  var e;
  const t = ie(), { size: n } = Kl((e = t.proxy) == null ? void 0 : e.$props), l = R(null), a = R([]), r = R([]), s = R(!1), o = R([]), i = R([]), u = R([]), c = R([]), h = R([]), v = R([]), b = R([]), d = R([]), p = [], g = R(0), C = R(0), T = R(0), S = R(!1), f = R([]), m = R(!1), w = R(!1), y = R(null), E = R({}), k = R(null), F = R(null), V = R(null), $ = R(null), z = R(null), Q = O(() => l.value ? Qe(f.value, l.value) : void 0);
  de(a, () => {
    var L;
    t.state && (M(!1), t.props.tableLayout === "auto" && ((L = t.refs.tableHeaderRef) == null || L.updateFixedColumnStyle()));
  }, {
    deep: !0
  });
  const D = () => {
    if (!l.value)
      throw new Error("[ElTable] prop row-key is required");
  }, Y = (L) => {
    var P;
    (P = L.children) == null || P.forEach((H) => {
      H.fixed = L.fixed, Y(H);
    });
  }, Z = () => {
    var L, P;
    o.value.forEach((X) => {
      Y(X);
    }), c.value = o.value.filter((X) => X.type !== "selection" && [!0, "left"].includes(X.fixed));
    let H;
    if (((P = (L = o.value) == null ? void 0 : L[0]) == null ? void 0 : P.type) === "selection") {
      const X = o.value[0];
      H = [!0, "left"].includes(X.fixed) || c.value.length && X.fixed !== "right", H && c.value.unshift(X);
    }
    h.value = o.value.filter((X) => X.fixed === "right");
    const I = o.value.filter((X) => (H ? X.type !== "selection" : !0) && !X.fixed);
    i.value = [].concat(c.value).concat(I).concat(h.value);
    const K = Rt(I), U = Rt(c.value), he = Rt(h.value);
    g.value = K.length, C.value = U.length, T.value = he.length, u.value = [].concat(U).concat(K).concat(he), s.value = c.value.length > 0 || h.value.length > 0;
  }, M = (L, P = !1) => {
    L && Z(), P ? t.state.doLayout() : t.state.debouncedUpdateLayout();
  }, x = (L) => Q.value ? !!Q.value[oe(L, l.value)] : f.value.includes(L), N = () => {
    S.value = !1;
    const L = f.value;
    f.value = [], L.length && t.emit("selection-change", []);
  }, G = () => {
    var L, P;
    let H;
    if (l.value) {
      H = [];
      const I = (P = (L = t == null ? void 0 : t.store) == null ? void 0 : L.states) == null ? void 0 : P.childrenColumnName.value, K = Qe(a.value, l.value, !0, I);
      for (const U in Q.value)
        ft(Q.value, U) && !K[U] && H.push(Q.value[U].row);
    } else
      H = f.value.filter((I) => !a.value.includes(I));
    if (H.length) {
      const I = f.value.filter((K) => !H.includes(K));
      f.value = I, t.emit("selection-change", I.slice());
    }
  }, ee = () => (f.value || []).slice(), te = (L, P, H = !0, I = !1) => {
    var K, U, he, X;
    const Fe = {
      children: (U = (K = t == null ? void 0 : t.store) == null ? void 0 : K.states) == null ? void 0 : U.childrenColumnName.value,
      checkStrictly: (X = (he = t == null ? void 0 : t.store) == null ? void 0 : he.states) == null ? void 0 : X.checkStrictly.value
    };
    if (Bt(f.value, L, P, Fe, I ? void 0 : y.value, a.value.indexOf(L))) {
      const Ct = (f.value || []).slice();
      H && t.emit("select", Ct, L), t.emit("selection-change", Ct);
    }
  }, ue = () => {
    var L, P;
    const H = w.value ? !S.value : !(S.value || f.value.length);
    S.value = H;
    let I = !1, K = 0;
    const U = (P = (L = t == null ? void 0 : t.store) == null ? void 0 : L.states) == null ? void 0 : P.rowKey.value, { childrenColumnName: he } = t.store.states, X = {
      children: he.value,
      checkStrictly: !1
    };
    a.value.forEach((Fe, yt) => {
      const Ct = yt + K;
      Bt(f.value, Fe, H, X, y.value, Ct) && (I = !0), K += le(oe(Fe, U));
    }), I && t.emit("selection-change", f.value ? f.value.slice() : []), t.emit("select-all", (f.value || []).slice());
  }, fe = () => {
    a.value.forEach((L) => {
      const P = oe(L, l.value), H = Q.value[P];
      H && (f.value[H.index] = L);
    });
  }, Se = () => {
    var L;
    if (((L = a.value) == null ? void 0 : L.length) === 0) {
      S.value = !1;
      return;
    }
    const { childrenColumnName: P } = t.store.states;
    let H = 0, I = 0;
    const K = (he) => {
      var X;
      for (const Fe of he) {
        const yt = y.value && y.value.call(null, Fe, H);
        if (x(Fe))
          I++;
        else if (!y.value || yt)
          return !1;
        if (H++, (X = Fe[P.value]) != null && X.length && !K(Fe[P.value]))
          return !1;
      }
      return !0;
    }, U = K(a.value || []);
    S.value = I === 0 ? !1 : U;
  }, le = (L) => {
    var P;
    if (!t || !t.store)
      return 0;
    const { treeData: H } = t.store.states;
    let I = 0;
    const K = (P = H.value[L]) == null ? void 0 : P.children;
    return K && (I += K.length, K.forEach((U) => {
      I += le(U);
    })), I;
  }, be = (L, P) => {
    J(L) || (L = [L]);
    const H = {};
    return L.forEach((I) => {
      E.value[I.id] = P, H[I.columnKey || I.id] = P;
    }), H;
  }, ye = (L, P, H) => {
    F.value && F.value !== L && (F.value.order = null), F.value = L, V.value = P, $.value = H;
  }, bt = () => {
    let L = A(r);
    Object.keys(E.value).forEach((P) => {
      const H = E.value[P];
      if (!H || H.length === 0)
        return;
      const I = br({
        columns: u.value
      }, P);
      I && I.filterMethod && (L = L.filter((K) => H.some((U) => I.filterMethod.call(null, U, K, I))));
    }), k.value = L;
  }, Hn = () => {
    a.value = _i(k.value, {
      sortingColumn: F.value,
      sortProp: V.value,
      sortOrder: $.value
    });
  }, Pr = (L = void 0) => {
    L && L.filter || bt(), Hn();
  }, Hr = (L) => {
    const { tableHeaderRef: P } = t.refs;
    if (!P)
      return;
    const H = Object.assign({}, P.filterPanels), I = Object.keys(H);
    if (I.length)
      if (me(L) && (L = [L]), J(L)) {
        const K = L.map((U) => ki({
          columns: u.value
        }, U));
        I.forEach((U) => {
          const he = K.find((X) => X.id === U);
          he && (he.filteredValue = []);
        }), t.store.commit("filterChange", {
          column: K,
          values: [],
          silent: !0,
          multi: !0
        });
      } else
        I.forEach((K) => {
          const U = u.value.find((he) => he.id === K);
          U && (U.filteredValue = []);
        }), E.value = {}, t.store.commit("filterChange", {
          column: {},
          values: [],
          silent: !0
        });
  }, _r = () => {
    F.value && (ye(null, null, null), t.store.commit("changeSortCondition", {
      silent: !0
    }));
  }, {
    setExpandRowKeys: Br,
    toggleRowExpansion: _n,
    updateExpandRows: Wr,
    states: Ir,
    isRowExpanded: Dr
  } = $i({
    data: a,
    rowKey: l
  }), {
    updateTreeExpandKeys: Vr,
    toggleTreeExpansion: zr,
    updateTreeData: Kr,
    updateKeyChildren: jr,
    loadOrToggle: Gr,
    states: Ur
  } = Hi({
    data: a,
    rowKey: l
  }), {
    updateCurrentRowData: qr,
    updateCurrentRow: Yr,
    setCurrentRowKey: Xr,
    states: Qr
  } = Pi({
    data: a,
    rowKey: l
  });
  return {
    assertRowKey: D,
    updateColumns: Z,
    scheduleLayout: M,
    isSelected: x,
    clearSelection: N,
    cleanSelection: G,
    getSelectionRows: ee,
    toggleRowSelection: te,
    _toggleAllSelection: ue,
    toggleAllSelection: null,
    updateSelectionByRowKey: fe,
    updateAllSelected: Se,
    updateFilters: be,
    updateCurrentRow: Yr,
    updateSort: ye,
    execFilter: bt,
    execSort: Hn,
    execQuery: Pr,
    clearFilter: Hr,
    clearSort: _r,
    toggleRowExpansion: _n,
    setExpandRowKeysAdapter: (L) => {
      Br(L), Vr(L);
    },
    setCurrentRowKey: Xr,
    toggleRowExpansionAdapter: (L, P) => {
      u.value.some(({ type: I }) => I === "expand") ? _n(L, P) : zr(L, P);
    },
    isRowExpanded: Dr,
    updateExpandRows: Wr,
    updateCurrentRowData: qr,
    loadOrToggle: Gr,
    updateTreeData: Kr,
    updateKeyChildren: jr,
    states: {
      tableSize: n,
      rowKey: l,
      data: a,
      _data: r,
      isComplex: s,
      _columns: o,
      originColumns: i,
      columns: u,
      fixedColumns: c,
      rightFixedColumns: h,
      leafColumns: v,
      fixedLeafColumns: b,
      rightFixedLeafColumns: d,
      updateOrderFns: p,
      leafColumnsLength: g,
      fixedLeafColumnsLength: C,
      rightFixedLeafColumnsLength: T,
      isAllSelected: S,
      selection: f,
      reserveSelection: m,
      selectOnIndeterminate: w,
      selectable: y,
      filters: E,
      filteredData: k,
      sortingColumn: F,
      sortProp: V,
      sortOrder: $,
      hoverRow: z,
      ...Ir,
      ...Ur,
      ...Qr
    }
  };
}
function fn(e, t) {
  return e.map((n) => {
    var l;
    return n.id === t.id ? t : ((l = n.children) != null && l.length && (n.children = fn(n.children, t)), n);
  });
}
function hn(e) {
  e.forEach((t) => {
    var n, l;
    t.no = (n = t.getColumnIndex) == null ? void 0 : n.call(t), (l = t.children) != null && l.length && hn(t.children);
  }), e.sort((t, n) => t.no - n.no);
}
function Wi() {
  const e = ie(), t = Bi();
  return {
    ns: ae("table"),
    ...t,
    mutations: {
      setData(s, o) {
        const i = A(s._data) !== o;
        s.data.value = o, s._data.value = o, e.store.execQuery(), e.store.updateCurrentRowData(), e.store.updateExpandRows(), e.store.updateTreeData(e.store.states.defaultExpandAll.value), A(s.reserveSelection) ? (e.store.assertRowKey(), e.store.updateSelectionByRowKey()) : i ? e.store.clearSelection() : e.store.cleanSelection(), e.store.updateAllSelected(), e.$ready && e.store.scheduleLayout();
      },
      insertColumn(s, o, i, u) {
        const c = A(s._columns);
        let h = [];
        i ? (i && !i.children && (i.children = []), i.children.push(o), h = fn(c, i)) : (c.push(o), h = c), hn(h), s._columns.value = h, s.updateOrderFns.push(u), o.type === "selection" && (s.selectable.value = o.selectable, s.reserveSelection.value = o.reserveSelection), e.$ready && (e.store.updateColumns(), e.store.scheduleLayout());
      },
      updateColumnOrder(s, o) {
        var i;
        ((i = o.getColumnIndex) == null ? void 0 : i.call(o)) !== o.no && (hn(s._columns.value), e.$ready && e.store.updateColumns());
      },
      removeColumn(s, o, i, u) {
        const c = A(s._columns) || [];
        if (i)
          i.children.splice(i.children.findIndex((v) => v.id === o.id), 1), ze(() => {
            var v;
            ((v = i.children) == null ? void 0 : v.length) === 0 && delete i.children;
          }), s._columns.value = fn(c, i);
        else {
          const v = c.indexOf(o);
          v > -1 && (c.splice(v, 1), s._columns.value = c);
        }
        const h = s.updateOrderFns.indexOf(u);
        h > -1 && s.updateOrderFns.splice(h, 1), e.$ready && (e.store.updateColumns(), e.store.scheduleLayout());
      },
      sort(s, o) {
        const { prop: i, order: u, init: c } = o;
        if (i) {
          const h = A(s.columns).find((v) => v.property === i);
          h && (h.order = u, e.store.updateSort(h, i, u), e.store.commit("changeSortCondition", { init: c }));
        }
      },
      changeSortCondition(s, o) {
        const { sortingColumn: i, sortProp: u, sortOrder: c } = s, h = A(i), v = A(u), b = A(c);
        mt(b) && (s.sortingColumn.value = null, s.sortProp.value = null);
        const d = { filter: !0 };
        e.store.execQuery(d), (!o || !(o.silent || o.init)) && e.emit("sort-change", {
          column: h,
          prop: v,
          order: b
        }), e.store.updateTableScrollY();
      },
      filterChange(s, o) {
        const { column: i, values: u, silent: c } = o, h = e.store.updateFilters(i, u);
        e.store.execQuery(), c || e.emit("filter-change", h), e.store.updateTableScrollY();
      },
      toggleAllSelection() {
        e.store.toggleAllSelection();
      },
      rowSelectedChanged(s, o) {
        e.store.toggleRowSelection(o), e.store.updateAllSelected();
      },
      setHoverRow(s, o) {
        s.hoverRow.value = o;
      },
      setCurrentRow(s, o) {
        e.store.updateCurrentRow(o);
      }
    },
    commit: function(s, ...o) {
      const i = e.store.mutations;
      if (i[s])
        i[s].apply(e, [e.store.states].concat(o));
      else
        throw new Error(`Action not found: ${s}`);
    },
    updateTableScrollY: function() {
      ze(() => e.layout.updateScrollY.apply(e.layout));
    }
  };
}
const it = {
  rowKey: "rowKey",
  defaultExpandAll: "defaultExpandAll",
  selectOnIndeterminate: "selectOnIndeterminate",
  indent: "indent",
  lazy: "lazy",
  data: "data",
  "treeProps.hasChildren": {
    key: "lazyColumnIdentifier",
    default: "hasChildren"
  },
  "treeProps.children": {
    key: "childrenColumnName",
    default: "children"
  },
  "treeProps.checkStrictly": {
    key: "checkStrictly",
    default: !1
  }
};
function Ii(e, t) {
  if (!e)
    throw new Error("Table is required.");
  const n = Wi();
  return n.toggleAllSelection = Ot(n._toggleAllSelection, 10), Object.keys(it).forEach((l) => {
    Sr(xr(t, l), l, n);
  }), Di(n, t), n;
}
function Di(e, t) {
  Object.keys(it).forEach((n) => {
    de(() => xr(t, n), (l) => {
      Sr(l, n, e);
    });
  });
}
function Sr(e, t, n) {
  let l = e, a = it[t];
  dt(it[t]) && (a = a.key, l = l || it[t].default), n.states[a].value = l;
}
function xr(e, t) {
  if (t.includes(".")) {
    const n = t.split(".");
    let l = e;
    return n.forEach((a) => {
      l = l[a];
    }), l;
  } else
    return e[t];
}
class Vi {
  constructor(t) {
    this.observers = [], this.table = null, this.store = null, this.columns = [], this.fit = !0, this.showHeader = !0, this.height = R(null), this.scrollX = R(!1), this.scrollY = R(!1), this.bodyWidth = R(null), this.fixedWidth = R(null), this.rightFixedWidth = R(null), this.gutterWidth = 0;
    for (const n in t)
      ft(t, n) && (pt(this[n]) ? this[n].value = t[n] : this[n] = t[n]);
    if (!this.table)
      throw new Error("Table is required for Table Layout");
    if (!this.store)
      throw new Error("Store is required for Table Layout");
  }
  updateScrollY() {
    const t = this.height.value;
    if (mt(t))
      return !1;
    const n = this.table.refs.scrollBarRef;
    if (this.table.vnode.el && (n != null && n.wrapRef)) {
      let l = !0;
      const a = this.scrollY.value;
      return l = n.wrapRef.scrollHeight > n.wrapRef.clientHeight, this.scrollY.value = l, a !== l;
    }
    return !1;
  }
  setHeight(t, n = "height") {
    if (!qe)
      return;
    const l = this.table.vnode.el;
    if (t = Mi(t), this.height.value = Number(t), !l && (t || t === 0))
      return ze(() => this.setHeight(t, n));
    De(t) ? (l.style[n] = `${t}px`, this.updateElsHeight()) : me(t) && (l.style[n] = t, this.updateElsHeight());
  }
  setMaxHeight(t) {
    this.setHeight(t, "max-height");
  }
  getFlattenColumns() {
    const t = [];
    return this.table.store.states.columns.value.forEach((l) => {
      l.isColumnGroup ? t.push.apply(t, l.columns) : t.push(l);
    }), t;
  }
  updateElsHeight() {
    this.updateScrollY(), this.notifyObservers("scrollable");
  }
  headerDisplayNone(t) {
    if (!t)
      return !0;
    let n = t;
    for (; n.tagName !== "DIV"; ) {
      if (getComputedStyle(n).display === "none")
        return !0;
      n = n.parentElement;
    }
    return !1;
  }
  updateColumnsWidth() {
    if (!qe)
      return;
    const t = this.fit, n = this.table.vnode.el.clientWidth;
    let l = 0;
    const a = this.getFlattenColumns(), r = a.filter((i) => !De(i.width));
    if (a.forEach((i) => {
      De(i.width) && i.realWidth && (i.realWidth = null);
    }), r.length > 0 && t) {
      if (a.forEach((i) => {
        l += Number(i.width || i.minWidth || 80);
      }), l <= n) {
        this.scrollX.value = !1;
        const i = n - l;
        if (r.length === 1)
          r[0].realWidth = Number(r[0].minWidth || 80) + i;
        else {
          const u = r.reduce((v, b) => v + Number(b.minWidth || 80), 0), c = i / u;
          let h = 0;
          r.forEach((v, b) => {
            if (b === 0)
              return;
            const d = Math.floor(Number(v.minWidth || 80) * c);
            h += d, v.realWidth = Number(v.minWidth || 80) + d;
          }), r[0].realWidth = Number(r[0].minWidth || 80) + i - h;
        }
      } else
        this.scrollX.value = !0, r.forEach((i) => {
          i.realWidth = Number(i.minWidth);
        });
      this.bodyWidth.value = Math.max(l, n), this.table.state.resizeState.value.width = this.bodyWidth.value;
    } else
      a.forEach((i) => {
        !i.width && !i.minWidth ? i.realWidth = 80 : i.realWidth = Number(i.width || i.minWidth), l += i.realWidth;
      }), this.scrollX.value = l > n, this.bodyWidth.value = l;
    const s = this.store.states.fixedColumns.value;
    if (s.length > 0) {
      let i = 0;
      s.forEach((u) => {
        i += Number(u.realWidth || u.width);
      }), this.fixedWidth.value = i;
    }
    const o = this.store.states.rightFixedColumns.value;
    if (o.length > 0) {
      let i = 0;
      o.forEach((u) => {
        i += Number(u.realWidth || u.width);
      }), this.rightFixedWidth.value = i;
    }
    this.notifyObservers("columns");
  }
  addObserver(t) {
    this.observers.push(t);
  }
  removeObserver(t) {
    const n = this.observers.indexOf(t);
    n !== -1 && this.observers.splice(n, 1);
  }
  notifyObservers(t) {
    this.observers.forEach((l) => {
      var a, r;
      switch (t) {
        case "columns":
          (a = l.state) == null || a.onColumnsChange(this);
          break;
        case "scrollable":
          (r = l.state) == null || r.onScrollableChange(this);
          break;
        default:
          throw new Error(`Table Layout don't have event ${t}.`);
      }
    });
  }
}
const { CheckboxGroup: zi } = Ze, Ki = ne({
  name: "ElTableFilterPanel",
  components: {
    ElCheckbox: Ze,
    ElCheckboxGroup: zi,
    ElScrollbar: Dl,
    ElTooltip: Wl,
    ElIcon: ct,
    ArrowDown: ma,
    ArrowUp: ga
  },
  directives: { ClickOutside: Ri },
  props: {
    placement: {
      type: String,
      default: "bottom-start"
    },
    store: {
      type: Object
    },
    column: {
      type: Object
    },
    upDataColumn: {
      type: Function
    },
    appendTo: ca.appendTo
  },
  setup(e) {
    const t = ie(), { t: n } = Vl(), l = ae("table-filter"), a = t == null ? void 0 : t.parent;
    a.filterPanels.value[e.column.id] || (a.filterPanels.value[e.column.id] = t);
    const r = R(!1), s = R(null), o = O(() => e.column && e.column.filters), i = O(() => e.column.filterClassName ? `${l.b()} ${e.column.filterClassName}` : l.b()), u = O({
      get: () => {
        var m;
        return (((m = e.column) == null ? void 0 : m.filteredValue) || [])[0];
      },
      set: (m) => {
        c.value && (Ue(m) ? c.value.splice(0, 1) : c.value.splice(0, 1, m));
      }
    }), c = O({
      get() {
        return e.column ? e.column.filteredValue || [] : [];
      },
      set(m) {
        e.column && e.upDataColumn("filteredValue", m);
      }
    }), h = O(() => e.column ? e.column.filterMultiple : !0), v = (m) => m.value === u.value, b = () => {
      r.value = !1;
    }, d = (m) => {
      m.stopPropagation(), r.value = !r.value;
    }, p = () => {
      r.value = !1;
    }, g = () => {
      S(c.value), b();
    }, C = () => {
      c.value = [], S(c.value), b();
    }, T = (m) => {
      u.value = m, Ue(m) ? S([]) : S(c.value), b();
    }, S = (m) => {
      e.store.commit("filterChange", {
        column: e.column,
        values: m
      }), e.store.updateAllSelected();
    };
    de(r, (m) => {
      e.column && e.upDataColumn("filterOpened", m);
    }, {
      immediate: !0
    });
    const f = O(() => {
      var m, w;
      return (w = (m = s.value) == null ? void 0 : m.popperRef) == null ? void 0 : w.contentRef;
    });
    return {
      tooltipVisible: r,
      multiple: h,
      filterClassName: i,
      filteredValue: c,
      filterValue: u,
      filters: o,
      handleConfirm: g,
      handleReset: C,
      handleSelect: T,
      isPropAbsent: Ue,
      isActive: v,
      t: n,
      ns: l,
      showFilterPanel: d,
      hideFilterPanel: p,
      popperPaneRef: f,
      tooltip: s
    };
  }
});
function ji(e, t, n, l, a, r) {
  const s = Ee("el-checkbox"), o = Ee("el-checkbox-group"), i = Ee("el-scrollbar"), u = Ee("arrow-up"), c = Ee("arrow-down"), h = Ee("el-icon"), v = Ee("el-tooltip"), b = jl("click-outside");
  return B(), we(v, {
    ref: "tooltip",
    visible: e.tooltipVisible,
    offset: 0,
    placement: e.placement,
    "show-arrow": !1,
    "stop-popper-mouse-event": !1,
    teleported: "",
    effect: "light",
    pure: "",
    "popper-class": e.filterClassName,
    persistent: "",
    "append-to": e.appendTo
  }, {
    content: Re(() => [
      e.multiple ? (B(), q("div", { key: 0 }, [
        ce("div", {
          class: _(e.ns.e("content"))
        }, [
          Me(i, {
            "wrap-class": e.ns.e("wrap")
          }, {
            default: Re(() => [
              Me(o, {
                modelValue: e.filteredValue,
                "onUpdate:modelValue": (d) => e.filteredValue = d,
                class: _(e.ns.e("checkbox-group"))
              }, {
                default: Re(() => [
                  (B(!0), q(vt, null, jn(e.filters, (d) => (B(), we(s, {
                    key: d.value,
                    value: d.value
                  }, {
                    default: Re(() => [
                      jt(We(d.text), 1)
                    ]),
                    _: 2
                  }, 1032, ["value"]))), 128))
                ]),
                _: 1
              }, 8, ["modelValue", "onUpdate:modelValue", "class"])
            ]),
            _: 1
          }, 8, ["wrap-class"])
        ], 2),
        ce("div", {
          class: _(e.ns.e("bottom"))
        }, [
          ce("button", {
            class: _({ [e.ns.is("disabled")]: e.filteredValue.length === 0 }),
            disabled: e.filteredValue.length === 0,
            type: "button",
            onClick: e.handleConfirm
          }, We(e.t("el.table.confirmFilter")), 11, ["disabled", "onClick"]),
          ce("button", {
            type: "button",
            onClick: e.handleReset
          }, We(e.t("el.table.resetFilter")), 9, ["onClick"])
        ], 2)
      ])) : (B(), q("ul", {
        key: 1,
        class: _(e.ns.e("list"))
      }, [
        ce("li", {
          class: _([
            e.ns.e("list-item"),
            {
              [e.ns.is("active")]: e.isPropAbsent(e.filterValue)
            }
          ]),
          onClick: (d) => e.handleSelect(null)
        }, We(e.t("el.table.clearFilter")), 11, ["onClick"]),
        (B(!0), q(vt, null, jn(e.filters, (d) => (B(), q("li", {
          key: d.value,
          class: _([e.ns.e("list-item"), e.ns.is("active", e.isActive(d))]),
          label: d.value,
          onClick: (p) => e.handleSelect(d.value)
        }, We(d.text), 11, ["label", "onClick"]))), 128))
      ], 2))
    ]),
    default: Re(() => [
      Ve((B(), q("span", {
        class: _([
          `${e.ns.namespace.value}-table__column-filter-trigger`,
          `${e.ns.namespace.value}-none-outline`
        ]),
        onClick: e.showFilterPanel
      }, [
        Me(h, null, {
          default: Re(() => [
            ge(e.$slots, "filter-icon", {}, () => [
              e.column.filterOpened ? (B(), we(u, { key: 0 })) : (B(), we(c, { key: 1 }))
            ])
          ]),
          _: 3
        })
      ], 10, ["onClick"])), [
        [b, e.hideFilterPanel, e.popperPaneRef]
      ])
    ]),
    _: 3
  }, 8, ["visible", "placement", "popper-class", "append-to"]);
}
var Gi = /* @__PURE__ */ Ke(Ki, [["render", ji], ["__file", "filter-panel.vue"]]);
function Pn(e) {
  const t = ie();
  Gl(() => {
    n.value.addObserver(t);
  }), gt(() => {
    l(n.value), a(n.value);
  }), va(() => {
    l(n.value), a(n.value);
  }), An(() => {
    n.value.removeObserver(t);
  });
  const n = O(() => {
    const r = e.layout;
    if (!r)
      throw new Error("Can not find table layout.");
    return r;
  }), l = (r) => {
    var s;
    const o = ((s = e.vnode.el) == null ? void 0 : s.querySelectorAll("colgroup > col")) || [];
    if (!o.length)
      return;
    const i = r.getFlattenColumns(), u = {};
    i.forEach((c) => {
      u[c.id] = c;
    });
    for (let c = 0, h = o.length; c < h; c++) {
      const v = o[c], b = v.getAttribute("name"), d = u[b];
      d && v.setAttribute("width", d.realWidth || d.width);
    }
  }, a = (r) => {
    var s, o;
    const i = ((s = e.vnode.el) == null ? void 0 : s.querySelectorAll("colgroup > col[name=gutter]")) || [];
    for (let c = 0, h = i.length; c < h; c++)
      i[c].setAttribute("width", r.scrollY.value ? r.gutterWidth : "0");
    const u = ((o = e.vnode.el) == null ? void 0 : o.querySelectorAll("th.gutter")) || [];
    for (let c = 0, h = u.length; c < h; c++) {
      const v = u[c];
      v.style.width = r.scrollY.value ? `${r.gutterWidth}px` : "0", v.style.display = r.scrollY.value ? "" : "none";
    }
  };
  return {
    tableLayout: n.value,
    onColumnsChange: l,
    onScrollableChange: a
  };
}
const Le = Symbol("ElTable");
function Ui(e, t) {
  const n = ie(), l = se(Le), a = (p) => {
    p.stopPropagation();
  }, r = (p, g) => {
    !g.filters && g.sortable ? d(p, g, !1) : g.filterable && !g.sortable && a(p), l == null || l.emit("header-click", g, p);
  }, s = (p, g) => {
    l == null || l.emit("header-contextmenu", g, p);
  }, o = R(null), i = R(!1), u = R({}), c = (p, g) => {
    if (qe && !(g.children && g.children.length > 0) && o.value && e.border) {
      i.value = !0;
      const C = l;
      t("set-drag-visible", !0);
      const S = (C == null ? void 0 : C.vnode.el).getBoundingClientRect().left, f = n.vnode.el.querySelector(`th.${g.id}`), m = f.getBoundingClientRect(), w = m.left - S + 30;
      Nt(f, "noclick"), u.value = {
        startMouseLeft: p.clientX,
        startLeft: m.right - S,
        startColumnLeft: m.left - S,
        tableLeft: S
      };
      const y = C == null ? void 0 : C.refs.resizeProxy;
      y.style.left = `${u.value.startLeft}px`, document.onselectstart = function() {
        return !1;
      }, document.ondragstart = function() {
        return !1;
      };
      const E = (F) => {
        const V = F.clientX - u.value.startMouseLeft, $ = u.value.startLeft + V;
        y.style.left = `${Math.max(w, $)}px`;
      }, k = () => {
        if (i.value) {
          const { startColumnLeft: F, startLeft: V } = u.value, z = Number.parseInt(y.style.left, 10) - F;
          g.width = g.realWidth = z, C == null || C.emit("header-dragend", g.width, V - F, g, p), requestAnimationFrame(() => {
            e.store.scheduleLayout(!1, !0);
          }), document.body.style.cursor = "", i.value = !1, o.value = null, u.value = {}, t("set-drag-visible", !1);
        }
        document.removeEventListener("mousemove", E), document.removeEventListener("mouseup", k), document.onselectstart = null, document.ondragstart = null, setTimeout(() => {
          ht(f, "noclick");
        }, 0);
      };
      document.addEventListener("mousemove", E), document.addEventListener("mouseup", k);
    }
  }, h = (p, g) => {
    var C;
    if (g.children && g.children.length > 0)
      return;
    const T = p.target;
    if (!Bl(T))
      return;
    const S = T == null ? void 0 : T.closest("th");
    if (!(!g || !g.resizable || !S) && !i.value && e.border) {
      const f = S.getBoundingClientRect(), m = document.body.style, w = ((C = S.parentNode) == null ? void 0 : C.lastElementChild) === S, y = e.allowDragLastColumn || !w;
      f.width > 12 && f.right - p.clientX < 8 && y ? (m.cursor = "col-resize", rt(S, "is-sortable") && (S.style.cursor = "col-resize"), o.value = g) : i.value || (m.cursor = "", rt(S, "is-sortable") && (S.style.cursor = "pointer"), o.value = null);
    }
  }, v = () => {
    qe && (document.body.style.cursor = "");
  }, b = ({ order: p, sortOrders: g }) => {
    if (p === "")
      return g[0];
    const C = g.indexOf(p || null);
    return g[C > g.length - 2 ? 0 : C + 1];
  }, d = (p, g, C) => {
    var T;
    p.stopPropagation();
    const S = g.order === C ? null : C || b(g), f = (T = p.target) == null ? void 0 : T.closest("th");
    if (f && rt(f, "noclick")) {
      ht(f, "noclick");
      return;
    }
    if (!g.sortable)
      return;
    const m = p.currentTarget;
    if (["ascending", "descending"].some((F) => rt(m, F) && !g.sortOrders.includes(F)))
      return;
    const w = e.store.states;
    let y = w.sortProp.value, E;
    const k = w.sortingColumn.value;
    (k !== g || k === g && mt(k.order)) && (k && (k.order = null), w.sortingColumn.value = g, y = g.property), S ? E = g.order = S : E = g.order = null, w.sortProp.value = y, w.sortOrder.value = E, l == null || l.store.commit("changeSortCondition");
  };
  return {
    handleHeaderClick: r,
    handleHeaderContextMenu: s,
    handleMouseDown: c,
    handleMouseMove: h,
    handleMouseOut: v,
    handleSortClick: d,
    handleFilterClick: a
  };
}
function qi(e) {
  const t = se(Le), n = ae("table");
  return {
    getHeaderRowStyle: (o) => {
      const i = t == null ? void 0 : t.props.headerRowStyle;
      return Te(i) ? i.call(null, { rowIndex: o }) : i;
    },
    getHeaderRowClass: (o) => {
      const i = [], u = t == null ? void 0 : t.props.headerRowClassName;
      return me(u) ? i.push(u) : Te(u) && i.push(u.call(null, { rowIndex: o })), i.join(" ");
    },
    getHeaderCellStyle: (o, i, u, c) => {
      var h;
      let v = (h = t == null ? void 0 : t.props.headerCellStyle) != null ? h : {};
      Te(v) && (v = v.call(null, {
        rowIndex: o,
        columnIndex: i,
        row: u,
        column: c
      }));
      const b = $n(i, c.fixed, e.store, u);
      return Je(b, "left"), Je(b, "right"), Object.assign({}, v, b);
    },
    getHeaderCellClass: (o, i, u, c) => {
      const h = Fn(n.b(), i, c.fixed, e.store, u), v = [
        c.id,
        c.order,
        c.headerAlign,
        c.className,
        c.labelClassName,
        ...h
      ];
      c.children || v.push("is-leaf"), c.sortable && v.push("is-sortable");
      const b = t == null ? void 0 : t.props.headerCellClassName;
      return me(b) ? v.push(b) : Te(b) && v.push(b.call(null, {
        rowIndex: o,
        columnIndex: i,
        row: u,
        column: c
      })), v.push(n.e("cell")), v.filter((d) => !!d).join(" ");
    }
  };
}
const Er = (e) => {
  const t = [];
  return e.forEach((n) => {
    n.children ? (t.push(n), t.push.apply(t, Er(n.children))) : t.push(n);
  }), t;
}, Rr = (e) => {
  let t = 1;
  const n = (r, s) => {
    if (s && (r.level = s.level + 1, t < r.level && (t = r.level)), r.children) {
      let o = 0;
      r.children.forEach((i) => {
        n(i, r), o += i.colSpan;
      }), r.colSpan = o;
    } else
      r.colSpan = 1;
  };
  e.forEach((r) => {
    r.level = 1, n(r, void 0);
  });
  const l = [];
  for (let r = 0; r < t; r++)
    l.push([]);
  return Er(e).forEach((r) => {
    r.children ? (r.rowSpan = 1, r.children.forEach((s) => s.isSubColumn = !0)) : r.rowSpan = t - r.level + 1, l[r.level - 1].push(r);
  }), l;
};
function Yi(e) {
  const t = se(Le), n = O(() => Rr(e.store.states.originColumns.value));
  return {
    isGroup: O(() => {
      const r = n.value.length > 1;
      return r && t && (t.state.isGroup.value = !0), r;
    }),
    toggleAllSelection: (r) => {
      r.stopPropagation(), t == null || t.store.commit("toggleAllSelection");
    },
    columnRows: n
  };
}
var Xi = ne({
  name: "ElTableHeader",
  components: {
    ElCheckbox: Ze
  },
  props: {
    fixed: {
      type: String,
      default: ""
    },
    store: {
      required: !0,
      type: Object
    },
    border: Boolean,
    defaultSort: {
      type: Object,
      default: () => ({
        prop: "",
        order: ""
      })
    },
    appendFilterPanelTo: {
      type: String
    },
    allowDragLastColumn: {
      type: Boolean
    }
  },
  setup(e, { emit: t }) {
    const n = ie(), l = se(Le), a = ae("table"), r = R({}), { onColumnsChange: s, onScrollableChange: o } = Pn(l), i = (l == null ? void 0 : l.props.tableLayout) === "auto", u = zl(/* @__PURE__ */ new Map()), c = R(), h = () => {
      setTimeout(() => {
        u.size > 0 && (u.forEach((F, V) => {
          const $ = c.value.querySelector(`.${V.replace(/\s/g, ".")}`);
          if ($) {
            const z = $.getBoundingClientRect().width;
            F.width = z;
          }
        }), u.clear());
      });
    };
    de(u, h), gt(async () => {
      await ze(), await ze();
      const { prop: F, order: V } = e.defaultSort;
      l == null || l.store.commit("sort", { prop: F, order: V, init: !0 }), h();
    });
    const {
      handleHeaderClick: v,
      handleHeaderContextMenu: b,
      handleMouseDown: d,
      handleMouseMove: p,
      handleMouseOut: g,
      handleSortClick: C,
      handleFilterClick: T
    } = Ui(e, t), {
      getHeaderRowStyle: S,
      getHeaderRowClass: f,
      getHeaderCellStyle: m,
      getHeaderCellClass: w
    } = qi(e), { isGroup: y, toggleAllSelection: E, columnRows: k } = Yi(e);
    return n.state = {
      onColumnsChange: s,
      onScrollableChange: o
    }, n.filterPanels = r, {
      ns: a,
      filterPanels: r,
      onColumnsChange: s,
      onScrollableChange: o,
      columnRows: k,
      getHeaderRowClass: f,
      getHeaderRowStyle: S,
      getHeaderCellClass: w,
      getHeaderCellStyle: m,
      handleHeaderClick: v,
      handleHeaderContextMenu: b,
      handleMouseDown: d,
      handleMouseMove: p,
      handleMouseOut: g,
      handleSortClick: C,
      handleFilterClick: T,
      isGroup: y,
      toggleAllSelection: E,
      saveIndexSelection: u,
      isTableLayoutAuto: i,
      theadRef: c,
      updateFixedColumnStyle: h
    };
  },
  render() {
    const {
      ns: e,
      isGroup: t,
      columnRows: n,
      getHeaderCellStyle: l,
      getHeaderCellClass: a,
      getHeaderRowClass: r,
      getHeaderRowStyle: s,
      handleHeaderClick: o,
      handleHeaderContextMenu: i,
      handleMouseDown: u,
      handleMouseMove: c,
      handleSortClick: h,
      handleMouseOut: v,
      store: b,
      $parent: d,
      saveIndexSelection: p,
      isTableLayoutAuto: g
    } = this;
    let C = 1;
    return W("thead", {
      ref: "theadRef",
      class: { [e.is("group")]: t }
    }, n.map((T, S) => W("tr", {
      class: r(S),
      key: S,
      style: s(S)
    }, T.map((f, m) => {
      f.rowSpan > C && (C = f.rowSpan);
      const w = a(S, m, T, f);
      return g && f.fixed && p.set(w, f), W("th", {
        class: w,
        colspan: f.colSpan,
        key: `${f.id}-thead`,
        rowspan: f.rowSpan,
        style: l(S, m, T, f),
        onClick: (y) => {
          y.currentTarget.classList.contains("noclick") || o(y, f);
        },
        onContextmenu: (y) => i(y, f),
        onMousedown: (y) => u(y, f),
        onMousemove: (y) => c(y, f),
        onMouseout: v
      }, [
        W("div", {
          class: [
            "cell",
            f.filteredValue && f.filteredValue.length > 0 ? "highlight" : ""
          ]
        }, [
          f.renderHeader ? f.renderHeader({
            column: f,
            $index: m,
            store: b,
            _self: d
          }) : f.label,
          f.sortable && W("span", {
            onClick: (y) => h(y, f),
            class: "caret-wrapper"
          }, [
            W("i", {
              onClick: (y) => h(y, f, "ascending"),
              class: "sort-caret ascending"
            }),
            W("i", {
              onClick: (y) => h(y, f, "descending"),
              class: "sort-caret descending"
            })
          ]),
          f.filterable && W(Gi, {
            store: b,
            placement: f.filterPlacement || "bottom-start",
            appendTo: d.appendFilterPanelTo,
            column: f,
            upDataColumn: (y, E) => {
              f[y] = E;
            }
          }, {
            "filter-icon": () => f.renderFilterIcon ? f.renderFilterIcon({
              filterOpened: f.filterOpened
            }) : null
          })
        ])
      ]);
    }))));
  }
});
function Jt(e, t, n = 0.03) {
  return e - t > n;
}
function Qi(e) {
  const t = se(Le), n = R(""), l = R(W("div")), a = (d, p, g) => {
    var C;
    const T = t, S = Zt(d);
    let f;
    const m = (C = T == null ? void 0 : T.vnode.el) == null ? void 0 : C.dataset.prefix;
    S && (f = ml({
      columns: e.store.states.columns.value
    }, S, m), f && (T == null || T.emit(`cell-${g}`, p, f, S, d))), T == null || T.emit(`row-${g}`, p, f, d);
  }, r = (d, p) => {
    a(d, p, "dblclick");
  }, s = (d, p) => {
    e.store.commit("setCurrentRow", p), a(d, p, "click");
  }, o = (d, p) => {
    a(d, p, "contextmenu");
  }, i = Ot((d) => {
    e.store.commit("setHoverRow", d);
  }, 30), u = Ot(() => {
    e.store.commit("setHoverRow", null);
  }, 30), c = (d) => {
    const p = window.getComputedStyle(d, null), g = Number.parseInt(p.paddingLeft, 10) || 0, C = Number.parseInt(p.paddingRight, 10) || 0, T = Number.parseInt(p.paddingTop, 10) || 0, S = Number.parseInt(p.paddingBottom, 10) || 0;
    return {
      left: g,
      right: C,
      top: T,
      bottom: S
    };
  }, h = (d, p, g) => {
    let C = p.target.parentNode;
    for (; d > 1 && (C = C == null ? void 0 : C.nextSibling, !(!C || C.nodeName !== "TR")); )
      g(C, "hover-row hover-fixed-row"), d--;
  };
  return {
    handleDoubleClick: r,
    handleClick: s,
    handleContextMenu: o,
    handleMouseEnter: i,
    handleMouseLeave: u,
    handleCellMouseEnter: (d, p, g) => {
      var C, T, S;
      const f = t, m = Zt(d), w = (C = f == null ? void 0 : f.vnode.el) == null ? void 0 : C.dataset.prefix;
      let y;
      if (m) {
        y = ml({
          columns: e.store.states.columns.value
        }, m, w), m.rowSpan > 1 && h(m.rowSpan, d, Nt);
        const N = f.hoverState = { cell: m, column: y, row: p };
        f == null || f.emit("cell-mouse-enter", N.row, N.column, N.cell, d);
      }
      if (!g)
        return;
      const E = d.target.querySelector(".cell");
      if (!(rt(E, `${w}-tooltip`) && E.childNodes.length))
        return;
      const k = document.createRange();
      k.setStart(E, 0), k.setEnd(E, E.childNodes.length);
      const { width: F, height: V } = k.getBoundingClientRect(), { width: $, height: z } = E.getBoundingClientRect(), { top: Q, left: D, right: Y, bottom: Z } = c(E), M = D + Y, x = Q + Z;
      Jt(F + M, $) || Jt(V + x, z) || Jt(E.scrollWidth, $) ? Fi(g, m.innerText || m.textContent, p, y, m, f) : ((T = pe) == null ? void 0 : T.trigger) === m && ((S = pe) == null || S());
    },
    handleCellMouseLeave: (d) => {
      const p = Zt(d);
      if (!p)
        return;
      p.rowSpan > 1 && h(p.rowSpan, d, ht);
      const g = t == null ? void 0 : t.hoverState;
      t == null || t.emit("cell-mouse-leave", g == null ? void 0 : g.row, g == null ? void 0 : g.column, g == null ? void 0 : g.cell, d);
    },
    tooltipContent: n,
    tooltipTrigger: l
  };
}
function Zi(e) {
  const t = se(Le), n = ae("table");
  return {
    getRowStyle: (u, c) => {
      const h = t == null ? void 0 : t.props.rowStyle;
      return Te(h) ? h.call(null, {
        row: u,
        rowIndex: c
      }) : h || null;
    },
    getRowClass: (u, c) => {
      const h = [n.e("row")];
      t != null && t.props.highlightCurrentRow && u === e.store.states.currentRow.value && h.push("current-row"), e.stripe && c % 2 === 1 && h.push(n.em("row", "striped"));
      const v = t == null ? void 0 : t.props.rowClassName;
      return me(v) ? h.push(v) : Te(v) && h.push(v.call(null, {
        row: u,
        rowIndex: c
      })), h;
    },
    getCellStyle: (u, c, h, v) => {
      const b = t == null ? void 0 : t.props.cellStyle;
      let d = b ?? {};
      Te(b) && (d = b.call(null, {
        rowIndex: u,
        columnIndex: c,
        row: h,
        column: v
      }));
      const p = $n(c, e == null ? void 0 : e.fixed, e.store);
      return Je(p, "left"), Je(p, "right"), Object.assign({}, d, p);
    },
    getCellClass: (u, c, h, v, b) => {
      const d = Fn(n.b(), c, e == null ? void 0 : e.fixed, e.store, void 0, b), p = [v.id, v.align, v.className, ...d], g = t == null ? void 0 : t.props.cellClassName;
      return me(g) ? p.push(g) : Te(g) && p.push(g.call(null, {
        rowIndex: u,
        columnIndex: c,
        row: h,
        column: v
      })), p.push(n.e("cell")), p.filter((C) => !!C).join(" ");
    },
    getSpan: (u, c, h, v) => {
      let b = 1, d = 1;
      const p = t == null ? void 0 : t.props.spanMethod;
      if (Te(p)) {
        const g = p({
          row: u,
          column: c,
          rowIndex: h,
          columnIndex: v
        });
        J(g) ? (b = g[0], d = g[1]) : dt(g) && (b = g.rowspan, d = g.colspan);
      }
      return { rowspan: b, colspan: d };
    },
    getColspanRealWidth: (u, c, h) => {
      if (c < 1)
        return u[h].realWidth;
      const v = u.map(({ realWidth: b, width: d }) => b || d).slice(h, h + c);
      return Number(v.reduce((b, d) => Number(b) + Number(d), -1));
    }
  };
}
const Ji = ne({
  name: "TableTdWrapper"
}), eu = /* @__PURE__ */ ne({
  ...Ji,
  props: {
    colspan: {
      type: Number,
      default: 1
    },
    rowspan: {
      type: Number,
      default: 1
    }
  },
  setup(e) {
    return (t, n) => (B(), q("td", {
      colspan: e.colspan,
      rowspan: e.rowspan
    }, [
      ge(t.$slots, "default")
    ], 8, ["colspan", "rowspan"]));
  }
});
var tu = /* @__PURE__ */ Ke(eu, [["__file", "td-wrapper.vue"]]);
function nu(e) {
  const t = se(Le), n = ae("table"), {
    handleDoubleClick: l,
    handleClick: a,
    handleContextMenu: r,
    handleMouseEnter: s,
    handleMouseLeave: o,
    handleCellMouseEnter: i,
    handleCellMouseLeave: u,
    tooltipContent: c,
    tooltipTrigger: h
  } = Qi(e), {
    getRowStyle: v,
    getRowClass: b,
    getCellStyle: d,
    getCellClass: p,
    getSpan: g,
    getColspanRealWidth: C
  } = Zi(e), T = O(() => e.store.states.columns.value.findIndex(({ type: y }) => y === "default")), S = (y, E) => {
    const k = t.props.rowKey;
    return k ? oe(y, k) : E;
  }, f = (y, E, k, F = !1) => {
    const { tooltipEffect: V, tooltipOptions: $, store: z } = e, { indent: Q, columns: D } = z.states, Y = b(y, E);
    let Z = !0;
    return k && (Y.push(n.em("row", `level-${k.level}`)), Z = k.display), W("tr", {
      style: [Z ? null : { display: "none" }, v(y, E)],
      class: Y,
      key: S(y, E),
      onDblclick: (x) => l(x, y),
      onClick: (x) => a(x, y),
      onContextmenu: (x) => r(x, y),
      onMouseenter: () => s(E),
      onMouseleave: o
    }, D.value.map((x, N) => {
      const { rowspan: G, colspan: ee } = g(y, x, E, N);
      if (!G || !ee)
        return null;
      const te = Object.assign({}, x);
      te.realWidth = C(D.value, ee, N);
      const ue = {
        store: e.store,
        _self: e.context || t,
        column: te,
        row: y,
        $index: E,
        cellIndex: N,
        expanded: F
      };
      N === T.value && k && (ue.treeNode = {
        indent: k.level * Q.value,
        level: k.level
      }, Oe(k.expanded) && (ue.treeNode.expanded = k.expanded, "loading" in k && (ue.treeNode.loading = k.loading), "noLazyChildren" in k && (ue.treeNode.noLazyChildren = k.noLazyChildren)));
      const fe = `${S(y, E)},${N}`, Se = te.columnKey || te.rawColumnKey || "", le = x.showOverflowTooltip && ur({
        effect: V
      }, $, x.showOverflowTooltip);
      return W(tu, {
        style: d(E, N, y, x),
        class: p(E, N, y, x, ee - 1),
        key: `${Se}${fe}`,
        rowspan: G,
        colspan: ee,
        onMouseenter: (be) => i(be, y, le),
        onMouseleave: u
      }, {
        default: () => m(N, x, ue)
      });
    }));
  }, m = (y, E, k) => E.renderCell(k);
  return {
    wrappedRowRender: (y, E) => {
      const k = e.store, { isRowExpanded: F, assertRowKey: V } = k, { treeData: $, lazyTreeNodeMap: z, childrenColumnName: Q, rowKey: D } = k.states, Y = k.states.columns.value;
      if (Y.some(({ type: M }) => M === "expand")) {
        const M = F(y), x = f(y, E, void 0, M), N = t.renderExpanded;
        if (!N)
          return console.error("[Element Error]renderExpanded is required."), x;
        const G = [[x]];
        return (t.props.preserveExpandedContent || M) && G[0].push(W("tr", {
          key: `expanded-row__${x.key}`,
          style: { display: M ? "" : "none" }
        }, [
          W("td", {
            colspan: Y.length,
            class: `${n.e("cell")} ${n.e("expanded-cell")}`
          }, [N({ row: y, $index: E, store: k, expanded: M })])
        ])), G;
      } else if (Object.keys($.value).length) {
        V();
        const M = oe(y, D.value);
        let x = $.value[M], N = null;
        x && (N = {
          expanded: x.expanded,
          level: x.level,
          display: !0
        }, Oe(x.lazy) && (Oe(x.loaded) && x.loaded && (N.noLazyChildren = !(x.children && x.children.length)), N.loading = x.loading));
        const G = [f(y, E, N)];
        if (x) {
          let ee = 0;
          const te = (fe, Se) => {
            fe && fe.length && Se && fe.forEach((le) => {
              const be = {
                display: Se.display && Se.expanded,
                level: Se.level + 1,
                expanded: !1,
                noLazyChildren: !1,
                loading: !1
              }, ye = oe(le, D.value);
              if (Ue(ye))
                throw new Error("For nested data item, row-key is required.");
              if (x = { ...$.value[ye] }, x && (be.expanded = x.expanded, x.level = x.level || be.level, x.display = !!(x.expanded && be.display), Oe(x.lazy) && (Oe(x.loaded) && x.loaded && (be.noLazyChildren = !(x.children && x.children.length)), be.loading = x.loading)), ee++, G.push(f(le, E + ee, be)), x) {
                const bt = z.value[ye] || le[Q.value];
                te(bt, x);
              }
            });
          };
          x.display = !0;
          const ue = z.value[M] || y[Q.value];
          te(ue, x);
        }
        return G;
      } else
        return f(y, E, void 0);
    },
    tooltipContent: c,
    tooltipTrigger: h
  };
}
const lu = {
  store: {
    required: !0,
    type: Object
  },
  stripe: Boolean,
  tooltipEffect: String,
  tooltipOptions: {
    type: Object
  },
  context: {
    default: () => ({}),
    type: Object
  },
  rowClassName: [String, Function],
  rowStyle: [Object, Function],
  fixed: {
    type: String,
    default: ""
  },
  highlight: Boolean
};
var ru = ne({
  name: "ElTableBody",
  props: lu,
  setup(e) {
    const t = ie(), n = se(Le), l = ae("table"), { wrappedRowRender: a, tooltipContent: r, tooltipTrigger: s } = nu(e), { onColumnsChange: o, onScrollableChange: i } = Pn(n), u = [];
    return de(e.store.states.hoverRow, (c, h) => {
      var v;
      const b = t == null ? void 0 : t.vnode.el, d = Array.from((b == null ? void 0 : b.children) || []).filter((C) => C == null ? void 0 : C.classList.contains(`${l.e("row")}`));
      let p = c;
      const g = (v = d[p]) == null ? void 0 : v.childNodes;
      if (g != null && g.length) {
        let C = 0;
        Array.from(g).reduce((S, f, m) => {
          var w, y;
          return ((w = g[m]) == null ? void 0 : w.colSpan) > 1 && (C = (y = g[m]) == null ? void 0 : y.colSpan), f.nodeName !== "TD" && C === 0 && S.push(m), C > 0 && C--, S;
        }, []).forEach((S) => {
          var f;
          for (p = c; p > 0; ) {
            const m = (f = d[p - 1]) == null ? void 0 : f.childNodes;
            if (m[S] && m[S].nodeName === "TD" && m[S].rowSpan > 1) {
              Nt(m[S], "hover-cell"), u.push(m[S]);
              break;
            }
            p--;
          }
        });
      } else
        u.forEach((C) => ht(C, "hover-cell")), u.length = 0;
      !e.store.states.isComplex.value || !qe || Ds(() => {
        const C = d[h], T = d[c];
        C && !C.classList.contains("hover-fixed-row") && ht(C, "hover-row"), T && Nt(T, "hover-row");
      });
    }), An(() => {
      var c;
      (c = pe) == null || c();
    }), {
      ns: l,
      onColumnsChange: o,
      onScrollableChange: i,
      wrappedRowRender: a,
      tooltipContent: r,
      tooltipTrigger: s
    };
  },
  render() {
    const { wrappedRowRender: e, store: t } = this, n = t.states.data.value || [];
    return W("tbody", { tabIndex: -1 }, [
      n.reduce((l, a) => l.concat(e(a, l.length)), [])
    ]);
  }
});
function au() {
  const e = se(Le), t = e == null ? void 0 : e.store, n = O(() => t.states.fixedLeafColumnsLength.value), l = O(() => t.states.rightFixedColumns.value.length), a = O(() => t.states.columns.value.length), r = O(() => t.states.fixedColumns.value.length), s = O(() => t.states.rightFixedColumns.value.length);
  return {
    leftFixedLeafCount: n,
    rightFixedLeafCount: l,
    columnsCount: a,
    leftFixedCount: r,
    rightFixedCount: s,
    columns: t.states.columns
  };
}
function ou(e) {
  const { columns: t } = au(), n = ae("table");
  return {
    getCellClasses: (r, s) => {
      const o = r[s], i = [
        n.e("cell"),
        o.id,
        o.align,
        o.labelClassName,
        ...Fn(n.b(), s, o.fixed, e.store)
      ];
      return o.className && i.push(o.className), o.children || i.push(n.is("leaf")), i;
    },
    getCellStyles: (r, s) => {
      const o = $n(s, r.fixed, e.store);
      return Je(o, "left"), Je(o, "right"), o;
    },
    columns: t
  };
}
var su = ne({
  name: "ElTableFooter",
  props: {
    fixed: {
      type: String,
      default: ""
    },
    store: {
      required: !0,
      type: Object
    },
    summaryMethod: Function,
    sumText: String,
    border: Boolean,
    defaultSort: {
      type: Object,
      default: () => ({
        prop: "",
        order: ""
      })
    }
  },
  setup(e) {
    const t = se(Le), n = ae("table"), { getCellClasses: l, getCellStyles: a, columns: r } = ou(e), { onScrollableChange: s, onColumnsChange: o } = Pn(t);
    return {
      ns: n,
      onScrollableChange: s,
      onColumnsChange: o,
      getCellClasses: l,
      getCellStyles: a,
      columns: r
    };
  },
  render() {
    const { columns: e, getCellStyles: t, getCellClasses: n, summaryMethod: l, sumText: a } = this, r = this.store.states.data.value;
    let s = [];
    return l ? s = l({
      columns: e,
      data: r
    }) : e.forEach((o, i) => {
      if (i === 0) {
        s[i] = a;
        return;
      }
      const u = r.map((b) => Number(b[o.property])), c = [];
      let h = !0;
      u.forEach((b) => {
        if (!Number.isNaN(+b)) {
          h = !1;
          const d = `${b}`.split(".")[1];
          c.push(d ? d.length : 0);
        }
      });
      const v = Math.max.apply(null, c);
      h ? s[i] = "" : s[i] = u.reduce((b, d) => {
        const p = Number(d);
        return Number.isNaN(+p) ? b : Number.parseFloat((b + d).toFixed(Math.min(v, 20)));
      }, 0);
    }), W(W("tfoot", [
      W("tr", {}, [
        ...e.map((o, i) => W("td", {
          key: i,
          colspan: o.colSpan,
          rowspan: o.rowSpan,
          class: n(e, i),
          style: t(o, i)
        }, [
          W("div", {
            class: ["cell", o.labelClassName]
          }, [s[i]])
        ]))
      ])
    ]));
  }
});
function iu(e) {
  return {
    setCurrentRow: (h) => {
      e.commit("setCurrentRow", h);
    },
    getSelectionRows: () => e.getSelectionRows(),
    toggleRowSelection: (h, v, b = !0) => {
      e.toggleRowSelection(h, v, !1, b), e.updateAllSelected();
    },
    clearSelection: () => {
      e.clearSelection();
    },
    clearFilter: (h) => {
      e.clearFilter(h);
    },
    toggleAllSelection: () => {
      e.commit("toggleAllSelection");
    },
    toggleRowExpansion: (h, v) => {
      e.toggleRowExpansionAdapter(h, v);
    },
    clearSort: () => {
      e.clearSort();
    },
    sort: (h, v) => {
      e.commit("sort", { prop: h, order: v });
    },
    updateKeyChildren: (h, v) => {
      e.updateKeyChildren(h, v);
    }
  };
}
function uu(e, t, n, l) {
  const a = R(!1), r = R(null), s = R(!1), o = (M) => {
    s.value = M;
  }, i = R({
    width: null,
    height: null,
    headerHeight: null
  }), u = R(!1), c = {
    display: "inline-block",
    verticalAlign: "middle"
  }, h = R(), v = R(0), b = R(0), d = R(0), p = R(0), g = R(0);
  ot(() => {
    t.setHeight(e.height);
  }), ot(() => {
    t.setMaxHeight(e.maxHeight);
  }), de(() => [e.currentRowKey, n.states.rowKey], ([M, x]) => {
    !A(x) || !A(M) || n.setCurrentRowKey(`${M}`);
  }, {
    immediate: !0
  }), de(() => e.data, (M) => {
    l.store.commit("setData", M);
  }, {
    immediate: !0,
    deep: !0
  }), ot(() => {
    e.expandRowKeys && n.setExpandRowKeysAdapter(e.expandRowKeys);
  });
  const C = () => {
    l.store.commit("setHoverRow", null), l.hoverState && (l.hoverState = null);
  }, T = (M, x) => {
    const { pixelX: N, pixelY: G } = x;
    Math.abs(N) >= Math.abs(G) && (l.refs.bodyWrapper.scrollLeft += x.pixelX / 5);
  }, S = O(() => e.height || e.maxHeight || n.states.fixedColumns.value.length > 0 || n.states.rightFixedColumns.value.length > 0), f = O(() => ({
    width: t.bodyWidth.value ? `${t.bodyWidth.value}px` : ""
  })), m = () => {
    S.value && t.updateElsHeight(), t.updateColumnsWidth(), !(typeof window > "u") && requestAnimationFrame(k);
  };
  gt(async () => {
    await ze(), n.updateColumns(), F(), requestAnimationFrame(m);
    const M = l.vnode.el, x = l.refs.headerWrapper;
    e.flexible && M && M.parentElement && (M.parentElement.style.minWidth = "0"), i.value = {
      width: h.value = M.offsetWidth,
      height: M.offsetHeight,
      headerHeight: e.showHeader && x ? x.offsetHeight : null
    }, n.states.columns.value.forEach((N) => {
      N.filteredValue && N.filteredValue.length && l.store.commit("filterChange", {
        column: N,
        values: N.filteredValue,
        silent: !0
      });
    }), l.$ready = !0;
  });
  const w = (M, x) => {
    if (!M)
      return;
    const N = Array.from(M.classList).filter((G) => !G.startsWith("is-scrolling-"));
    N.push(t.scrollX.value ? x : "is-scrolling-none"), M.className = N.join(" ");
  }, y = (M) => {
    const { tableWrapper: x } = l.refs;
    w(x, M);
  }, E = (M) => {
    const { tableWrapper: x } = l.refs;
    return !!(x && x.classList.contains(M));
  }, k = function() {
    if (!l.refs.scrollBarRef)
      return;
    if (!t.scrollX.value) {
      const fe = "is-scrolling-none";
      E(fe) || y(fe);
      return;
    }
    const M = l.refs.scrollBarRef.wrapRef;
    if (!M)
      return;
    const { scrollLeft: x, offsetWidth: N, scrollWidth: G } = M, { headerWrapper: ee, footerWrapper: te } = l.refs;
    ee && (ee.scrollLeft = x), te && (te.scrollLeft = x);
    const ue = G - N - 1;
    x >= ue ? y("is-scrolling-right") : y(x === 0 ? "is-scrolling-left" : "is-scrolling-middle");
  }, F = () => {
    l.refs.scrollBarRef && (l.refs.scrollBarRef.wrapRef && In(l.refs.scrollBarRef.wrapRef, "scroll", k, {
      passive: !0
    }), e.fit ? Dn(l.vnode.el, V) : In(window, "resize", V), Dn(l.refs.bodyWrapper, () => {
      var M, x;
      V(), (x = (M = l.refs) == null ? void 0 : M.scrollBarRef) == null || x.update();
    }));
  }, V = () => {
    var M, x, N, G;
    const ee = l.vnode.el;
    if (!l.$ready || !ee)
      return;
    let te = !1;
    const {
      width: ue,
      height: fe,
      headerHeight: Se
    } = i.value, le = h.value = ee.offsetWidth;
    ue !== le && (te = !0);
    const be = ee.offsetHeight;
    (e.height || S.value) && fe !== be && (te = !0);
    const ye = e.tableLayout === "fixed" ? l.refs.headerWrapper : (M = l.refs.tableHeaderRef) == null ? void 0 : M.$el;
    e.showHeader && (ye == null ? void 0 : ye.offsetHeight) !== Se && (te = !0), v.value = ((x = l.refs.tableWrapper) == null ? void 0 : x.scrollHeight) || 0, d.value = (ye == null ? void 0 : ye.scrollHeight) || 0, p.value = ((N = l.refs.footerWrapper) == null ? void 0 : N.offsetHeight) || 0, g.value = ((G = l.refs.appendWrapper) == null ? void 0 : G.offsetHeight) || 0, b.value = v.value - d.value - p.value - g.value, te && (i.value = {
      width: le,
      height: be,
      headerHeight: e.showHeader && (ye == null ? void 0 : ye.offsetHeight) || 0
    }, m());
  }, $ = Lt(), z = O(() => {
    const { bodyWidth: M, scrollY: x, gutterWidth: N } = t;
    return M.value ? `${M.value - (x.value ? N : 0)}px` : "";
  }), Q = O(() => e.maxHeight ? "fixed" : e.tableLayout), D = O(() => {
    if (e.data && e.data.length)
      return null;
    let M = "100%";
    e.height && b.value && (M = `${b.value}px`);
    const x = h.value;
    return {
      width: x ? `${x}px` : "",
      height: M
    };
  }), Y = O(() => e.height ? {
    height: "100%"
  } : e.maxHeight ? Number.isNaN(Number(e.maxHeight)) ? {
    maxHeight: `calc(${e.maxHeight} - ${d.value + p.value}px)`
  } : {
    maxHeight: `${e.maxHeight - d.value - p.value}px`
  } : {});
  return {
    isHidden: a,
    renderExpanded: r,
    setDragVisible: o,
    isGroup: u,
    handleMouseLeave: C,
    handleHeaderFooterMousewheel: T,
    tableSize: $,
    emptyBlockStyle: D,
    handleFixedMousewheel: (M, x) => {
      const N = l.refs.bodyWrapper;
      if (Math.abs(x.spinY) > 0) {
        const G = N.scrollTop;
        x.pixelY < 0 && G !== 0 && M.preventDefault(), x.pixelY > 0 && N.scrollHeight - N.clientHeight > G && M.preventDefault(), N.scrollTop += Math.ceil(x.pixelY / 5);
      } else
        N.scrollLeft += Math.ceil(x.pixelX / 5);
    },
    resizeProxyVisible: s,
    bodyWidth: z,
    resizeState: i,
    doLayout: m,
    tableBodyStyles: f,
    tableLayout: Q,
    scrollbarViewStyle: c,
    scrollbarStyle: Y
  };
}
function cu(e) {
  const t = R(), n = () => {
    const a = e.vnode.el.querySelector(".hidden-columns"), r = { childList: !0, subtree: !0 }, s = e.store.states.updateOrderFns;
    t.value = new MutationObserver(() => {
      s.forEach((o) => o());
    }), t.value.observe(a, r);
  };
  gt(() => {
    n();
  }), An(() => {
    var l;
    (l = t.value) == null || l.disconnect();
  });
}
var du = {
  data: {
    type: Array,
    default: () => []
  },
  size: Vt,
  width: [String, Number],
  height: [String, Number],
  maxHeight: [String, Number],
  fit: {
    type: Boolean,
    default: !0
  },
  stripe: Boolean,
  border: Boolean,
  rowKey: [String, Function],
  showHeader: {
    type: Boolean,
    default: !0
  },
  showSummary: Boolean,
  sumText: String,
  summaryMethod: Function,
  rowClassName: [String, Function],
  rowStyle: [Object, Function],
  cellClassName: [String, Function],
  cellStyle: [Object, Function],
  headerRowClassName: [String, Function],
  headerRowStyle: [Object, Function],
  headerCellClassName: [String, Function],
  headerCellStyle: [Object, Function],
  highlightCurrentRow: Boolean,
  currentRowKey: [String, Number],
  emptyText: String,
  expandRowKeys: Array,
  defaultExpandAll: Boolean,
  defaultSort: Object,
  tooltipEffect: String,
  tooltipOptions: Object,
  spanMethod: Function,
  selectOnIndeterminate: {
    type: Boolean,
    default: !0
  },
  indent: {
    type: Number,
    default: 16
  },
  treeProps: {
    type: Object,
    default: () => ({
      hasChildren: "hasChildren",
      children: "children",
      checkStrictly: !1
    })
  },
  lazy: Boolean,
  load: Function,
  style: {
    type: Object,
    default: () => ({})
  },
  className: {
    type: String,
    default: ""
  },
  tableLayout: {
    type: String,
    default: "fixed"
  },
  scrollbarAlwaysOn: Boolean,
  flexible: Boolean,
  showOverflowTooltip: [Boolean, Object],
  tooltipFormatter: Function,
  appendFilterPanelTo: String,
  scrollbarTabindex: {
    type: [Number, String],
    default: void 0
  },
  allowDragLastColumn: {
    type: Boolean,
    default: !0
  },
  preserveExpandedContent: {
    type: Boolean,
    default: !1
  }
};
function Tr(e) {
  const t = e.tableLayout === "auto";
  let n = e.columns || [];
  t && n.every(({ width: a }) => ke(a)) && (n = []);
  const l = (a) => {
    const r = {
      key: `${e.tableLayout}_${a.id}`,
      style: {},
      name: void 0
    };
    return t ? r.style = {
      width: `${a.width}px`
    } : r.name = a.id, r;
  };
  return W("colgroup", {}, n.map((a) => W("col", l(a))));
}
Tr.props = ["columns", "tableLayout"];
const fu = () => {
  const e = R(), t = (r, s) => {
    const o = e.value;
    o && o.scrollTo(r, s);
  }, n = (r, s) => {
    const o = e.value;
    o && De(s) && ["Top", "Left"].includes(r) && o[`setScroll${r}`](s);
  };
  return {
    scrollBarRef: e,
    scrollTo: t,
    setScrollTop: (r) => n("Top", r),
    setScrollLeft: (r) => n("Left", r)
  };
};
var Cl = !1, je, vn, pn, Tt, kt, kr, At, gn, mn, bn, Ar, yn, Cn, Mr, Lr;
function ve() {
  if (!Cl) {
    Cl = !0;
    var e = navigator.userAgent, t = /(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e), n = /(Mac OS X)|(Windows)|(Linux)/.exec(e);
    if (yn = /\b(iPhone|iP[ao]d)/.exec(e), Cn = /\b(iP[ao]d)/.exec(e), bn = /Android/i.exec(e), Mr = /FBAN\/\w+;/i.exec(e), Lr = /Mobile/i.exec(e), Ar = !!/Win64/.exec(e), t) {
      je = t[1] ? parseFloat(t[1]) : t[5] ? parseFloat(t[5]) : NaN, je && document && document.documentMode && (je = document.documentMode);
      var l = /(?:Trident\/(\d+.\d+))/.exec(e);
      kr = l ? parseFloat(l[1]) + 4 : je, vn = t[2] ? parseFloat(t[2]) : NaN, pn = t[3] ? parseFloat(t[3]) : NaN, Tt = t[4] ? parseFloat(t[4]) : NaN, Tt ? (t = /(?:Chrome\/(\d+\.\d+))/.exec(e), kt = t && t[1] ? parseFloat(t[1]) : NaN) : kt = NaN;
    } else je = vn = pn = kt = Tt = NaN;
    if (n) {
      if (n[1]) {
        var a = /(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);
        At = a ? parseFloat(a[1].replace("_", ".")) : !0;
      } else At = !1;
      gn = !!n[2], mn = !!n[3];
    } else At = gn = mn = !1;
  }
}
var wn = { ie: function() {
  return ve() || je;
}, ieCompatibilityMode: function() {
  return ve() || kr > je;
}, ie64: function() {
  return wn.ie() && Ar;
}, firefox: function() {
  return ve() || vn;
}, opera: function() {
  return ve() || pn;
}, webkit: function() {
  return ve() || Tt;
}, safari: function() {
  return wn.webkit();
}, chrome: function() {
  return ve() || kt;
}, windows: function() {
  return ve() || gn;
}, osx: function() {
  return ve() || At;
}, linux: function() {
  return ve() || mn;
}, iphone: function() {
  return ve() || yn;
}, mobile: function() {
  return ve() || yn || Cn || bn || Lr;
}, nativeApp: function() {
  return ve() || Mr;
}, android: function() {
  return ve() || bn;
}, ipad: function() {
  return ve() || Cn;
} }, hu = wn, vu = !!(typeof window < "u" && window.document && window.document.createElement), pu = { canUseDOM: vu }, Or = pu, Nr;
Or.canUseDOM && (Nr = document.implementation && document.implementation.hasFeature && document.implementation.hasFeature("", "") !== !0);
function gu(e, t) {
  if (!Or.canUseDOM || t && !("addEventListener" in document)) return !1;
  var n = "on" + e, l = n in document;
  if (!l) {
    var a = document.createElement("div");
    a.setAttribute(n, "return;"), l = typeof a[n] == "function";
  }
  return !l && Nr && e === "wheel" && (l = document.implementation.hasFeature("Events.wheel", "3.0")), l;
}
var mu = gu, wl = 10, Sl = 40, xl = 800;
function Fr(e) {
  var t = 0, n = 0, l = 0, a = 0;
  return "detail" in e && (n = e.detail), "wheelDelta" in e && (n = -e.wheelDelta / 120), "wheelDeltaY" in e && (n = -e.wheelDeltaY / 120), "wheelDeltaX" in e && (t = -e.wheelDeltaX / 120), "axis" in e && e.axis === e.HORIZONTAL_AXIS && (t = n, n = 0), l = t * wl, a = n * wl, "deltaY" in e && (a = e.deltaY), "deltaX" in e && (l = e.deltaX), (l || a) && e.deltaMode && (e.deltaMode == 1 ? (l *= Sl, a *= Sl) : (l *= xl, a *= xl)), l && !t && (t = l < 1 ? -1 : 1), a && !n && (n = a < 1 ? -1 : 1), { spinX: t, spinY: n, pixelX: l, pixelY: a };
}
Fr.getEventType = function() {
  return hu.firefox() ? "DOMMouseScroll" : mu("wheel") ? "wheel" : "mousewheel";
};
var bu = Fr;
/**
* Checks if an event is supported in the current execution environment.
*
* NOTE: This will not work correctly for non-generic events such as `change`,
* `reset`, `load`, `error`, and `select`.
*
* Borrows from Modernizr.
*
* @param {string} eventNameSuffix Event name, e.g. "click".
* @param {?boolean} capture Check if the capture phase is supported.
* @return {boolean} True if the event is supported.
* @internal
* @license Modernizr 3.0.0pre (Custom Build) | MIT
*/
const yu = function(e, t) {
  if (e && e.addEventListener) {
    const n = function(l) {
      const a = bu(l);
      t && Reflect.apply(t, this, [l, a]);
    };
    e.addEventListener("wheel", n, { passive: !0 });
  }
}, Cu = {
  beforeMount(e, t) {
    yu(e, t.value);
  }
};
let wu = 1;
const Su = ne({
  name: "ElTable",
  directives: {
    Mousewheel: Cu
  },
  components: {
    TableHeader: Xi,
    TableBody: ru,
    TableFooter: su,
    ElScrollbar: Dl,
    hColgroup: Tr
  },
  props: du,
  emits: [
    "select",
    "select-all",
    "selection-change",
    "cell-mouse-enter",
    "cell-mouse-leave",
    "cell-contextmenu",
    "cell-click",
    "cell-dblclick",
    "row-click",
    "row-contextmenu",
    "row-dblclick",
    "header-click",
    "header-contextmenu",
    "sort-change",
    "filter-change",
    "current-change",
    "header-dragend",
    "expand-change",
    "scroll"
  ],
  setup(e) {
    const { t } = Vl(), n = ae("table"), l = ie();
    kn(Le, l);
    const a = Ii(l, e);
    l.store = a;
    const r = new Vi({
      store: l.store,
      table: l,
      fit: e.fit,
      showHeader: e.showHeader
    });
    l.layout = r;
    const s = O(() => (a.states.data.value || []).length === 0), {
      setCurrentRow: o,
      getSelectionRows: i,
      toggleRowSelection: u,
      clearSelection: c,
      clearFilter: h,
      toggleAllSelection: v,
      toggleRowExpansion: b,
      clearSort: d,
      sort: p,
      updateKeyChildren: g
    } = iu(a), {
      isHidden: C,
      renderExpanded: T,
      setDragVisible: S,
      isGroup: f,
      handleMouseLeave: m,
      handleHeaderFooterMousewheel: w,
      tableSize: y,
      emptyBlockStyle: E,
      handleFixedMousewheel: k,
      resizeProxyVisible: F,
      bodyWidth: V,
      resizeState: $,
      doLayout: z,
      tableBodyStyles: Q,
      tableLayout: D,
      scrollbarViewStyle: Y,
      scrollbarStyle: Z
    } = uu(e, r, a, l), { scrollBarRef: M, scrollTo: x, setScrollLeft: N, setScrollTop: G } = fu(), ee = Ot(z, 50), te = `${n.namespace.value}-table_${wu++}`;
    l.tableId = te, l.state = {
      isGroup: f,
      resizeState: $,
      doLayout: z,
      debouncedUpdateLayout: ee
    };
    const ue = O(() => {
      var le;
      return (le = e.sumText) != null ? le : t("el.table.sumText");
    }), fe = O(() => {
      var le;
      return (le = e.emptyText) != null ? le : t("el.table.emptyText");
    }), Se = O(() => Rr(a.states.originColumns.value)[0]);
    return cu(l), Ul(() => {
      ee.cancel();
    }), {
      ns: n,
      layout: r,
      store: a,
      columns: Se,
      handleHeaderFooterMousewheel: w,
      handleMouseLeave: m,
      tableId: te,
      tableSize: y,
      isHidden: C,
      isEmpty: s,
      renderExpanded: T,
      resizeProxyVisible: F,
      resizeState: $,
      isGroup: f,
      bodyWidth: V,
      tableBodyStyles: Q,
      emptyBlockStyle: E,
      debouncedUpdateLayout: ee,
      handleFixedMousewheel: k,
      setCurrentRow: o,
      getSelectionRows: i,
      toggleRowSelection: u,
      clearSelection: c,
      clearFilter: h,
      toggleAllSelection: v,
      toggleRowExpansion: b,
      clearSort: d,
      doLayout: z,
      sort: p,
      updateKeyChildren: g,
      t,
      setDragVisible: S,
      context: l,
      computedSumText: ue,
      computedEmptyText: fe,
      tableLayout: D,
      scrollbarViewStyle: Y,
      scrollbarStyle: Z,
      scrollBarRef: M,
      scrollTo: x,
      setScrollLeft: N,
      setScrollTop: G,
      allowDragLastColumn: e.allowDragLastColumn
    };
  }
});
function xu(e, t, n, l, a, r) {
  const s = Ee("hColgroup"), o = Ee("table-header"), i = Ee("table-body"), u = Ee("table-footer"), c = Ee("el-scrollbar"), h = jl("mousewheel");
  return B(), q("div", {
    ref: "tableWrapper",
    class: _([
      {
        [e.ns.m("fit")]: e.fit,
        [e.ns.m("striped")]: e.stripe,
        [e.ns.m("border")]: e.border || e.isGroup,
        [e.ns.m("hidden")]: e.isHidden,
        [e.ns.m("group")]: e.isGroup,
        [e.ns.m("fluid-height")]: e.maxHeight,
        [e.ns.m("scrollable-x")]: e.layout.scrollX.value,
        [e.ns.m("scrollable-y")]: e.layout.scrollY.value,
        [e.ns.m("enable-row-hover")]: !e.store.states.isComplex.value,
        [e.ns.m("enable-row-transition")]: (e.store.states.data.value || []).length !== 0 && (e.store.states.data.value || []).length < 100,
        "has-footer": e.showSummary
      },
      e.ns.m(e.tableSize),
      e.className,
      e.ns.b(),
      e.ns.m(`layout-${e.tableLayout}`)
    ]),
    style: Xe(e.style),
    "data-prefix": e.ns.namespace.value,
    onMouseleave: e.handleMouseLeave
  }, [
    ce("div", {
      class: _(e.ns.e("inner-wrapper"))
    }, [
      ce("div", {
        ref: "hiddenColumns",
        class: "hidden-columns"
      }, [
        ge(e.$slots, "default")
      ], 512),
      e.showHeader && e.tableLayout === "fixed" ? Ve((B(), q("div", {
        key: 0,
        ref: "headerWrapper",
        class: _(e.ns.e("header-wrapper"))
      }, [
        ce("table", {
          ref: "tableHeader",
          class: _(e.ns.e("header")),
          style: Xe(e.tableBodyStyles),
          border: "0",
          cellpadding: "0",
          cellspacing: "0"
        }, [
          Me(s, {
            columns: e.store.states.columns.value,
            "table-layout": e.tableLayout
          }, null, 8, ["columns", "table-layout"]),
          Me(o, {
            ref: "tableHeaderRef",
            border: e.border,
            "default-sort": e.defaultSort,
            store: e.store,
            "append-filter-panel-to": e.appendFilterPanelTo,
            "allow-drag-last-column": e.allowDragLastColumn,
            onSetDragVisible: e.setDragVisible
          }, null, 8, ["border", "default-sort", "store", "append-filter-panel-to", "allow-drag-last-column", "onSetDragVisible"])
        ], 6)
      ], 2)), [
        [h, e.handleHeaderFooterMousewheel]
      ]) : xe("v-if", !0),
      ce("div", {
        ref: "bodyWrapper",
        class: _(e.ns.e("body-wrapper"))
      }, [
        Me(c, {
          ref: "scrollBarRef",
          "view-style": e.scrollbarViewStyle,
          "wrap-style": e.scrollbarStyle,
          always: e.scrollbarAlwaysOn,
          tabindex: e.scrollbarTabindex,
          onScroll: (v) => e.$emit("scroll", v)
        }, {
          default: Re(() => [
            ce("table", {
              ref: "tableBody",
              class: _(e.ns.e("body")),
              cellspacing: "0",
              cellpadding: "0",
              border: "0",
              style: Xe({
                width: e.bodyWidth,
                tableLayout: e.tableLayout
              })
            }, [
              Me(s, {
                columns: e.store.states.columns.value,
                "table-layout": e.tableLayout
              }, null, 8, ["columns", "table-layout"]),
              e.showHeader && e.tableLayout === "auto" ? (B(), we(o, {
                key: 0,
                ref: "tableHeaderRef",
                class: _(e.ns.e("body-header")),
                border: e.border,
                "default-sort": e.defaultSort,
                store: e.store,
                "append-filter-panel-to": e.appendFilterPanelTo,
                onSetDragVisible: e.setDragVisible
              }, null, 8, ["class", "border", "default-sort", "store", "append-filter-panel-to", "onSetDragVisible"])) : xe("v-if", !0),
              Me(i, {
                context: e.context,
                highlight: e.highlightCurrentRow,
                "row-class-name": e.rowClassName,
                "tooltip-effect": e.tooltipEffect,
                "tooltip-options": e.tooltipOptions,
                "row-style": e.rowStyle,
                store: e.store,
                stripe: e.stripe
              }, null, 8, ["context", "highlight", "row-class-name", "tooltip-effect", "tooltip-options", "row-style", "store", "stripe"]),
              e.showSummary && e.tableLayout === "auto" ? (B(), we(u, {
                key: 1,
                class: _(e.ns.e("body-footer")),
                border: e.border,
                "default-sort": e.defaultSort,
                store: e.store,
                "sum-text": e.computedSumText,
                "summary-method": e.summaryMethod
              }, null, 8, ["class", "border", "default-sort", "store", "sum-text", "summary-method"])) : xe("v-if", !0)
            ], 6),
            e.isEmpty ? (B(), q("div", {
              key: 0,
              ref: "emptyBlock",
              style: Xe(e.emptyBlockStyle),
              class: _(e.ns.e("empty-block"))
            }, [
              ce("span", {
                class: _(e.ns.e("empty-text"))
              }, [
                ge(e.$slots, "empty", {}, () => [
                  jt(We(e.computedEmptyText), 1)
                ])
              ], 2)
            ], 6)) : xe("v-if", !0),
            e.$slots.append ? (B(), q("div", {
              key: 1,
              ref: "appendWrapper",
              class: _(e.ns.e("append-wrapper"))
            }, [
              ge(e.$slots, "append")
            ], 2)) : xe("v-if", !0)
          ]),
          _: 3
        }, 8, ["view-style", "wrap-style", "always", "tabindex", "onScroll"])
      ], 2),
      e.showSummary && e.tableLayout === "fixed" ? Ve((B(), q("div", {
        key: 1,
        ref: "footerWrapper",
        class: _(e.ns.e("footer-wrapper"))
      }, [
        ce("table", {
          class: _(e.ns.e("footer")),
          cellspacing: "0",
          cellpadding: "0",
          border: "0",
          style: Xe(e.tableBodyStyles)
        }, [
          Me(s, {
            columns: e.store.states.columns.value,
            "table-layout": e.tableLayout
          }, null, 8, ["columns", "table-layout"]),
          Me(u, {
            border: e.border,
            "default-sort": e.defaultSort,
            store: e.store,
            "sum-text": e.computedSumText,
            "summary-method": e.summaryMethod
          }, null, 8, ["border", "default-sort", "store", "sum-text", "summary-method"])
        ], 6)
      ], 2)), [
        [Gn, !e.isEmpty],
        [h, e.handleHeaderFooterMousewheel]
      ]) : xe("v-if", !0),
      e.border || e.isGroup ? (B(), q("div", {
        key: 2,
        class: _(e.ns.e("border-left-patch"))
      }, null, 2)) : xe("v-if", !0)
    ], 2),
    Ve(ce("div", {
      ref: "resizeProxy",
      class: _(e.ns.e("column-resize-proxy"))
    }, null, 2), [
      [Gn, e.resizeProxyVisible]
    ])
  ], 46, ["data-prefix", "onMouseleave"]);
}
var Eu = /* @__PURE__ */ Ke(Su, [["render", xu], ["__file", "table.vue"]]);
const Ru = {
  selection: "table-column--selection",
  expand: "table__expand-column"
}, Tu = {
  default: {
    order: ""
  },
  selection: {
    width: 48,
    minWidth: 48,
    realWidth: 48,
    order: ""
  },
  expand: {
    width: 48,
    minWidth: 48,
    realWidth: 48,
    order: ""
  },
  index: {
    width: 48,
    minWidth: 48,
    realWidth: 48,
    order: ""
  }
}, ku = (e) => Ru[e] || "", Au = {
  selection: {
    renderHeader({ store: e, column: t }) {
      function n() {
        return e.states.data.value && e.states.data.value.length === 0;
      }
      return W(Ze, {
        disabled: n(),
        size: e.states.tableSize.value,
        indeterminate: e.states.selection.value.length > 0 && !e.states.isAllSelected.value,
        "onUpdate:modelValue": e.toggleAllSelection,
        modelValue: e.states.isAllSelected.value,
        ariaLabel: t.label
      });
    },
    renderCell({
      row: e,
      column: t,
      store: n,
      $index: l
    }) {
      return W(Ze, {
        disabled: t.selectable ? !t.selectable.call(null, e, l) : !1,
        size: n.states.tableSize.value,
        onChange: () => {
          n.commit("rowSelectedChanged", e);
        },
        onClick: (a) => a.stopPropagation(),
        modelValue: n.isSelected(e),
        ariaLabel: t.label
      });
    },
    sortable: !1,
    resizable: !1
  },
  index: {
    renderHeader({ column: e }) {
      return e.label || "#";
    },
    renderCell({
      column: e,
      $index: t
    }) {
      let n = t + 1;
      const l = e.index;
      return De(l) ? n = t + l : Te(l) && (n = l(t)), W("div", {}, [n]);
    },
    sortable: !1
  },
  expand: {
    renderHeader({ column: e }) {
      return e.label || "";
    },
    renderCell({
      row: e,
      store: t,
      expanded: n
    }) {
      const { ns: l } = t, a = [l.e("expand-icon")];
      return n && a.push(l.em("expand-icon", "expanded")), W("div", {
        class: a,
        onClick: function(s) {
          s.stopPropagation(), t.toggleRowExpansion(e);
        }
      }, {
        default: () => [
          W(ct, null, {
            default: () => [W(Yl)]
          })
        ]
      });
    },
    sortable: !1,
    resizable: !1
  }
};
function Mu({
  row: e,
  column: t,
  $index: n
}) {
  var l;
  const a = t.property, r = a && Il(e, a).value;
  return t && t.formatter ? t.formatter(e, t, r, n) : ((l = r == null ? void 0 : r.toString) == null ? void 0 : l.call(r)) || "";
}
function Lu({
  row: e,
  treeNode: t,
  store: n
}, l = !1) {
  const { ns: a } = n;
  if (!t)
    return l ? [
      W("span", {
        class: a.e("placeholder")
      })
    ] : null;
  const r = [], s = function(o) {
    o.stopPropagation(), !t.loading && n.loadOrToggle(e);
  };
  if (t.indent && r.push(W("span", {
    class: a.e("indent"),
    style: { "padding-left": `${t.indent}px` }
  })), Oe(t.expanded) && !t.noLazyChildren) {
    const o = [
      a.e("expand-icon"),
      t.expanded ? a.em("expand-icon", "expanded") : ""
    ];
    let i = Yl;
    t.loading && (i = ql), r.push(W("div", {
      class: o,
      onClick: s
    }, {
      default: () => [
        W(ct, { class: { [a.is("loading")]: t.loading } }, {
          default: () => [W(i)]
        })
      ]
    }));
  } else
    r.push(W("span", {
      class: a.e("placeholder")
    }));
  return r;
}
function El(e, t) {
  return e.reduce((n, l) => (n[l] = l, n), t);
}
function Ou(e, t) {
  const n = ie();
  return {
    registerComplexWatchers: () => {
      const r = ["fixed"], s = {
        realWidth: "width",
        realMinWidth: "minWidth"
      }, o = El(r, s);
      Object.keys(o).forEach((i) => {
        const u = s[i];
        ft(t, u) && de(() => t[u], (c) => {
          let h = c;
          u === "width" && i === "realWidth" && (h = Nn(c)), u === "minWidth" && i === "realMinWidth" && (h = yr(c)), n.columnConfig.value[u] = h, n.columnConfig.value[i] = h;
          const v = u === "fixed";
          e.value.store.scheduleLayout(v);
        });
      });
    },
    registerNormalWatchers: () => {
      const r = [
        "label",
        "filters",
        "filterMultiple",
        "filteredValue",
        "sortable",
        "index",
        "formatter",
        "className",
        "labelClassName",
        "filterClassName",
        "showOverflowTooltip",
        "tooltipFormatter"
      ], s = {
        property: "prop",
        align: "realAlign",
        headerAlign: "realHeaderAlign"
      }, o = El(r, s);
      Object.keys(o).forEach((i) => {
        const u = s[i];
        ft(t, u) && de(() => t[u], (c) => {
          n.columnConfig.value[i] = c;
        });
      });
    }
  };
}
function Nu(e, t, n) {
  const l = ie(), a = R(""), r = R(!1), s = R(), o = R(), i = ae("table");
  ot(() => {
    s.value = e.align ? `is-${e.align}` : null, s.value;
  }), ot(() => {
    o.value = e.headerAlign ? `is-${e.headerAlign}` : s.value, o.value;
  });
  const u = O(() => {
    let f = l.vnode.vParent || l.parent;
    for (; f && !f.tableId && !f.columnId; )
      f = f.vnode.vParent || f.parent;
    return f;
  }), c = O(() => {
    const { store: f } = l.parent;
    if (!f)
      return !1;
    const { treeData: m } = f.states, w = m.value;
    return w && Object.keys(w).length > 0;
  }), h = R(Nn(e.width)), v = R(yr(e.minWidth)), b = (f) => (h.value && (f.width = h.value), v.value && (f.minWidth = v.value), !h.value && v.value && (f.width = void 0), f.minWidth || (f.minWidth = 80), f.realWidth = Number(ke(f.width) ? f.minWidth : f.width), f), d = (f) => {
    const m = f.type, w = Au[m] || {};
    Object.keys(w).forEach((E) => {
      const k = w[E];
      E !== "className" && !ke(k) && (f[E] = k);
    });
    const y = ku(m);
    if (y) {
      const E = `${A(i.namespace)}-${y}`;
      f.className = f.className ? `${f.className} ${E}` : E;
    }
    return f;
  }, p = (f) => {
    J(f) ? f.forEach((w) => m(w)) : m(f);
    function m(w) {
      var y;
      ((y = w == null ? void 0 : w.type) == null ? void 0 : y.name) === "ElTableColumn" && (w.vParent = l);
    }
  };
  return {
    columnId: a,
    realAlign: s,
    isSubColumn: r,
    realHeaderAlign: o,
    columnOrTableParent: u,
    setColumnWidth: b,
    setColumnForcedProps: d,
    setColumnRenders: (f) => {
      e.renderHeader || f.type !== "selection" && (f.renderHeader = (w) => (l.columnConfig.value.label, ge(t, "header", w, () => [f.label]))), t["filter-icon"] && (f.renderFilterIcon = (w) => ge(t, "filter-icon", w));
      let m = f.renderCell;
      return f.type === "expand" ? (f.renderCell = (w) => W("div", {
        class: "cell"
      }, [m(w)]), n.value.renderExpanded = (w) => t.default ? t.default(w) : t.default) : (m = m || Mu, f.renderCell = (w) => {
        let y = null;
        if (t.default) {
          const z = t.default(w);
          y = z.some((Q) => Q.type !== pa) ? z : m(w);
        } else
          y = m(w);
        const { columns: E } = n.value.store.states, k = E.value.findIndex((z) => z.type === "default"), F = c.value && w.cellIndex === k, V = Lu(w, F), $ = {
          class: "cell",
          style: {}
        };
        return f.showOverflowTooltip && ($.class = `${$.class} ${A(i.namespace)}-tooltip`, $.style = {
          width: `${(w.column.realWidth || Number(w.column.width)) - 1}px`
        }), p(y), W("div", $, [V, y]);
      }), f;
    },
    getPropsData: (...f) => f.reduce((m, w) => (J(w) && w.forEach((y) => {
      m[y] = e[y];
    }), m), {}),
    getColumnElIndex: (f, m) => Array.prototype.indexOf.call(f, m),
    updateColumnOrder: () => {
      n.value.store.commit("updateColumnOrder", l.columnConfig.value);
    }
  };
}
var Fu = {
  type: {
    type: String,
    default: "default"
  },
  label: String,
  className: String,
  labelClassName: String,
  property: String,
  prop: String,
  width: {
    type: [String, Number],
    default: ""
  },
  minWidth: {
    type: [String, Number],
    default: ""
  },
  renderHeader: Function,
  sortable: {
    type: [Boolean, String],
    default: !1
  },
  sortMethod: Function,
  sortBy: [String, Function, Array],
  resizable: {
    type: Boolean,
    default: !0
  },
  columnKey: String,
  align: String,
  headerAlign: String,
  showOverflowTooltip: {
    type: [Boolean, Object],
    default: void 0
  },
  tooltipFormatter: Function,
  fixed: [Boolean, String],
  formatter: Function,
  selectable: Function,
  reserveSelection: Boolean,
  filterMethod: Function,
  filteredValue: Array,
  filters: Array,
  filterPlacement: String,
  filterMultiple: {
    type: Boolean,
    default: !0
  },
  filterClassName: String,
  index: [Number, Function],
  sortOrders: {
    type: Array,
    default: () => ["ascending", "descending", null],
    validator: (e) => e.every((t) => ["ascending", "descending", null].includes(t))
  }
};
let $u = 1;
var $r = ne({
  name: "ElTableColumn",
  components: {
    ElCheckbox: Ze
  },
  props: Fu,
  setup(e, { slots: t }) {
    const n = ie(), l = R({}), a = O(() => {
      let S = n.parent;
      for (; S && !S.tableId; )
        S = S.parent;
      return S;
    }), { registerNormalWatchers: r, registerComplexWatchers: s } = Ou(a, e), {
      columnId: o,
      isSubColumn: i,
      realHeaderAlign: u,
      columnOrTableParent: c,
      setColumnWidth: h,
      setColumnForcedProps: v,
      setColumnRenders: b,
      getPropsData: d,
      getColumnElIndex: p,
      realAlign: g,
      updateColumnOrder: C
    } = Nu(e, t, a), T = c.value;
    o.value = `${T.tableId || T.columnId}_column_${$u++}`, Gl(() => {
      i.value = a.value !== T;
      const S = e.type || "default", f = e.sortable === "" ? !0 : e.sortable, m = S === "selection" ? !1 : ke(e.showOverflowTooltip) ? T.props.showOverflowTooltip : e.showOverflowTooltip, w = ke(e.tooltipFormatter) ? T.props.tooltipFormatter : e.tooltipFormatter, y = {
        ...Tu[S],
        id: o.value,
        type: S,
        property: e.prop || e.property,
        align: g,
        headerAlign: u,
        showOverflowTooltip: m,
        tooltipFormatter: w,
        filterable: e.filters || e.filterMethod,
        filteredValue: [],
        filterPlacement: "",
        filterClassName: "",
        isColumnGroup: !1,
        isSubColumn: !1,
        filterOpened: !1,
        sortable: f,
        index: e.index,
        rawColumnKey: n.vnode.key
      };
      let $ = d([
        "columnKey",
        "label",
        "className",
        "labelClassName",
        "type",
        "renderHeader",
        "formatter",
        "fixed",
        "resizable"
      ], ["sortMethod", "sortBy", "sortOrders"], ["selectable", "reserveSelection"], [
        "filterMethod",
        "filters",
        "filterMultiple",
        "filterOpened",
        "filteredValue",
        "filterPlacement",
        "filterClassName"
      ]);
      $ = Ai(y, $), $ = Li(b, h, v)($), l.value = $, r(), s();
    }), gt(() => {
      var S;
      const f = c.value, m = i.value ? f.vnode.el.children : (S = f.refs.hiddenColumns) == null ? void 0 : S.children, w = () => p(m || [], n.vnode.el);
      l.value.getColumnIndex = w, w() > -1 && a.value.store.commit("insertColumn", l.value, i.value ? f.columnConfig.value : null, C);
    }), Ul(() => {
      const S = l.value.getColumnIndex;
      (S ? S() : -1) > -1 && a.value.store.commit("removeColumn", l.value, i.value ? T.columnConfig.value : null, C);
    }), n.columnId = o.value, n.columnConfig = l;
  },
  render() {
    var e, t, n;
    try {
      const l = (t = (e = this.$slots).default) == null ? void 0 : t.call(e, {
        row: {},
        column: {},
        $index: -1
      }), a = [];
      if (J(l))
        for (const s of l)
          ((n = s.type) == null ? void 0 : n.name) === "ElTableColumn" || s.shapeFlag & 2 ? a.push(s) : s.type === vt && J(s.children) && s.children.forEach((o) => {
            (o == null ? void 0 : o.patchFlag) !== 1024 && !me(o == null ? void 0 : o.children) && a.push(o);
          });
      return W("div", a);
    } catch {
      return W("div", []);
    }
  }
});
const Vu = Rn(Eu, {
  TableColumn: $r
}), zu = zt($r);
export {
  Ri as C,
  Du as E,
  Ne as S,
  er as a,
  tr as b,
  Ca as c,
  Io as d,
  Wo as e,
  Be as f,
  Oo as g,
  eo as h,
  ya as i,
  Pt as j,
  On as k,
  To as l,
  Do as m,
  Xn as n,
  nl as o,
  Iu as p,
  Vu as q,
  zu as r,
  Ao as s,
  Is as t,
  Et as u
};
