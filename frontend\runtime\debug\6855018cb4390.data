a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:54:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:48:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:52:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:12:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"237";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:16:"application/json";s:12:"content-type";s:16:"application/json";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";}s:5:"route";s:36:"instrument/handle-instrument-booking";s:6:"action";s:74:"frontend\controllers\InstrumentController::actionHandleInstrumentBooking()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:237:"{"id":"","detail":{"type":0,"instrumentId":"2654","instrumentName":"01031721","related_experiment":"N150001-003","warn":0,"remark":"DCVCS ","user":"1135"},"timeArr":[{"start_time":"2025-06-21 23:00:00","end_time":"2025-06-21 24:00:00"}]}";s:17:"Decoded to Params";a:3:{s:2:"id";s:0:"";s:6:"detail";a:7:{s:4:"type";i:0;s:12:"instrumentId";s:4:"2654";s:14:"instrumentName";s:8:"01031721";s:18:"related_experiment";s:11:"N150001-003";s:4:"warn";i:0;s:6:"remark";s:6:"DCVCS ";s:4:"user";s:4:"1135";}s:7:"timeArr";a:1:{i:0;a:2:{s:10:"start_time";s:19:"2025-06-21 23:00:00";s:8:"end_time";s:19:"2025-06-21 24:00:00";}}}}s:6:"SERVER";a:45:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"237";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:16:"application/json";s:12:"CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:47:"D:/integle2025/eln_trunk/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"62901";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:38:"r=instrument/handle-instrument-booking";s:11:"REQUEST_URI";s:40:"/?r=instrument/handle-instrument-booking";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1750401420.507;s:12:"REQUEST_TIME";i:1750401420;}s:3:"GET";a:1:{s:1:"r";s:36:"instrument/handle-instrument-booking";}s:4:"POST";a:0:{}s:6:"COOKIE";a:9:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:15:"center_language";s:2:"CN";s:11:"dataview_id";s:3:"101";s:9:"page_type";s:1:"1";s:16:"last_active_time";s:13:"1750401420407";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1135";s:5:"email";N;s:4:"name";s:6:"chenqi";s:5:"phone";N;s:6:"ticket";s:32:"38828f261ee60584144cf546b2ff9ece";s:8:"reg_time";s:10:"1744077856";s:5:"Token";s:32:"7eb44480540d6e80df79fce77c791828";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:6:"陈奇";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"2";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,84";s:10:"department";a:0:{}s:2:"id";s:4:"1135";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"3";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:34:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750401420.5609939;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750401420.582248;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750401420.7410641;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750401420.7474771;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1750401420.7528341;i:4;a:0:{}}i:5;a:5:{i:0;s:55:"Route requested: 'instrument/handle-instrument-booking'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1750401420.7528911;i:4;a:0:{}}i:6;a:5:{i:0;s:61:"请求数据为:{"r":"instrument\/handle-instrument-booking"}";i:1;i:4;i:2;s:7:"Request";i:3;d:**********.048785;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:132;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}}i:7;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.455004;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4727111;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.473388;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.473757;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.4741919;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.474262;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.4751379;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.4771399;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4881029;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4885991;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.4890649;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"D:\integle2025\eln_trunk\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:18;a:5:{i:0;s:50:"Route to run: instrument/handle-instrument-booking";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.4906731;i:4;a:0:{}}i:19;a:5:{i:0;s:90:"Running action: frontend\controllers\InstrumentController::actionHandleInstrumentBooking()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.4942739;i:4;a:0:{}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.5039101;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5877;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.5241611;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5877;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:138:"SELECT `available_slots`, `max_advance_day`, `min_advance`, `max_booking_duration` FROM `instruments` WHERE (`id`='2654') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581614;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6004;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:175:"SELECT COUNT(*) FROM `instruments_book` WHERE (((`instrument_id`='2654') AND (`status`=1)) AND (`start_time` < '2025-06-21 24:00:00')) AND (`end_time` > '2025-06-21 23:00:00')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5849731;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6023;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5870759;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592483;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:36;a:5:{i:0;s:225:"INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.610405;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5918;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:4452:"exception 'PDOException' with message 'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '2025-06-21 24:00:00' for column 'end_time' at row 1' in D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php:798
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(798): PDOStatement->execute()
#1 D:\integle2025\eln_trunk\common\components\Command.php(29): yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448): common\components\command->execute()
#3 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457): yii\db\Schema->insert('instruments_boo...', Array)
#4 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427): yii\db\ActiveRecord->insertInternal(NULL)
#5 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593): yii\db\ActiveRecord->insert(true, NULL)
#6 D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918): yii\db\BaseActiveRecord->save()
#7 D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854): frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0, Array, Array, '')
#8 [internal function]: frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55): call_user_func_array(Array, Array)
#10 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154): yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454): yii\base\Controller->runAction('handle-instrume...', Array)
#12 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84): yii\base\Module->runAction('instrument/hand...', Array)
#13 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375): yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33): yii\base\Application->run()
#15 {main}

Next exception 'yii\db\Exception' with message 'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '2025-06-21 24:00:00' for column 'end_time' at row 1
The SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)' in D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php:628
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(808): yii\db\Schema->convertException(Object(PDOException), 'INSERT INTO `in...')
#1 D:\integle2025\eln_trunk\common\components\Command.php(29): yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448): common\components\command->execute()
#3 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457): yii\db\Schema->insert('instruments_boo...', Array)
#4 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427): yii\db\ActiveRecord->insertInternal(NULL)
#5 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593): yii\db\ActiveRecord->insert(true, NULL)
#6 D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918): yii\db\BaseActiveRecord->save()
#7 D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854): frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0, Array, Array, '')
#8 [internal function]: frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55): call_user_func_array(Array, Array)
#10 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154): yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454): yii\base\Controller->runAction('handle-instrume...', Array)
#12 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84): yii\base\Module->runAction('instrument/hand...', Array)
#13 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375): yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33): yii\base\Application->run()
#15 {main}
Additional Information:
Array
(
    [0] => 22007
    [1] => 1292
    [2] => Incorrect datetime value: '2025-06-21 24:00:00' for column 'end_time' at row 1
)
";i:1;i:1;i:2;s:16:"yii\db\Exception";i:3;d:**********.6129899;i:4;a:0:{}}i:40;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.62012;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:4988:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (1, 'yii\\db\\Exception', **********.613, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', 'exception \'PDOException\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php:798\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(798): PDOStatement->execute()\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\n\nNext exception \'yii\\db\\Exception\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\nThe SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, \'2025-06-21 23:00:00\', \'2025-06-21 24:00:00\', \'N150001-003\', \'DCVCS \', 1135, 0)\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php:628\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(808): yii\\db\\Schema->convertException(Object(PDOException), \'INSERT INTO `in...\')\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\r\nAdditional Information:\r\nArray\n(\n    [0] => 22007\n    [1] => 1292\n    [2] => Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\n)\n')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.623332;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:6203:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (4, 'application', 1750401420.5386, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', '$_GET = [\n    \'r\' => \'instrument/handle-instrument-booking\'\n]\n\n$_COOKIE = [\n    \'eln_page_limit\' => \'15\'\n    \'ldap_check\' => \'0\'\n    \'integle_session\' => \'0vd1scddjptlgvq6v4umqol4j7\'\n    \'sims_u\' => \'38828f261ee60584144cf546b2ff9ece\'\n    \'lock_interval\' => \'180\'\n    \'center_language\' => \'CN\'\n    \'dataview_id\' => \'101\'\n    \'page_type\' => \'1\'\n    \'last_active_time\' => \'1750401420407\'\n]\n\n$_SESSION = [\n    \'__flash\' => []\n    \'userinfo\' => [\n        \'user_id\' => \'1135\'\n        \'email\' => null\n        \'name\' => \'chenqi\'\n        \'phone\' => null\n        \'ticket\' => \'38828f261ee60584144cf546b2ff9ece\'\n        \'reg_time\' => \'1744077856\'\n        \'Token\' => \'7eb44480540d6e80df79fce77c791828\'\n        \'register_from\' => \'\'\n        \'from_ldap\' => \'0\'\n        \'gender\' => \'0\'\n        \'nick_name\' => \'\'\n        \'contact_phone\' => \'\'\n        \'real_name\' => \'陈奇\'\n        \'point\' => \'0\'\n        \'company_name\' => \'\'\n        \'job\' => \'\'\n        \'office_phone\' => \'\'\n        \'qq\' => \'\'\n        \'country\' => \'\'\n        \'province\' => \'\'\n        \'city\' => \'\'\n        \'detail_address\' => \'\'\n        \'post_code\' => \'\'\n        \'id_card\' => \'\'\n        \'big_img\' => \'\'\n        \'small_img\' => \'\'\n        \'unread_message\' => \'2\'\n        \'default_group\' => \'0\'\n        \'contact_email\' => \'\'\n        \'role_ids\' => \'1,84\'\n        \'department\' => []\n        \'id\' => \'1135\'\n        \'groups\' => [\n            0 => [\n                \'id\' => \'1\'\n                \'name\' => \'公司群\'\n                \'role\' => \'1\'\n            ]\n            1 => [\n                \'id\' => \'598\'\n                \'name\' => \'cq1\'\n                \'role\' => \'3\'\n            ]\n        ]\n        \'current_company_id\' => \'1\'\n        \'app_access\' => 1\n    ]\n    \'eln_lang\' => \'zh-CN\'\n]\n\n$_SERVER = [\n    \'MIBDIRS\' => \'D:/xampp/php/extras/mibs\'\n    \'MYSQL_HOME\' => \'\\\\xampp\\\\mysql\\\\bin\'\n    \'OPENSSL_CONF\' => \'D:/xampp/apache/bin/openssl.cnf\'\n    \'PHP_PEAR_SYSCONF_DIR\' => \'\\\\xampp\\\\php\'\n    \'PHPRC\' => \'\\\\xampp\\\\php\'\n    \'TMP\' => \'\\\\xampp\\\\tmp\'\n    \'HTTP_HOST\' => \'dev.eln.integle.com\'\n    \'HTTP_CONNECTION\' => \'keep-alive\'\n    \'CONTENT_LENGTH\' => \'237\'\n    \'HTTP_X_REQUESTED_WITH\' => \'XMLHttpRequest\'\n    \'HTTP_USER_AGENT\' => \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\'\n    \'HTTP_ACCEPT\' => \'application/json\'\n    \'CONTENT_TYPE\' => \'application/json\'\n    \'HTTP_ORIGIN\' => \'http://dev.eln.integle.com\'\n    \'HTTP_REFERER\' => \'http://dev.eln.integle.com/\'\n    \'HTTP_ACCEPT_ENCODING\' => \'gzip, deflate\'\n    \'HTTP_ACCEPT_LANGUAGE\' => \'zh-CN,zh;q=0.9\'\n    \'HTTP_COOKIE\' => \'eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407\'\n    \'PATH\' => \'C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\java8path;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Program Files (x86)\\\\PerkinElmerInformatics\\\\ChemOffice2017\\\\ChemScript\\\\Lib;C:\\\\WINDOWS\\\\system32;C:\\\\WINDOWS;C:\\\\WINDOWS\\\\System32\\\\Wbem;C:\\\\WINDOWS\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\WINDOWS\\\\System32\\\\OpenSSH\\\\;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent64\\\\OCR;D:\\\\Program Files\\\\TortoiseSVN\\\\bin;D:\\\\Program Files\\\\Java\\\\jdk-1.8\\\\bin;D:\\\\Program Files\\\\php\\\\php-5.6.40-Win32-VC11-x64;D:\\\\composer;D:\\\\Program Files\\\\Git\\\\cmd;D:\\\\Program Files\\\\nodejs\\\\node_global\\\\node_modules;D:\\\\nvm;D:\\\\nvm4w\\\\nodejs;D:\\\\Program Files\\\\nodejs\\\\node_global;D:\\\\Program Files\\\\wget-1.21.4-win64;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\pnpm;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\Program Files\\\\JetBrains\\\\IntelliJ IDEA 2024.1.4\\\\bin;;D:\\\\Program Files\\\\JetBrains\\\\PhpStorm 2024.1.4\\\\bin;;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Composer\\\\vendor\\\\bin;D:\\\\Program Files\\\\JetBrains\\\\WebStorm 2024.1.5\\\\bin;;D:\\\\Users\\\\chenc\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\bin;D:\\\\Program Files\\\\cursor\\\\resources\\\\app\\\\bin\'\n    \'SystemRoot\' => \'C:\\\\WINDOWS\'\n    \'COMSPEC\' => \'C:\\\\WINDOWS\\\\system32\\\\cmd.exe\'\n    \'PATHEXT\' => \'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\'\n    \'WINDIR\' => \'C:\\\\WINDOWS\'\n    \'SERVER_SIGNATURE\' => \'<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>\n\'\n    \'SERVER_SOFTWARE\' => \'Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40\'\n    \'SERVER_NAME\' => \'dev.eln.integle.com\'\n    \'SERVER_ADDR\' => \'::1\'\n    \'SERVER_PORT\' => \'80\'\n    \'REMOTE_ADDR\' => \'::1\'\n    \'DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'REQUEST_SCHEME\' => \'http\'\n    \'CONTEXT_PREFIX\' => \'\'\n    \'CONTEXT_DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'SERVER_ADMIN\' => \'postmaster@localhost\'\n    \'SCRIPT_FILENAME\' => \'D:/integle2025/eln_trunk/frontend/web/index.php\'\n    \'REMOTE_PORT\' => \'62901\'\n    \'GATEWAY_INTERFACE\' => \'CGI/1.1\'\n    \'SERVER_PROTOCOL\' => \'HTTP/1.1\'\n    \'REQUEST_METHOD\' => \'POST\'\n    \'QUERY_STRING\' => \'r=instrument/handle-instrument-booking\'\n    \'REQUEST_URI\' => \'/?r=instrument/handle-instrument-booking\'\n    \'SCRIPT_NAME\' => \'/index.php\'\n    \'PHP_SELF\' => \'/index.php\'\n    \'REQUEST_TIME_FLOAT\' => 1750401420.507\n    \'REQUEST_TIME\' => 1750401420\n]')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6946881;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:133:"Sending email "http://dev.eln.integle.com/ 2025-06-20 14:37:00 系统报错" to "<EMAIL>, <EMAIL>"";i:1;i:4;i:2;s:25:"yii\mail\BaseMailer::send";i:3;d:**********.968601;i:4;a:0:{}}i:50;a:5:{i:0;s:133:"Sending email "http://dev.eln.integle.com/ 2025-06-20 14:37:00 系统报错" to "<EMAIL>, <EMAIL>"";i:1;i:4;i:2;s:35:"yii\swiftmailer\Mailer::sendMessage";i:3;d:**********.9686639;i:4;a:0:{}}i:51;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750401423.4102981;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:20588280;s:4:"time";d:2.8735370635986328;s:8:"messages";a:18:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.5039361;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5877;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.5204649;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5877;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:138:"SELECT `available_slots`, `max_advance_day`, `min_advance`, `max_booking_duration` FROM `instruments` WHERE (`id`='2654') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581686;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6004;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:138:"SELECT `available_slots`, `max_advance_day`, `min_advance`, `max_booking_duration` FROM `instruments` WHERE (`id`='2654') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5823629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6004;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:175:"SELECT COUNT(*) FROM `instruments_book` WHERE (((`instrument_id`='2654') AND (`status`=1)) AND (`start_time` < '2025-06-21 24:00:00')) AND (`end_time` > '2025-06-21 23:00:00')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5850241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6023;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:175:"SELECT COUNT(*) FROM `instruments_book` WHERE (((`instrument_id`='2654') AND (`status`=1)) AND (`start_time` < '2025-06-21 24:00:00')) AND (`end_time` > '2025-06-21 23:00:00')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5858331;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6023;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.587132;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.589591;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5932629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:225:"INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6104851;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5918;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:225:"INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.611099;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5918;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.620156;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.623096;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:4988:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (1, 'yii\\db\\Exception', **********.613, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', 'exception \'PDOException\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php:798\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(798): PDOStatement->execute()\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\n\nNext exception \'yii\\db\\Exception\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\nThe SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, \'2025-06-21 23:00:00\', \'2025-06-21 24:00:00\', \'N150001-003\', \'DCVCS \', 1135, 0)\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php:628\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(808): yii\\db\\Schema->convertException(Object(PDOException), \'INSERT INTO `in...\')\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\r\nAdditional Information:\r\nArray\n(\n    [0] => 22007\n    [1] => 1292\n    [2] => Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\n)\n')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6233931;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:4988:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (1, 'yii\\db\\Exception', **********.613, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', 'exception \'PDOException\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php:798\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(798): PDOStatement->execute()\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\n\nNext exception \'yii\\db\\Exception\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\nThe SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, \'2025-06-21 23:00:00\', \'2025-06-21 24:00:00\', \'N150001-003\', \'DCVCS \', 1135, 0)\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php:628\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(808): yii\\db\\Schema->convertException(Object(PDOException), \'INSERT INTO `in...\')\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\r\nAdditional Information:\r\nArray\n(\n    [0] => 22007\n    [1] => 1292\n    [2] => Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\n)\n')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6944849;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:6203:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (4, 'application', 1750401420.5386, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', '$_GET = [\n    \'r\' => \'instrument/handle-instrument-booking\'\n]\n\n$_COOKIE = [\n    \'eln_page_limit\' => \'15\'\n    \'ldap_check\' => \'0\'\n    \'integle_session\' => \'0vd1scddjptlgvq6v4umqol4j7\'\n    \'sims_u\' => \'38828f261ee60584144cf546b2ff9ece\'\n    \'lock_interval\' => \'180\'\n    \'center_language\' => \'CN\'\n    \'dataview_id\' => \'101\'\n    \'page_type\' => \'1\'\n    \'last_active_time\' => \'1750401420407\'\n]\n\n$_SESSION = [\n    \'__flash\' => []\n    \'userinfo\' => [\n        \'user_id\' => \'1135\'\n        \'email\' => null\n        \'name\' => \'chenqi\'\n        \'phone\' => null\n        \'ticket\' => \'38828f261ee60584144cf546b2ff9ece\'\n        \'reg_time\' => \'1744077856\'\n        \'Token\' => \'7eb44480540d6e80df79fce77c791828\'\n        \'register_from\' => \'\'\n        \'from_ldap\' => \'0\'\n        \'gender\' => \'0\'\n        \'nick_name\' => \'\'\n        \'contact_phone\' => \'\'\n        \'real_name\' => \'陈奇\'\n        \'point\' => \'0\'\n        \'company_name\' => \'\'\n        \'job\' => \'\'\n        \'office_phone\' => \'\'\n        \'qq\' => \'\'\n        \'country\' => \'\'\n        \'province\' => \'\'\n        \'city\' => \'\'\n        \'detail_address\' => \'\'\n        \'post_code\' => \'\'\n        \'id_card\' => \'\'\n        \'big_img\' => \'\'\n        \'small_img\' => \'\'\n        \'unread_message\' => \'2\'\n        \'default_group\' => \'0\'\n        \'contact_email\' => \'\'\n        \'role_ids\' => \'1,84\'\n        \'department\' => []\n        \'id\' => \'1135\'\n        \'groups\' => [\n            0 => [\n                \'id\' => \'1\'\n                \'name\' => \'公司群\'\n                \'role\' => \'1\'\n            ]\n            1 => [\n                \'id\' => \'598\'\n                \'name\' => \'cq1\'\n                \'role\' => \'3\'\n            ]\n        ]\n        \'current_company_id\' => \'1\'\n        \'app_access\' => 1\n    ]\n    \'eln_lang\' => \'zh-CN\'\n]\n\n$_SERVER = [\n    \'MIBDIRS\' => \'D:/xampp/php/extras/mibs\'\n    \'MYSQL_HOME\' => \'\\\\xampp\\\\mysql\\\\bin\'\n    \'OPENSSL_CONF\' => \'D:/xampp/apache/bin/openssl.cnf\'\n    \'PHP_PEAR_SYSCONF_DIR\' => \'\\\\xampp\\\\php\'\n    \'PHPRC\' => \'\\\\xampp\\\\php\'\n    \'TMP\' => \'\\\\xampp\\\\tmp\'\n    \'HTTP_HOST\' => \'dev.eln.integle.com\'\n    \'HTTP_CONNECTION\' => \'keep-alive\'\n    \'CONTENT_LENGTH\' => \'237\'\n    \'HTTP_X_REQUESTED_WITH\' => \'XMLHttpRequest\'\n    \'HTTP_USER_AGENT\' => \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\'\n    \'HTTP_ACCEPT\' => \'application/json\'\n    \'CONTENT_TYPE\' => \'application/json\'\n    \'HTTP_ORIGIN\' => \'http://dev.eln.integle.com\'\n    \'HTTP_REFERER\' => \'http://dev.eln.integle.com/\'\n    \'HTTP_ACCEPT_ENCODING\' => \'gzip, deflate\'\n    \'HTTP_ACCEPT_LANGUAGE\' => \'zh-CN,zh;q=0.9\'\n    \'HTTP_COOKIE\' => \'eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407\'\n    \'PATH\' => \'C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\java8path;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Program Files (x86)\\\\PerkinElmerInformatics\\\\ChemOffice2017\\\\ChemScript\\\\Lib;C:\\\\WINDOWS\\\\system32;C:\\\\WINDOWS;C:\\\\WINDOWS\\\\System32\\\\Wbem;C:\\\\WINDOWS\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\WINDOWS\\\\System32\\\\OpenSSH\\\\;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent64\\\\OCR;D:\\\\Program Files\\\\TortoiseSVN\\\\bin;D:\\\\Program Files\\\\Java\\\\jdk-1.8\\\\bin;D:\\\\Program Files\\\\php\\\\php-5.6.40-Win32-VC11-x64;D:\\\\composer;D:\\\\Program Files\\\\Git\\\\cmd;D:\\\\Program Files\\\\nodejs\\\\node_global\\\\node_modules;D:\\\\nvm;D:\\\\nvm4w\\\\nodejs;D:\\\\Program Files\\\\nodejs\\\\node_global;D:\\\\Program Files\\\\wget-1.21.4-win64;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\pnpm;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\Program Files\\\\JetBrains\\\\IntelliJ IDEA 2024.1.4\\\\bin;;D:\\\\Program Files\\\\JetBrains\\\\PhpStorm 2024.1.4\\\\bin;;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Composer\\\\vendor\\\\bin;D:\\\\Program Files\\\\JetBrains\\\\WebStorm 2024.1.5\\\\bin;;D:\\\\Users\\\\chenc\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\bin;D:\\\\Program Files\\\\cursor\\\\resources\\\\app\\\\bin\'\n    \'SystemRoot\' => \'C:\\\\WINDOWS\'\n    \'COMSPEC\' => \'C:\\\\WINDOWS\\\\system32\\\\cmd.exe\'\n    \'PATHEXT\' => \'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\'\n    \'WINDIR\' => \'C:\\\\WINDOWS\'\n    \'SERVER_SIGNATURE\' => \'<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>\n\'\n    \'SERVER_SOFTWARE\' => \'Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40\'\n    \'SERVER_NAME\' => \'dev.eln.integle.com\'\n    \'SERVER_ADDR\' => \'::1\'\n    \'SERVER_PORT\' => \'80\'\n    \'REMOTE_ADDR\' => \'::1\'\n    \'DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'REQUEST_SCHEME\' => \'http\'\n    \'CONTEXT_PREFIX\' => \'\'\n    \'CONTEXT_DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'SERVER_ADMIN\' => \'postmaster@localhost\'\n    \'SCRIPT_FILENAME\' => \'D:/integle2025/eln_trunk/frontend/web/index.php\'\n    \'REMOTE_PORT\' => \'62901\'\n    \'GATEWAY_INTERFACE\' => \'CGI/1.1\'\n    \'SERVER_PROTOCOL\' => \'HTTP/1.1\'\n    \'REQUEST_METHOD\' => \'POST\'\n    \'QUERY_STRING\' => \'r=instrument/handle-instrument-booking\'\n    \'REQUEST_URI\' => \'/?r=instrument/handle-instrument-booking\'\n    \'SCRIPT_NAME\' => \'/index.php\'\n    \'PHP_SELF\' => \'/index.php\'\n    \'REQUEST_TIME_FLOAT\' => 1750401420.507\n    \'REQUEST_TIME\' => 1750401420\n]')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6947341;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:6203:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (4, 'application', 1750401420.5386, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', '$_GET = [\n    \'r\' => \'instrument/handle-instrument-booking\'\n]\n\n$_COOKIE = [\n    \'eln_page_limit\' => \'15\'\n    \'ldap_check\' => \'0\'\n    \'integle_session\' => \'0vd1scddjptlgvq6v4umqol4j7\'\n    \'sims_u\' => \'38828f261ee60584144cf546b2ff9ece\'\n    \'lock_interval\' => \'180\'\n    \'center_language\' => \'CN\'\n    \'dataview_id\' => \'101\'\n    \'page_type\' => \'1\'\n    \'last_active_time\' => \'1750401420407\'\n]\n\n$_SESSION = [\n    \'__flash\' => []\n    \'userinfo\' => [\n        \'user_id\' => \'1135\'\n        \'email\' => null\n        \'name\' => \'chenqi\'\n        \'phone\' => null\n        \'ticket\' => \'38828f261ee60584144cf546b2ff9ece\'\n        \'reg_time\' => \'1744077856\'\n        \'Token\' => \'7eb44480540d6e80df79fce77c791828\'\n        \'register_from\' => \'\'\n        \'from_ldap\' => \'0\'\n        \'gender\' => \'0\'\n        \'nick_name\' => \'\'\n        \'contact_phone\' => \'\'\n        \'real_name\' => \'陈奇\'\n        \'point\' => \'0\'\n        \'company_name\' => \'\'\n        \'job\' => \'\'\n        \'office_phone\' => \'\'\n        \'qq\' => \'\'\n        \'country\' => \'\'\n        \'province\' => \'\'\n        \'city\' => \'\'\n        \'detail_address\' => \'\'\n        \'post_code\' => \'\'\n        \'id_card\' => \'\'\n        \'big_img\' => \'\'\n        \'small_img\' => \'\'\n        \'unread_message\' => \'2\'\n        \'default_group\' => \'0\'\n        \'contact_email\' => \'\'\n        \'role_ids\' => \'1,84\'\n        \'department\' => []\n        \'id\' => \'1135\'\n        \'groups\' => [\n            0 => [\n                \'id\' => \'1\'\n                \'name\' => \'公司群\'\n                \'role\' => \'1\'\n            ]\n            1 => [\n                \'id\' => \'598\'\n                \'name\' => \'cq1\'\n                \'role\' => \'3\'\n            ]\n        ]\n        \'current_company_id\' => \'1\'\n        \'app_access\' => 1\n    ]\n    \'eln_lang\' => \'zh-CN\'\n]\n\n$_SERVER = [\n    \'MIBDIRS\' => \'D:/xampp/php/extras/mibs\'\n    \'MYSQL_HOME\' => \'\\\\xampp\\\\mysql\\\\bin\'\n    \'OPENSSL_CONF\' => \'D:/xampp/apache/bin/openssl.cnf\'\n    \'PHP_PEAR_SYSCONF_DIR\' => \'\\\\xampp\\\\php\'\n    \'PHPRC\' => \'\\\\xampp\\\\php\'\n    \'TMP\' => \'\\\\xampp\\\\tmp\'\n    \'HTTP_HOST\' => \'dev.eln.integle.com\'\n    \'HTTP_CONNECTION\' => \'keep-alive\'\n    \'CONTENT_LENGTH\' => \'237\'\n    \'HTTP_X_REQUESTED_WITH\' => \'XMLHttpRequest\'\n    \'HTTP_USER_AGENT\' => \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\'\n    \'HTTP_ACCEPT\' => \'application/json\'\n    \'CONTENT_TYPE\' => \'application/json\'\n    \'HTTP_ORIGIN\' => \'http://dev.eln.integle.com\'\n    \'HTTP_REFERER\' => \'http://dev.eln.integle.com/\'\n    \'HTTP_ACCEPT_ENCODING\' => \'gzip, deflate\'\n    \'HTTP_ACCEPT_LANGUAGE\' => \'zh-CN,zh;q=0.9\'\n    \'HTTP_COOKIE\' => \'eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407\'\n    \'PATH\' => \'C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\java8path;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Program Files (x86)\\\\PerkinElmerInformatics\\\\ChemOffice2017\\\\ChemScript\\\\Lib;C:\\\\WINDOWS\\\\system32;C:\\\\WINDOWS;C:\\\\WINDOWS\\\\System32\\\\Wbem;C:\\\\WINDOWS\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\WINDOWS\\\\System32\\\\OpenSSH\\\\;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent64\\\\OCR;D:\\\\Program Files\\\\TortoiseSVN\\\\bin;D:\\\\Program Files\\\\Java\\\\jdk-1.8\\\\bin;D:\\\\Program Files\\\\php\\\\php-5.6.40-Win32-VC11-x64;D:\\\\composer;D:\\\\Program Files\\\\Git\\\\cmd;D:\\\\Program Files\\\\nodejs\\\\node_global\\\\node_modules;D:\\\\nvm;D:\\\\nvm4w\\\\nodejs;D:\\\\Program Files\\\\nodejs\\\\node_global;D:\\\\Program Files\\\\wget-1.21.4-win64;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\pnpm;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\Program Files\\\\JetBrains\\\\IntelliJ IDEA 2024.1.4\\\\bin;;D:\\\\Program Files\\\\JetBrains\\\\PhpStorm 2024.1.4\\\\bin;;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Composer\\\\vendor\\\\bin;D:\\\\Program Files\\\\JetBrains\\\\WebStorm 2024.1.5\\\\bin;;D:\\\\Users\\\\chenc\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\bin;D:\\\\Program Files\\\\cursor\\\\resources\\\\app\\\\bin\'\n    \'SystemRoot\' => \'C:\\\\WINDOWS\'\n    \'COMSPEC\' => \'C:\\\\WINDOWS\\\\system32\\\\cmd.exe\'\n    \'PATHEXT\' => \'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\'\n    \'WINDIR\' => \'C:\\\\WINDOWS\'\n    \'SERVER_SIGNATURE\' => \'<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>\n\'\n    \'SERVER_SOFTWARE\' => \'Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40\'\n    \'SERVER_NAME\' => \'dev.eln.integle.com\'\n    \'SERVER_ADDR\' => \'::1\'\n    \'SERVER_PORT\' => \'80\'\n    \'REMOTE_ADDR\' => \'::1\'\n    \'DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'REQUEST_SCHEME\' => \'http\'\n    \'CONTEXT_PREFIX\' => \'\'\n    \'CONTEXT_DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'SERVER_ADMIN\' => \'postmaster@localhost\'\n    \'SCRIPT_FILENAME\' => \'D:/integle2025/eln_trunk/frontend/web/index.php\'\n    \'REMOTE_PORT\' => \'62901\'\n    \'GATEWAY_INTERFACE\' => \'CGI/1.1\'\n    \'SERVER_PROTOCOL\' => \'HTTP/1.1\'\n    \'REQUEST_METHOD\' => \'POST\'\n    \'QUERY_STRING\' => \'r=instrument/handle-instrument-booking\'\n    \'REQUEST_URI\' => \'/?r=instrument/handle-instrument-booking\'\n    \'SCRIPT_NAME\' => \'/index.php\'\n    \'PHP_SELF\' => \'/index.php\'\n    \'REQUEST_TIME_FLOAT\' => 1750401420.507\n    \'REQUEST_TIME\' => 1750401420\n]')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.753005;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:14:{i:25;a:5:{i:0;s:138:"SELECT `available_slots`, `max_advance_day`, `min_advance`, `max_booking_duration` FROM `instruments` WHERE (`id`='2654') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581686;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6004;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:138:"SELECT `available_slots`, `max_advance_day`, `min_advance`, `max_booking_duration` FROM `instruments` WHERE (`id`='2654') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5823629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6004;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:175:"SELECT COUNT(*) FROM `instruments_book` WHERE (((`instrument_id`='2654') AND (`status`=1)) AND (`start_time` < '2025-06-21 24:00:00')) AND (`end_time` > '2025-06-21 23:00:00')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5850241;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6023;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:175:"SELECT COUNT(*) FROM `instruments_book` WHERE (((`instrument_id`='2654') AND (`status`=1)) AND (`start_time` < '2025-06-21 24:00:00')) AND (`end_time` > '2025-06-21 23:00:00')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5858331;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:6023;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5901;s:8:"function";s:27:"checkInstrumentAvailability";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.587132;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.589591;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592531;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5932629;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5910;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:225:"INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6104851;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5918;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:225:"INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21 23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.611099;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php";s:4:"line";i:5918;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php";s:4:"line";i:5854;s:8:"function";s:23:"handleInstrumentBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:4988:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (1, 'yii\\db\\Exception', **********.613, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', 'exception \'PDOException\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php:798\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(798): PDOStatement->execute()\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\n\nNext exception \'yii\\db\\Exception\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\nThe SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, \'2025-06-21 23:00:00\', \'2025-06-21 24:00:00\', \'N150001-003\', \'DCVCS \', 1135, 0)\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php:628\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(808): yii\\db\\Schema->convertException(Object(PDOException), \'INSERT INTO `in...\')\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\r\nAdditional Information:\r\nArray\n(\n    [0] => 22007\n    [1] => 1292\n    [2] => Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\n)\n')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6233931;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:4988:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (1, 'yii\\db\\Exception', **********.613, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', 'exception \'PDOException\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php:798\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(798): PDOStatement->execute()\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\n\nNext exception \'yii\\db\\Exception\' with message \'SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\nThe SQL being executed was: INSERT INTO `instruments_book` (`instrument_id`, `start_time`, `end_time`, `related_experiment`, `remark`, `create_by`, `reminder`) VALUES (2654, \'2025-06-21 23:00:00\', \'2025-06-21 24:00:00\', \'N150001-003\', \'DCVCS \', 1135, 0)\' in D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php:628\nStack trace:\n#0 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Command.php(808): yii\\db\\Schema->convertException(Object(PDOException), \'INSERT INTO `in...\')\n#1 D:\\integle2025\\eln_trunk\\common\\components\\Command.php(29): yii\\db\\Command->execute()\n#2 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\Schema.php(448): common\\components\\command->execute()\n#3 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(457): yii\\db\\Schema->insert(\'instruments_boo...\', Array)\n#4 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\ActiveRecord.php(427): yii\\db\\ActiveRecord->insertInternal(NULL)\n#5 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\db\\BaseActiveRecord.php(593): yii\\db\\ActiveRecord->insert(true, NULL)\n#6 D:\\integle2025\\eln_trunk\\frontend\\services\\InstrumentServer.php(5918): yii\\db\\BaseActiveRecord->save()\n#7 D:\\integle2025\\eln_trunk\\frontend\\controllers\\InstrumentController.php(5854): frontend\\services\\InstrumentServer->handleInstrumentBooking(\'2654\', 0, Array, Array, \'\')\n#8 [internal function]: frontend\\controllers\\InstrumentController->actionHandleInstrumentBooking()\n#9 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\InlineAction.php(55): call_user_func_array(Array, Array)\n#10 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Controller.php(154): yii\\base\\InlineAction->runWithParams(Array)\n#11 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Module.php(454): yii\\base\\Controller->runAction(\'handle-instrume...\', Array)\n#12 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\web\\Application.php(84): yii\\base\\Module->runAction(\'instrument/hand...\', Array)\n#13 D:\\integle2025\\eln_trunk\\vendor\\yiisoft\\yii2\\base\\Application.php(375): yii\\web\\Application->handleRequest(Object(yii\\web\\Request))\n#14 D:\\integle2025\\eln_trunk\\frontend\\web\\index.php(33): yii\\base\\Application->run()\n#15 {main}\r\nAdditional Information:\r\nArray\n(\n    [0] => 22007\n    [1] => 1292\n    [2] => Incorrect datetime value: \'2025-06-21 24:00:00\' for column \'end_time\' at row 1\n)\n')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6944849;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:6203:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (4, 'application', 1750401420.5386, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', '$_GET = [\n    \'r\' => \'instrument/handle-instrument-booking\'\n]\n\n$_COOKIE = [\n    \'eln_page_limit\' => \'15\'\n    \'ldap_check\' => \'0\'\n    \'integle_session\' => \'0vd1scddjptlgvq6v4umqol4j7\'\n    \'sims_u\' => \'38828f261ee60584144cf546b2ff9ece\'\n    \'lock_interval\' => \'180\'\n    \'center_language\' => \'CN\'\n    \'dataview_id\' => \'101\'\n    \'page_type\' => \'1\'\n    \'last_active_time\' => \'1750401420407\'\n]\n\n$_SESSION = [\n    \'__flash\' => []\n    \'userinfo\' => [\n        \'user_id\' => \'1135\'\n        \'email\' => null\n        \'name\' => \'chenqi\'\n        \'phone\' => null\n        \'ticket\' => \'38828f261ee60584144cf546b2ff9ece\'\n        \'reg_time\' => \'1744077856\'\n        \'Token\' => \'7eb44480540d6e80df79fce77c791828\'\n        \'register_from\' => \'\'\n        \'from_ldap\' => \'0\'\n        \'gender\' => \'0\'\n        \'nick_name\' => \'\'\n        \'contact_phone\' => \'\'\n        \'real_name\' => \'陈奇\'\n        \'point\' => \'0\'\n        \'company_name\' => \'\'\n        \'job\' => \'\'\n        \'office_phone\' => \'\'\n        \'qq\' => \'\'\n        \'country\' => \'\'\n        \'province\' => \'\'\n        \'city\' => \'\'\n        \'detail_address\' => \'\'\n        \'post_code\' => \'\'\n        \'id_card\' => \'\'\n        \'big_img\' => \'\'\n        \'small_img\' => \'\'\n        \'unread_message\' => \'2\'\n        \'default_group\' => \'0\'\n        \'contact_email\' => \'\'\n        \'role_ids\' => \'1,84\'\n        \'department\' => []\n        \'id\' => \'1135\'\n        \'groups\' => [\n            0 => [\n                \'id\' => \'1\'\n                \'name\' => \'公司群\'\n                \'role\' => \'1\'\n            ]\n            1 => [\n                \'id\' => \'598\'\n                \'name\' => \'cq1\'\n                \'role\' => \'3\'\n            ]\n        ]\n        \'current_company_id\' => \'1\'\n        \'app_access\' => 1\n    ]\n    \'eln_lang\' => \'zh-CN\'\n]\n\n$_SERVER = [\n    \'MIBDIRS\' => \'D:/xampp/php/extras/mibs\'\n    \'MYSQL_HOME\' => \'\\\\xampp\\\\mysql\\\\bin\'\n    \'OPENSSL_CONF\' => \'D:/xampp/apache/bin/openssl.cnf\'\n    \'PHP_PEAR_SYSCONF_DIR\' => \'\\\\xampp\\\\php\'\n    \'PHPRC\' => \'\\\\xampp\\\\php\'\n    \'TMP\' => \'\\\\xampp\\\\tmp\'\n    \'HTTP_HOST\' => \'dev.eln.integle.com\'\n    \'HTTP_CONNECTION\' => \'keep-alive\'\n    \'CONTENT_LENGTH\' => \'237\'\n    \'HTTP_X_REQUESTED_WITH\' => \'XMLHttpRequest\'\n    \'HTTP_USER_AGENT\' => \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\'\n    \'HTTP_ACCEPT\' => \'application/json\'\n    \'CONTENT_TYPE\' => \'application/json\'\n    \'HTTP_ORIGIN\' => \'http://dev.eln.integle.com\'\n    \'HTTP_REFERER\' => \'http://dev.eln.integle.com/\'\n    \'HTTP_ACCEPT_ENCODING\' => \'gzip, deflate\'\n    \'HTTP_ACCEPT_LANGUAGE\' => \'zh-CN,zh;q=0.9\'\n    \'HTTP_COOKIE\' => \'eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407\'\n    \'PATH\' => \'C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\java8path;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Program Files (x86)\\\\PerkinElmerInformatics\\\\ChemOffice2017\\\\ChemScript\\\\Lib;C:\\\\WINDOWS\\\\system32;C:\\\\WINDOWS;C:\\\\WINDOWS\\\\System32\\\\Wbem;C:\\\\WINDOWS\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\WINDOWS\\\\System32\\\\OpenSSH\\\\;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent64\\\\OCR;D:\\\\Program Files\\\\TortoiseSVN\\\\bin;D:\\\\Program Files\\\\Java\\\\jdk-1.8\\\\bin;D:\\\\Program Files\\\\php\\\\php-5.6.40-Win32-VC11-x64;D:\\\\composer;D:\\\\Program Files\\\\Git\\\\cmd;D:\\\\Program Files\\\\nodejs\\\\node_global\\\\node_modules;D:\\\\nvm;D:\\\\nvm4w\\\\nodejs;D:\\\\Program Files\\\\nodejs\\\\node_global;D:\\\\Program Files\\\\wget-1.21.4-win64;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\pnpm;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\Program Files\\\\JetBrains\\\\IntelliJ IDEA 2024.1.4\\\\bin;;D:\\\\Program Files\\\\JetBrains\\\\PhpStorm 2024.1.4\\\\bin;;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Composer\\\\vendor\\\\bin;D:\\\\Program Files\\\\JetBrains\\\\WebStorm 2024.1.5\\\\bin;;D:\\\\Users\\\\chenc\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\bin;D:\\\\Program Files\\\\cursor\\\\resources\\\\app\\\\bin\'\n    \'SystemRoot\' => \'C:\\\\WINDOWS\'\n    \'COMSPEC\' => \'C:\\\\WINDOWS\\\\system32\\\\cmd.exe\'\n    \'PATHEXT\' => \'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\'\n    \'WINDIR\' => \'C:\\\\WINDOWS\'\n    \'SERVER_SIGNATURE\' => \'<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>\n\'\n    \'SERVER_SOFTWARE\' => \'Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40\'\n    \'SERVER_NAME\' => \'dev.eln.integle.com\'\n    \'SERVER_ADDR\' => \'::1\'\n    \'SERVER_PORT\' => \'80\'\n    \'REMOTE_ADDR\' => \'::1\'\n    \'DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'REQUEST_SCHEME\' => \'http\'\n    \'CONTEXT_PREFIX\' => \'\'\n    \'CONTEXT_DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'SERVER_ADMIN\' => \'postmaster@localhost\'\n    \'SCRIPT_FILENAME\' => \'D:/integle2025/eln_trunk/frontend/web/index.php\'\n    \'REMOTE_PORT\' => \'62901\'\n    \'GATEWAY_INTERFACE\' => \'CGI/1.1\'\n    \'SERVER_PROTOCOL\' => \'HTTP/1.1\'\n    \'REQUEST_METHOD\' => \'POST\'\n    \'QUERY_STRING\' => \'r=instrument/handle-instrument-booking\'\n    \'REQUEST_URI\' => \'/?r=instrument/handle-instrument-booking\'\n    \'SCRIPT_NAME\' => \'/index.php\'\n    \'PHP_SELF\' => \'/index.php\'\n    \'REQUEST_TIME_FLOAT\' => 1750401420.507\n    \'REQUEST_TIME\' => 1750401420\n]')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.6947341;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:6203:"INSERT INTO `yii_log` (`level`, `category`, `log_time`, `prefix`, `message`)
                VALUES (4, 'application', 1750401420.5386, '[::1][-][0vd1scddjptlgvq6v4umqol4j7]', '$_GET = [\n    \'r\' => \'instrument/handle-instrument-booking\'\n]\n\n$_COOKIE = [\n    \'eln_page_limit\' => \'15\'\n    \'ldap_check\' => \'0\'\n    \'integle_session\' => \'0vd1scddjptlgvq6v4umqol4j7\'\n    \'sims_u\' => \'38828f261ee60584144cf546b2ff9ece\'\n    \'lock_interval\' => \'180\'\n    \'center_language\' => \'CN\'\n    \'dataview_id\' => \'101\'\n    \'page_type\' => \'1\'\n    \'last_active_time\' => \'1750401420407\'\n]\n\n$_SESSION = [\n    \'__flash\' => []\n    \'userinfo\' => [\n        \'user_id\' => \'1135\'\n        \'email\' => null\n        \'name\' => \'chenqi\'\n        \'phone\' => null\n        \'ticket\' => \'38828f261ee60584144cf546b2ff9ece\'\n        \'reg_time\' => \'1744077856\'\n        \'Token\' => \'7eb44480540d6e80df79fce77c791828\'\n        \'register_from\' => \'\'\n        \'from_ldap\' => \'0\'\n        \'gender\' => \'0\'\n        \'nick_name\' => \'\'\n        \'contact_phone\' => \'\'\n        \'real_name\' => \'陈奇\'\n        \'point\' => \'0\'\n        \'company_name\' => \'\'\n        \'job\' => \'\'\n        \'office_phone\' => \'\'\n        \'qq\' => \'\'\n        \'country\' => \'\'\n        \'province\' => \'\'\n        \'city\' => \'\'\n        \'detail_address\' => \'\'\n        \'post_code\' => \'\'\n        \'id_card\' => \'\'\n        \'big_img\' => \'\'\n        \'small_img\' => \'\'\n        \'unread_message\' => \'2\'\n        \'default_group\' => \'0\'\n        \'contact_email\' => \'\'\n        \'role_ids\' => \'1,84\'\n        \'department\' => []\n        \'id\' => \'1135\'\n        \'groups\' => [\n            0 => [\n                \'id\' => \'1\'\n                \'name\' => \'公司群\'\n                \'role\' => \'1\'\n            ]\n            1 => [\n                \'id\' => \'598\'\n                \'name\' => \'cq1\'\n                \'role\' => \'3\'\n            ]\n        ]\n        \'current_company_id\' => \'1\'\n        \'app_access\' => 1\n    ]\n    \'eln_lang\' => \'zh-CN\'\n]\n\n$_SERVER = [\n    \'MIBDIRS\' => \'D:/xampp/php/extras/mibs\'\n    \'MYSQL_HOME\' => \'\\\\xampp\\\\mysql\\\\bin\'\n    \'OPENSSL_CONF\' => \'D:/xampp/apache/bin/openssl.cnf\'\n    \'PHP_PEAR_SYSCONF_DIR\' => \'\\\\xampp\\\\php\'\n    \'PHPRC\' => \'\\\\xampp\\\\php\'\n    \'TMP\' => \'\\\\xampp\\\\tmp\'\n    \'HTTP_HOST\' => \'dev.eln.integle.com\'\n    \'HTTP_CONNECTION\' => \'keep-alive\'\n    \'CONTENT_LENGTH\' => \'237\'\n    \'HTTP_X_REQUESTED_WITH\' => \'XMLHttpRequest\'\n    \'HTTP_USER_AGENT\' => \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\'\n    \'HTTP_ACCEPT\' => \'application/json\'\n    \'CONTENT_TYPE\' => \'application/json\'\n    \'HTTP_ORIGIN\' => \'http://dev.eln.integle.com\'\n    \'HTTP_REFERER\' => \'http://dev.eln.integle.com/\'\n    \'HTTP_ACCEPT_ENCODING\' => \'gzip, deflate\'\n    \'HTTP_ACCEPT_LANGUAGE\' => \'zh-CN,zh;q=0.9\'\n    \'HTTP_COOKIE\' => \'eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750401420407\'\n    \'PATH\' => \'C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\java8path;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Program Files (x86)\\\\PerkinElmerInformatics\\\\ChemOffice2017\\\\ChemScript\\\\Lib;C:\\\\WINDOWS\\\\system32;C:\\\\WINDOWS;C:\\\\WINDOWS\\\\System32\\\\Wbem;C:\\\\WINDOWS\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\WINDOWS\\\\System32\\\\OpenSSH\\\\;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent;C:\\\\Program Files (x86)\\\\DSOC\\\\ExtractContent64\\\\OCR;D:\\\\Program Files\\\\TortoiseSVN\\\\bin;D:\\\\Program Files\\\\Java\\\\jdk-1.8\\\\bin;D:\\\\Program Files\\\\php\\\\php-5.6.40-Win32-VC11-x64;D:\\\\composer;D:\\\\Program Files\\\\Git\\\\cmd;D:\\\\Program Files\\\\nodejs\\\\node_global\\\\node_modules;D:\\\\nvm;D:\\\\nvm4w\\\\nodejs;D:\\\\Program Files\\\\nodejs\\\\node_global;D:\\\\Program Files\\\\wget-1.21.4-win64;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\pnpm;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\Program Files\\\\JetBrains\\\\IntelliJ IDEA 2024.1.4\\\\bin;;D:\\\\Program Files\\\\JetBrains\\\\PhpStorm 2024.1.4\\\\bin;;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Composer\\\\vendor\\\\bin;D:\\\\Program Files\\\\JetBrains\\\\WebStorm 2024.1.5\\\\bin;;D:\\\\Users\\\\chenc\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\bin;D:\\\\Program Files\\\\cursor\\\\resources\\\\app\\\\bin\'\n    \'SystemRoot\' => \'C:\\\\WINDOWS\'\n    \'COMSPEC\' => \'C:\\\\WINDOWS\\\\system32\\\\cmd.exe\'\n    \'PATHEXT\' => \'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\'\n    \'WINDIR\' => \'C:\\\\WINDOWS\'\n    \'SERVER_SIGNATURE\' => \'<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>\n\'\n    \'SERVER_SOFTWARE\' => \'Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40\'\n    \'SERVER_NAME\' => \'dev.eln.integle.com\'\n    \'SERVER_ADDR\' => \'::1\'\n    \'SERVER_PORT\' => \'80\'\n    \'REMOTE_ADDR\' => \'::1\'\n    \'DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'REQUEST_SCHEME\' => \'http\'\n    \'CONTEXT_PREFIX\' => \'\'\n    \'CONTEXT_DOCUMENT_ROOT\' => \'D:/integle2025/eln_trunk/frontend/web\'\n    \'SERVER_ADMIN\' => \'postmaster@localhost\'\n    \'SCRIPT_FILENAME\' => \'D:/integle2025/eln_trunk/frontend/web/index.php\'\n    \'REMOTE_PORT\' => \'62901\'\n    \'GATEWAY_INTERFACE\' => \'CGI/1.1\'\n    \'SERVER_PROTOCOL\' => \'HTTP/1.1\'\n    \'REQUEST_METHOD\' => \'POST\'\n    \'QUERY_STRING\' => \'r=instrument/handle-instrument-booking\'\n    \'REQUEST_URI\' => \'/?r=instrument/handle-instrument-booking\'\n    \'SCRIPT_NAME\' => \'/index.php\'\n    \'PHP_SELF\' => \'/index.php\'\n    \'REQUEST_TIME_FLOAT\' => 1750401420.507\n    \'REQUEST_TIME\' => 1750401420\n]')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.753005;i:4;a:1:{i:0;a:5:{s:4:"file";s:54:"D:\integle2025\eln_trunk\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:1:{i:0;a:12:{s:12:"isSuccessful";b:1;s:4:"from";s:20:"<EMAIL>";s:2:"to";s:51:"<EMAIL>, <EMAIL>";s:5:"reply";N;s:2:"cc";N;s:3:"bcc";s:0:"";s:7:"subject";s:60:"http://dev.eln.integle.com/ 2025-06-20 14:37:00 系统报错";s:7:"charset";s:5:"UTF-8";s:4:"body";s:9897:"2025-06-20 14:37:01
[::1][-][0vd1scddjptlgvq6v4umqol4j7][error][yii\db\Exception]
exception 'PDOException' with message 'SQLSTATE[22007]: Invalid
datetime format: 1292 Incorrect datetime value: '2025-06-21 24:00:00'
for column 'end_time' at row 1' in
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php:798
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(798):
PDOStatement->execute()
#1 D:\integle2025\eln_trunk\common\components\Command.php(29):
yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('instruments_boo...', Array)
#4
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal(NULL)
#5
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593):
yii\db\ActiveRecord->insert(true, NULL)
#6
D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854):
frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0,
Array, Array, '')
#8 [internal function]:
frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55):
call_user_func_array(Array, Array)
#10
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('handle-instrume...', Array)
#12
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('instrument/hand...', Array)
#13
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}

Next exception 'yii\db\Exception' with message 'SQLSTATE[22007]:
Invalid datetime format: 1292 Incorrect datetime value: '2025-06-21
24:00:00' for column 'end_time' at row 1
The SQL being executed was: INSERT INTO `instruments_book`
(`instrument_id`, `start_time`, `end_time`, `related_experiment`,
`remark`, `create_by`, `reminder`) VALUES (2654, '2025-06-21
23:00:00', '2025-06-21 24:00:00', 'N150001-003', 'DCVCS ', 1135, 0)'
in D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php:628
Stack trace:
#0 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Command.php(808):
yii\db\Schema->convertException(Object(PDOException), 'INSERT INTO
`in...')
#1 D:\integle2025\eln_trunk\common\components\Command.php(29):
yii\db\Command->execute()
#2 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('instruments_boo...', Array)
#4
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal(NULL)
#5
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\db\BaseActiveRecord.php(593):
yii\db\ActiveRecord->insert(true, NULL)
#6
D:\integle2025\eln_trunk\frontend\services\InstrumentServer.php(5918):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_trunk\frontend\controllers\InstrumentController.php(5854):
frontend\services\InstrumentServer->handleInstrumentBooking('2654', 0,
Array, Array, '')
#8 [internal function]:
frontend\controllers\InstrumentController->actionHandleInstrumentBooking()
#9
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\InlineAction.php(55):
call_user_func_array(Array, Array)
#10
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams(Array)
#11 D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('handle-instrume...', Array)
#12
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('instrument/hand...', Array)
#13
D:\integle2025\eln_trunk\vendor\yiisoft\yii2\base\Application.php(375):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_trunk\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}
Additional Information:
Array
(
    [0] => 22007
    [1] => 1292
    [2] => Incorrect datetime value: '2025-06-21 24:00:00' for column
'end_time' at row 1
)

2025-06-20 14:37:00
[::1][-][0vd1scddjptlgvq6v4umqol4j7][info][application] $_GET = [
    'r' => 'instrument/handle-instrument-booking'
]

$_COOKIE = [
    'eln_page_limit' => '15'
    'ldap_check' => '0'
    'integle_session' => '0vd1scddjptlgvq6v4umqol4j7'
    'sims_u' => '38828f261ee60584144cf546b2ff9ece'
    'lock_interval' => '180'
    'center_language' => 'CN'
    'dataview_id' => '101'
    'page_type' => '1'
    'last_active_time' => '1750401420407'
]

$_SESSION = [
    '__flash' => []
    'userinfo' => [
        'user_id' => '1135'
        'email' => null
        'name' => 'chenqi'
        'phone' => null
        'ticket' => '38828f261ee60584144cf546b2ff9ece'
        'reg_time' => '1744077856'
        'Token' => '7eb44480540d6e80df79fce77c791828'
        'register_from' => ''
        'from_ldap' => '0'
        'gender' => '0'
        'nick_name' => ''
        'contact_phone' => ''
        'real_name' => '陈奇'
        'point' => '0'
        'company_name' => ''
        'job' => ''
        'office_phone' => ''
        'qq' => ''
        'country' => ''
        'province' => ''
        'city' => ''
        'detail_address' => ''
        'post_code' => ''
        'id_card' => ''
        'big_img' => ''
        'small_img' => ''
        'unread_message' => '2'
        'default_group' => '0'
        'contact_email' => ''
        'role_ids' => '1,84'
        'department' => []
        'id' => '1135'
        'groups' => [
            0 => [
                'id' => '1'
                'name' => '公司群'
                'role' => '1'
            ]
            1 => [
                'id' => '598'
                'name' => 'cq1'
                'role' => '3'
            ]
        ]
        'current_company_id' => '1'
        'app_access' => 1
    ]
    'eln_lang' => 'zh-CN'
]

$_SERVER = [
    'MIBDIRS' => 'D:/xampp/php/extras/mibs'
    'MYSQL_HOME' => '\\xampp\\mysql\\bin'
    'OPENSSL_CONF' => 'D:/xampp/apache/bin/openssl.cnf'
    'PHP_PEAR_SYSCONF_DIR' => '\\xampp\\php'
    'PHPRC' => '\\xampp\\php'
    'TMP' => '\\xampp\\tmp'
    'HTTP_HOST' => 'dev.eln.integle.com'
    'HTTP_CONNECTION' => 'keep-alive'
    'CONTENT_LENGTH' => '237'
    'HTTP_X_REQUESTED_WITH' => 'XMLHttpRequest'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)
AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_ORIGIN' => 'http://dev.eln.integle.com'
    'HTTP_REFERER' => 'http://dev.eln.integle.com/'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9'
    'HTTP_COOKIE' => 'eln_page_limit=15; ldap_check=0;
integle_session=0vd1scddjptlgvq6v4umqol4j7;
sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180;
center_language=CN; dataview_id=101; page_type=1;
last_active_time=1750401420407'
    'PATH' => 'C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\javapath;C:\\Program Files
(x86)\\PerkinElmerInformatics\\ChemOffice2017\\ChemScript\\Lib;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program
Files\\dotnet\\;C:\\Program Files
(x86)\\DSOC\\ExtractContent;C:\\Program Files
(x86)\\DSOC\\ExtractContent64\\OCR;D:\\Program
Files\\TortoiseSVN\\bin;D:\\Program
Files\\Java\\jdk-1.8\\bin;D:\\Program
Files\\php\\php-5.6.40-Win32-VC11-x64;D:\\composer;D:\\Program
Files\\Git\\cmd;D:\\Program
Files\\nodejs\\node_global\\node_modules;D:\\nvm;D:\\nvm4w\\nodejs;D:\\Program
Files\\nodejs\\node_global;D:\\Program
Files\\wget-1.21.4-win64;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program
Files\\JetBrains\\IntelliJ IDEA 2024.1.4\\bin;;D:\\Program
Files\\JetBrains\\PhpStorm
2024.1.4\\bin;;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\Program
Files\\JetBrains\\WebStorm
2024.1.5\\bin;;D:\\Users\\chenc\\AppData\\Local\\Programs\\Microsoft
VS Code\\bin;D:\\Program Files\\cursor\\resources\\app\\bin'
    'SystemRoot' => 'C:\\WINDOWS'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' =>
'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'WINDIR' => 'C:\\WINDOWS'
    'SERVER_SIGNATURE' => '<address>Apache/2.4.38 (Win64)
OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port
80</address>
'
    'SERVER_SOFTWARE' => 'Apache/2.4.38 (Win64) OpenSSL/1.0.2q
PHP/5.6.40'
    'SERVER_NAME' => 'dev.eln.integle.com'
    'SERVER_ADDR' => '::1'
    'SERVER_PORT' => '80'
    'REMOTE_ADDR' => '::1'
    'DOCUMENT_ROOT' => 'D:/integle2025/eln_trunk/frontend/web'
    'REQUEST_SCHEME' => 'http'
    'CONTEXT_PREFIX' => ''
    'CONTEXT_DOCUMENT_ROOT' => 'D:/integle2025/eln_trunk/frontend/web'
    'SERVER_ADMIN' => 'postmaster@localhost'
    'SCRIPT_FILENAME' =>
'D:/integle2025/eln_trunk/frontend/web/index.php'
    'REMOTE_PORT' => '62901'
    'GATEWAY_INTERFACE' => 'CGI/1.1'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'REQUEST_METHOD' => 'POST'
    'QUERY_STRING' => 'r=instrument/handle-instrument-booking'
    'REQUEST_URI' => '/?r=instrument/handle-instrument-booking'
    'SCRIPT_NAME' => '/index.php'
    'PHP_SELF' => '/index.php'
    'REQUEST_TIME_FLOAT' => 1750401420.507
    'REQUEST_TIME' => 1750401420
]";s:4:"time";i:**********;s:7:"headers";O:26:"Swift_Mime_SimpleHeaderSet":5:{s:36:" Swift_Mime_SimpleHeaderSet _factory";O:30:"Swift_Mime_SimpleHeaderFactory":4:{s:40:" Swift_Mime_SimpleHeaderFactory _encoder";O:40:"Swift_Mime_HeaderEncoder_QpHeaderEncoder":2:{s:14:" * _charStream";O:39:"Swift_CharacterStream_NgCharacterStream":9:{s:52:" Swift_CharacterStream_NgCharacterStream _charReader";O:32:"Swift_CharacterReader_Utf8Reader":0:{}s:59:" Swift_CharacterStream_NgCharacterStream _charReaderFactory";O:57:"Swift_CharacterReaderFactory_SimpleCharacterReaderFactory":0:{}s:49:" Swift_CharacterStream_NgCharacterStream _charset";s:5:"UTF-8";s:47:" Swift_CharacterStream_NgCharacterStream _datas";s:12:"系统报错";s:51:" Swift_CharacterStream_NgCharacterStream _datasSize";i:12;s:45:" Swift_CharacterStream_NgCharacterStream _map";a:2:{s:1:"i";a:0:{}s:1:"p";a:4:{i:0;i:3;i:1;i:6;i:2;i:9;i:3;i:12;}}s:49:" Swift_CharacterStream_NgCharacterStream _mapType";i:3;s:51:" Swift_CharacterStream_NgCharacterStream _charCount";i:4;s:52:" Swift_CharacterStream_NgCharacterStream _currentPos";i:4;}s:10:" * _filter";N;}s:45:" Swift_Mime_SimpleHeaderFactory _paramEncoder";O:28:"Swift_Encoder_Rfc2231Encoder":1:{s:41:" Swift_Encoder_Rfc2231Encoder _charStream";O:39:"Swift_CharacterStream_NgCharacterStream":9:{s:52:" Swift_CharacterStream_NgCharacterStream _charReader";N;s:59:" Swift_CharacterStream_NgCharacterStream _charReaderFactory";r:1366;s:49:" Swift_CharacterStream_NgCharacterStream _charset";s:5:"UTF-8";s:47:" Swift_CharacterStream_NgCharacterStream _datas";s:0:"";s:51:" Swift_CharacterStream_NgCharacterStream _datasSize";i:0;s:45:" Swift_CharacterStream_NgCharacterStream _map";N;s:49:" Swift_CharacterStream_NgCharacterStream _mapType";i:0;s:51:" Swift_CharacterStream_NgCharacterStream _charCount";i:0;s:52:" Swift_CharacterStream_NgCharacterStream _currentPos";i:0;}}s:40:" Swift_Mime_SimpleHeaderFactory _grammar";O:18:"Swift_Mime_Grammar":0:{}s:40:" Swift_Mime_SimpleHeaderFactory _charset";s:5:"UTF-8";}s:36:" Swift_Mime_SimpleHeaderSet _headers";a:9:{s:25:"content-transfer-encoding";a:1:{i:0;O:37:"Swift_Mime_Headers_UnstructuredHeader":8:{s:45:" Swift_Mime_Headers_UnstructuredHeader _value";s:16:"quoted-printable";s:40:" Swift_Mime_Headers_AbstractHeader _name";s:25:"Content-Transfer-Encoding";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:16:"quoted-printable";}}s:12:"content-type";a:1:{i:0;O:38:"Swift_Mime_Headers_ParameterizedHeader":10:{s:53:" Swift_Mime_Headers_ParameterizedHeader _paramEncoder";N;s:47:" Swift_Mime_Headers_ParameterizedHeader _params";a:1:{s:7:"charset";s:5:"UTF-8";}s:45:" Swift_Mime_Headers_UnstructuredHeader _value";s:10:"text/plain";s:40:" Swift_Mime_Headers_AbstractHeader _name";s:12:"Content-Type";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:10:"text/plain";}}s:12:"mime-version";a:1:{i:0;O:37:"Swift_Mime_Headers_UnstructuredHeader":8:{s:45:" Swift_Mime_Headers_UnstructuredHeader _value";s:3:"1.0";s:40:" Swift_Mime_Headers_AbstractHeader _name";s:12:"MIME-Version";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:3:"1.0";}}s:4:"date";a:1:{i:0;O:29:"Swift_Mime_Headers_DateHeader":8:{s:41:" Swift_Mime_Headers_DateHeader _timestamp";i:**********;s:40:" Swift_Mime_Headers_AbstractHeader _name";s:4:"Date";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";N;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:31:"Fri, 20 Jun 2025 14:37:01 +0800";}}s:10:"message-id";a:1:{i:0;O:39:"Swift_Mime_Headers_IdentificationHeader":8:{s:45:" Swift_Mime_Headers_IdentificationHeader _ids";a:1:{i:0;s:52:"<EMAIL>";}s:40:" Swift_Mime_Headers_AbstractHeader _name";s:10:"Message-ID";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";N;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:54:"<<EMAIL>>";}}s:4:"from";a:1:{i:0;O:32:"Swift_Mime_Headers_MailboxHeader":8:{s:44:" Swift_Mime_Headers_MailboxHeader _mailboxes";a:1:{s:20:"<EMAIL>";s:15:"Integle message";}s:40:" Swift_Mime_Headers_AbstractHeader _name";s:4:"From";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:38:"Integle message <<EMAIL>>";}}s:7:"subject";a:1:{i:0;O:37:"Swift_Mime_Headers_UnstructuredHeader":8:{s:45:" Swift_Mime_Headers_UnstructuredHeader _value";s:60:"http://dev.eln.integle.com/ 2025-06-20 14:37:00 系统报错";s:40:" Swift_Mime_Headers_AbstractHeader _name";s:7:"Subject";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:76;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:96:"http://dev.eln.integle.com/ 2025-06-20 14:37:00 =?UTF-8?Q?=E7=B3=BB=E7=BB=9F=E6=8A=A5=E9=94=99?=";}}s:2:"to";a:1:{i:0;O:32:"Swift_Mime_Headers_MailboxHeader":8:{s:44:" Swift_Mime_Headers_MailboxHeader _mailboxes";a:2:{s:25:"<EMAIL>";N;s:24:"<EMAIL>";N;}s:40:" Swift_Mime_Headers_AbstractHeader _name";s:2:"To";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:51:"<EMAIL>, <EMAIL>";}}s:3:"bcc";a:1:{i:0;O:32:"Swift_Mime_Headers_MailboxHeader":8:{s:44:" Swift_Mime_Headers_MailboxHeader _mailboxes";a:0:{}s:40:" Swift_Mime_Headers_AbstractHeader _name";s:3:"Bcc";s:43:" Swift_Mime_Headers_AbstractHeader _grammar";r:1392;s:43:" Swift_Mime_Headers_AbstractHeader _encoder";r:1363;s:46:" Swift_Mime_Headers_AbstractHeader _lineLength";i:78;s:40:" Swift_Mime_Headers_AbstractHeader _lang";N;s:43:" Swift_Mime_Headers_AbstractHeader _charset";s:5:"UTF-8";s:47:" Swift_Mime_Headers_AbstractHeader _cachedValue";s:0:"";}}}s:34:" Swift_Mime_SimpleHeaderSet _order";a:16:{s:11:"return-path";i:0;s:8:"received";i:1;s:14:"dkim-signature";i:2;s:19:"domainkey-signature";i:3;s:6:"sender";i:4;s:10:"message-id";i:5;s:4:"date";i:6;s:7:"subject";i:7;s:4:"from";i:8;s:8:"reply-to";i:9;s:2:"to";i:10;s:2:"cc";i:11;s:3:"bcc";i:12;s:12:"mime-version";i:13;s:12:"content-type";i:14;s:25:"content-transfer-encoding";i:15;}s:37:" Swift_Mime_SimpleHeaderSet _required";a:3:{s:4:"date";i:0;s:10:"message-id";i:1;s:4:"from";i:2;}s:36:" Swift_Mime_SimpleHeaderSet _charset";s:5:"UTF-8";}s:4:"file";s:29:"20250620-143703-3681-8226.eml";}}s:7:"summary";a:9:{s:3:"tag";s:13:"6855018cb4390";s:3:"url";s:66:"http://dev.eln.integle.com/?r=instrument/handle-instrument-booking";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750401423;s:10:"statusCode";i:500;s:8:"sqlCount";i:7;s:9:"mailCount";i:1;}}