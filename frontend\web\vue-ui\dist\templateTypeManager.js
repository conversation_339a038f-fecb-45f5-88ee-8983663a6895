import { b as At, S as Xe, i as ut, c as Ot, d as Mt, e as St, f as ft, g as Pe, h as dt, j as We, k as $e, l as ct, _ as pt, u as mt, m as Ue, n as Pt, o as ke, p as gt, q as Re, t as $t, r as Rt, s as Nt, w as Ct, x as Ge, y as Me, z as It, A as Vt, a as ge, B as Lt, C as Bt, D as Dt, v as Wt } from "./index3.js";
import { c as qe, k as Ut, a as kt, g as vt, s as zt, b as Ht, d as Xt, e as Gt, f as ze, h as yt, n as xe, i as Kt, j as Jt, l as Zt, m as Yt, S as Qt, o as er, p as Ne, E as tr, q as rr, r as nr } from "./el-button.js";
import { ref as N, computed as I, defineComponent as oe, watch as ie, provide as ht, reactive as bt, toRefs as wt, createElementBlock as Y, openBlock as G, normalizeClass as ue, unref as y, renderSlot as he, inject as <PERSON>, onMounted as <PERSON>, onBeforeUnmount as Tt, onUpdated as ar, createVNode as j, Fragment as be, nextTick as _t, useSlots as ir, createElementVNode as ae, withCtx as S, createBlock as we, createCommentVNode as Ke, resolveDynamicComponent as or, normalizeStyle as Je, createTextVNode as Ce, toDisplayString as Q, TransitionGroup as sr, renderList as lr, withDirectives as ur } from "vue";
import { ElMessage as H, ElDialog as Ze, ElTabs as fr, ElTabPane as dr, ElMessageBox as cr } from "element-plus";
import { p as pr, h as mr, i as gr, e as vr, d as yr } from "./index2.js";
import { u as hr } from "./vue-i18n.js";
import { _ as br } from "./_plugin-vue_export-helper.js";
function wr(r, e) {
  for (var t = -1, n = r == null ? 0 : r.length; ++t < n && e(r[t], t, r) !== !1; )
    ;
  return r;
}
function Tr(r, e) {
  return r && qe(e, Ut(e), r);
}
function _r(r, e) {
  return r && qe(e, kt(e), r);
}
function xr(r, e) {
  return qe(r, vt(r), e);
}
var Fr = Object.getOwnPropertySymbols, qr = Fr ? function(r) {
  for (var e = []; r; )
    At(e, vt(r)), r = Ht(r);
  return e;
} : zt;
function Er(r, e) {
  return qe(r, qr(r), e);
}
var jr = Object.prototype, Ar = jr.hasOwnProperty;
function Or(r) {
  var e = r.length, t = new r.constructor(e);
  return e && typeof r[0] == "string" && Ar.call(r, "index") && (t.index = r.index, t.input = r.input), t;
}
function Mr(r, e) {
  var t = r.buffer;
  return new r.constructor(t, r.byteOffset, r.byteLength);
}
var Sr = /\w*$/;
function Pr(r) {
  var e = new r.constructor(r.source, Sr.exec(r));
  return e.lastIndex = r.lastIndex, e;
}
var Ye = Xe ? Xe.prototype : void 0, Qe = Ye ? Ye.valueOf : void 0;
function $r(r) {
  return Qe ? Object(Qe.call(r)) : {};
}
var Rr = "[object Boolean]", Nr = "[object Date]", Cr = "[object Map]", Ir = "[object Number]", Vr = "[object RegExp]", Lr = "[object Set]", Br = "[object String]", Dr = "[object Symbol]", Wr = "[object ArrayBuffer]", Ur = "[object DataView]", kr = "[object Float32Array]", zr = "[object Float64Array]", Hr = "[object Int8Array]", Xr = "[object Int16Array]", Gr = "[object Int32Array]", Kr = "[object Uint8Array]", Jr = "[object Uint8ClampedArray]", Zr = "[object Uint16Array]", Yr = "[object Uint32Array]";
function Qr(r, e, t) {
  var n = r.constructor;
  switch (e) {
    case Wr:
      return Gt(r);
    case Rr:
    case Nr:
      return new n(+r);
    case Ur:
      return Mr(r);
    case kr:
    case zr:
    case Hr:
    case Xr:
    case Gr:
    case Kr:
    case Jr:
    case Zr:
    case Yr:
      return Xt(r, t);
    case Cr:
      return new n();
    case Ir:
    case Br:
      return new n(r);
    case Vr:
      return Pr(r);
    case Lr:
      return new n();
    case Dr:
      return $r(r);
  }
}
var en = "[object Map]";
function tn(r) {
  return ut(r) && ze(r) == en;
}
var et = xe && xe.isMap, rn = et ? yt(et) : tn, nn = "[object Set]";
function an(r) {
  return ut(r) && ze(r) == nn;
}
var tt = xe && xe.isSet, on = tt ? yt(tt) : an, sn = 1, ln = 2, xt = "[object Arguments]", un = "[object Array]", fn = "[object Boolean]", dn = "[object Date]", cn = "[object Error]", Ft = "[object Function]", pn = "[object GeneratorFunction]", mn = "[object Map]", gn = "[object Number]", qt = "[object Object]", vn = "[object RegExp]", yn = "[object Set]", hn = "[object String]", bn = "[object Symbol]", wn = "[object WeakMap]", Tn = "[object ArrayBuffer]", _n = "[object DataView]", xn = "[object Float32Array]", Fn = "[object Float64Array]", qn = "[object Int8Array]", En = "[object Int16Array]", jn = "[object Int32Array]", An = "[object Uint8Array]", On = "[object Uint8ClampedArray]", Mn = "[object Uint16Array]", Sn = "[object Uint32Array]", P = {};
P[xt] = P[un] = P[Tn] = P[_n] = P[fn] = P[dn] = P[xn] = P[Fn] = P[qn] = P[En] = P[jn] = P[mn] = P[gn] = P[qt] = P[vn] = P[yn] = P[hn] = P[bn] = P[An] = P[On] = P[Mn] = P[Sn] = !0;
P[cn] = P[Ft] = P[wn] = !1;
function Te(r, e, t, n, a, o) {
  var i, s = e & sn, p = e & ln;
  if (i !== void 0)
    return i;
  if (!Ot(r))
    return r;
  var v = Mt(r);
  if (v)
    return i = Or(r), Kt(r, i);
  var d = ze(r), h = d == Ft || d == pn;
  if (Jt(r))
    return Zt(r, s);
  if (d == qt || d == xt || h && !a)
    return i = h ? {} : Yt(r), p ? Er(r, _r(i, r)) : xr(r, Tr(i, r));
  if (!P[d])
    return a ? r : {};
  i = Qr(r, d, s), o || (o = new Qt());
  var w = o.get(r);
  if (w)
    return w;
  o.set(r, i), on(r) ? r.forEach(function(u) {
    i.add(Te(u, e, t, u, r, o));
  }) : rn(r) && r.forEach(function(u, g) {
    i.set(g, Te(u, e, t, g, r, o));
  });
  var A = er, O = v ? void 0 : A(r);
  return wr(O || r, function(u, g) {
    O && (g = u, u = r[g]), St(i, g, Te(u, e, t, g, r, o));
  }), i;
}
var Pn = 4;
function rt(r) {
  return Te(r, Pn);
}
const $n = We({
  size: {
    type: String,
    values: ct
  },
  disabled: Boolean
}), Rn = We({
  ...$n,
  model: Object,
  rules: {
    type: $e(Object)
  },
  labelPosition: {
    type: String,
    values: ["left", "right", "top"],
    default: "right"
  },
  requireAsteriskPosition: {
    type: String,
    values: ["left", "right"],
    default: "left"
  },
  labelWidth: {
    type: [String, Number],
    default: ""
  },
  labelSuffix: {
    type: String,
    default: ""
  },
  inline: Boolean,
  inlineMessage: Boolean,
  statusIcon: Boolean,
  showMessage: {
    type: Boolean,
    default: !0
  },
  validateOnRuleChange: {
    type: Boolean,
    default: !0
  },
  hideRequiredAsterisk: Boolean,
  scrollToError: Boolean,
  scrollIntoViewOptions: {
    type: [Object, Boolean]
  }
}), Nn = {
  validate: (r, e, t) => (ft(r) || Pe(r)) && dt(e) && Pe(t)
};
function Cn() {
  const r = N([]), e = I(() => {
    if (!r.value.length)
      return "0";
    const o = Math.max(...r.value);
    return o ? `${o}px` : "";
  });
  function t(o) {
    const i = r.value.indexOf(o);
    return i === -1 && e.value, i;
  }
  function n(o, i) {
    if (o && i) {
      const s = t(i);
      r.value.splice(s, 1, o);
    } else o && r.value.push(o);
  }
  function a(o) {
    const i = t(o);
    i > -1 && r.value.splice(i, 1);
  }
  return {
    autoLabelWidth: e,
    registerLabelWidth: n,
    deregisterLabelWidth: a
  };
}
const ve = (r, e) => {
  const t = Ne(e);
  return t.length > 0 ? r.filter((n) => n.prop && t.includes(n.prop)) : r;
}, In = "ElForm", Vn = oe({
  name: In
}), Ln = /* @__PURE__ */ oe({
  ...Vn,
  props: Rn,
  emits: Nn,
  setup(r, { expose: e, emit: t }) {
    const n = r, a = [], o = mt(), i = Ue("form"), s = I(() => {
      const { labelPosition: l, inline: m } = n;
      return [
        i.b(),
        i.m(o.value || "default"),
        {
          [i.m(`label-${l}`)]: l,
          [i.m("inline")]: m
        }
      ];
    }), p = (l) => a.find((m) => m.prop === l), v = (l) => {
      a.push(l);
    }, d = (l) => {
      l.prop && a.splice(a.indexOf(l), 1);
    }, h = (l = []) => {
      n.model && ve(a, l).forEach((m) => m.resetField());
    }, w = (l = []) => {
      ve(a, l).forEach((m) => m.clearValidate());
    }, A = I(() => !!n.model), O = (l) => {
      if (a.length === 0)
        return [];
      const m = ve(a, l);
      return m.length ? m : [];
    }, u = async (l) => f(void 0, l), g = async (l = []) => {
      if (!A.value)
        return !1;
      const m = O(l);
      if (m.length === 0)
        return !0;
      let q = {};
      for (const F of m)
        try {
          await F.validate(""), F.validateState === "error" && F.resetField();
        } catch (V) {
          q = {
            ...q,
            ...V
          };
        }
      return Object.keys(q).length === 0 ? !0 : Promise.reject(q);
    }, f = async (l = [], m) => {
      const q = !gt(m);
      try {
        const F = await g(l);
        return F === !0 && await (m == null ? void 0 : m(F)), F;
      } catch (F) {
        if (F instanceof Error)
          throw F;
        const V = F;
        return n.scrollToError && C(Object.keys(V)[0]), await (m == null ? void 0 : m(!1, V)), q && Promise.reject(V);
      }
    }, C = (l) => {
      var m;
      const q = ve(a, l)[0];
      q && ((m = q.$el) == null || m.scrollIntoView(n.scrollIntoViewOptions));
    };
    return ie(() => n.rules, () => {
      n.validateOnRuleChange && u().catch((l) => Pt());
    }, { deep: !0, flush: "post" }), ht(ke, bt({
      ...wt(n),
      emit: t,
      resetFields: h,
      clearValidate: w,
      validateField: f,
      getField: p,
      addField: v,
      removeField: d,
      ...Cn()
    })), e({
      validate: u,
      validateField: f,
      resetFields: h,
      clearValidate: w,
      scrollToField: C,
      fields: a
    }), (l, m) => (G(), Y("form", {
      class: ue(y(s))
    }, [
      he(l.$slots, "default")
    ], 2));
  }
});
var Bn = /* @__PURE__ */ pt(Ln, [["__file", "form.vue"]]);
function ee() {
  return ee = Object.assign ? Object.assign.bind() : function(r) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var n in t)
        Object.prototype.hasOwnProperty.call(t, n) && (r[n] = t[n]);
    }
    return r;
  }, ee.apply(this, arguments);
}
function Dn(r, e) {
  r.prototype = Object.create(e.prototype), r.prototype.constructor = r, ce(r, e);
}
function Ie(r) {
  return Ie = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t) {
    return t.__proto__ || Object.getPrototypeOf(t);
  }, Ie(r);
}
function ce(r, e) {
  return ce = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(n, a) {
    return n.__proto__ = a, n;
  }, ce(r, e);
}
function Wn() {
  if (typeof Reflect > "u" || !Reflect.construct || Reflect.construct.sham) return !1;
  if (typeof Proxy == "function") return !0;
  try {
    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    })), !0;
  } catch {
    return !1;
  }
}
function _e(r, e, t) {
  return Wn() ? _e = Reflect.construct.bind() : _e = function(a, o, i) {
    var s = [null];
    s.push.apply(s, o);
    var p = Function.bind.apply(a, s), v = new p();
    return i && ce(v, i.prototype), v;
  }, _e.apply(null, arguments);
}
function Un(r) {
  return Function.toString.call(r).indexOf("[native code]") !== -1;
}
function Ve(r) {
  var e = typeof Map == "function" ? /* @__PURE__ */ new Map() : void 0;
  return Ve = function(n) {
    if (n === null || !Un(n)) return n;
    if (typeof n != "function")
      throw new TypeError("Super expression must either be null or a function");
    if (typeof e < "u") {
      if (e.has(n)) return e.get(n);
      e.set(n, a);
    }
    function a() {
      return _e(n, arguments, Ie(this).constructor);
    }
    return a.prototype = Object.create(n.prototype, {
      constructor: {
        value: a,
        enumerable: !1,
        writable: !0,
        configurable: !0
      }
    }), ce(a, n);
  }, Ve(r);
}
var kn = /%[sdj%]/g, zn = function() {
};
typeof process < "u" && process.env;
function Le(r) {
  if (!r || !r.length) return null;
  var e = {};
  return r.forEach(function(t) {
    var n = t.field;
    e[n] = e[n] || [], e[n].push(t);
  }), e;
}
function z(r) {
  for (var e = arguments.length, t = new Array(e > 1 ? e - 1 : 0), n = 1; n < e; n++)
    t[n - 1] = arguments[n];
  var a = 0, o = t.length;
  if (typeof r == "function")
    return r.apply(null, t);
  if (typeof r == "string") {
    var i = r.replace(kn, function(s) {
      if (s === "%%")
        return "%";
      if (a >= o)
        return s;
      switch (s) {
        case "%s":
          return String(t[a++]);
        case "%d":
          return Number(t[a++]);
        case "%j":
          try {
            return JSON.stringify(t[a++]);
          } catch {
            return "[Circular]";
          }
          break;
        default:
          return s;
      }
    });
    return i;
  }
  return r;
}
function Hn(r) {
  return r === "string" || r === "url" || r === "hex" || r === "email" || r === "date" || r === "pattern";
}
function B(r, e) {
  return !!(r == null || e === "array" && Array.isArray(r) && !r.length || Hn(e) && typeof r == "string" && !r);
}
function Xn(r, e, t) {
  var n = [], a = 0, o = r.length;
  function i(s) {
    n.push.apply(n, s || []), a++, a === o && t(n);
  }
  r.forEach(function(s) {
    e(s, i);
  });
}
function nt(r, e, t) {
  var n = 0, a = r.length;
  function o(i) {
    if (i && i.length) {
      t(i);
      return;
    }
    var s = n;
    n = n + 1, s < a ? e(r[s], o) : t([]);
  }
  o([]);
}
function Gn(r) {
  var e = [];
  return Object.keys(r).forEach(function(t) {
    e.push.apply(e, r[t] || []);
  }), e;
}
var at = /* @__PURE__ */ function(r) {
  Dn(e, r);
  function e(t, n) {
    var a;
    return a = r.call(this, "Async Validation Error") || this, a.errors = t, a.fields = n, a;
  }
  return e;
}(/* @__PURE__ */ Ve(Error));
function Kn(r, e, t, n, a) {
  if (e.first) {
    var o = new Promise(function(w, A) {
      var O = function(f) {
        return n(f), f.length ? A(new at(f, Le(f))) : w(a);
      }, u = Gn(r);
      nt(u, t, O);
    });
    return o.catch(function(w) {
      return w;
    }), o;
  }
  var i = e.firstFields === !0 ? Object.keys(r) : e.firstFields || [], s = Object.keys(r), p = s.length, v = 0, d = [], h = new Promise(function(w, A) {
    var O = function(g) {
      if (d.push.apply(d, g), v++, v === p)
        return n(d), d.length ? A(new at(d, Le(d))) : w(a);
    };
    s.length || (n(d), w(a)), s.forEach(function(u) {
      var g = r[u];
      i.indexOf(u) !== -1 ? nt(g, t, O) : Xn(g, t, O);
    });
  });
  return h.catch(function(w) {
    return w;
  }), h;
}
function Jn(r) {
  return !!(r && r.message !== void 0);
}
function Zn(r, e) {
  for (var t = r, n = 0; n < e.length; n++) {
    if (t == null)
      return t;
    t = t[e[n]];
  }
  return t;
}
function it(r, e) {
  return function(t) {
    var n;
    return r.fullFields ? n = Zn(e, r.fullFields) : n = e[t.field || r.fullField], Jn(t) ? (t.field = t.field || r.fullField, t.fieldValue = n, t) : {
      message: typeof t == "function" ? t() : t,
      fieldValue: n,
      field: t.field || r.fullField
    };
  };
}
function ot(r, e) {
  if (e) {
    for (var t in e)
      if (e.hasOwnProperty(t)) {
        var n = e[t];
        typeof n == "object" && typeof r[t] == "object" ? r[t] = ee({}, r[t], n) : r[t] = n;
      }
  }
  return r;
}
var Et = function(e, t, n, a, o, i) {
  e.required && (!n.hasOwnProperty(e.field) || B(t, i || e.type)) && a.push(z(o.messages.required, e.fullField));
}, Yn = function(e, t, n, a, o) {
  (/^\s+$/.test(t) || t === "") && a.push(z(o.messages.whitespace, e.fullField));
}, ye, Qn = function() {
  if (ye)
    return ye;
  var r = "[a-fA-F\\d:]", e = function(m) {
    return m && m.includeBoundaries ? "(?:(?<=\\s|^)(?=" + r + ")|(?<=" + r + ")(?=\\s|$))" : "";
  }, t = "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}", n = "[a-fA-F\\d]{1,4}", a = (`
(?:
(?:` + n + ":){7}(?:" + n + `|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:` + n + ":){6}(?:" + t + "|:" + n + `|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:` + n + ":){5}(?::" + t + "|(?::" + n + `){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:` + n + ":){4}(?:(?::" + n + "){0,1}:" + t + "|(?::" + n + `){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:` + n + ":){3}(?:(?::" + n + "){0,2}:" + t + "|(?::" + n + `){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:` + n + ":){2}(?:(?::" + n + "){0,3}:" + t + "|(?::" + n + `){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:` + n + ":){1}(?:(?::" + n + "){0,4}:" + t + "|(?::" + n + `){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::` + n + "){0,5}:" + t + "|(?::" + n + `){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm, "").replace(/\n/g, "").trim(), o = new RegExp("(?:^" + t + "$)|(?:^" + a + "$)"), i = new RegExp("^" + t + "$"), s = new RegExp("^" + a + "$"), p = function(m) {
    return m && m.exact ? o : new RegExp("(?:" + e(m) + t + e(m) + ")|(?:" + e(m) + a + e(m) + ")", "g");
  };
  p.v4 = function(l) {
    return l && l.exact ? i : new RegExp("" + e(l) + t + e(l), "g");
  }, p.v6 = function(l) {
    return l && l.exact ? s : new RegExp("" + e(l) + a + e(l), "g");
  };
  var v = "(?:(?:[a-z]+:)?//)", d = "(?:\\S+(?::\\S*)?@)?", h = p.v4().source, w = p.v6().source, A = "(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)", O = "(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*", u = "(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))", g = "(?::\\d{2,5})?", f = '(?:[/?#][^\\s"]*)?', C = "(?:" + v + "|www\\.)" + d + "(?:localhost|" + h + "|" + w + "|" + A + O + u + ")" + g + f;
  return ye = new RegExp("(?:^" + C + "$)", "i"), ye;
}, st = {
  // http://emailregex.com/
  email: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,
  // url: new RegExp(
  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  //   'i',
  // ),
  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i
}, fe = {
  integer: function(e) {
    return fe.number(e) && parseInt(e, 10) === e;
  },
  float: function(e) {
    return fe.number(e) && !fe.integer(e);
  },
  array: function(e) {
    return Array.isArray(e);
  },
  regexp: function(e) {
    if (e instanceof RegExp)
      return !0;
    try {
      return !!new RegExp(e);
    } catch {
      return !1;
    }
  },
  date: function(e) {
    return typeof e.getTime == "function" && typeof e.getMonth == "function" && typeof e.getYear == "function" && !isNaN(e.getTime());
  },
  number: function(e) {
    return isNaN(e) ? !1 : typeof e == "number";
  },
  object: function(e) {
    return typeof e == "object" && !fe.array(e);
  },
  method: function(e) {
    return typeof e == "function";
  },
  email: function(e) {
    return typeof e == "string" && e.length <= 320 && !!e.match(st.email);
  },
  url: function(e) {
    return typeof e == "string" && e.length <= 2048 && !!e.match(Qn());
  },
  hex: function(e) {
    return typeof e == "string" && !!e.match(st.hex);
  }
}, ea = function(e, t, n, a, o) {
  if (e.required && t === void 0) {
    Et(e, t, n, a, o);
    return;
  }
  var i = ["integer", "float", "array", "regexp", "object", "method", "email", "number", "date", "url", "hex"], s = e.type;
  i.indexOf(s) > -1 ? fe[s](t) || a.push(z(o.messages.types[s], e.fullField, e.type)) : s && typeof t !== e.type && a.push(z(o.messages.types[s], e.fullField, e.type));
}, ta = function(e, t, n, a, o) {
  var i = typeof e.len == "number", s = typeof e.min == "number", p = typeof e.max == "number", v = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g, d = t, h = null, w = typeof t == "number", A = typeof t == "string", O = Array.isArray(t);
  if (w ? h = "number" : A ? h = "string" : O && (h = "array"), !h)
    return !1;
  O && (d = t.length), A && (d = t.replace(v, "_").length), i ? d !== e.len && a.push(z(o.messages[h].len, e.fullField, e.len)) : s && !p && d < e.min ? a.push(z(o.messages[h].min, e.fullField, e.min)) : p && !s && d > e.max ? a.push(z(o.messages[h].max, e.fullField, e.max)) : s && p && (d < e.min || d > e.max) && a.push(z(o.messages[h].range, e.fullField, e.min, e.max));
}, ne = "enum", ra = function(e, t, n, a, o) {
  e[ne] = Array.isArray(e[ne]) ? e[ne] : [], e[ne].indexOf(t) === -1 && a.push(z(o.messages[ne], e.fullField, e[ne].join(", ")));
}, na = function(e, t, n, a, o) {
  if (e.pattern) {
    if (e.pattern instanceof RegExp)
      e.pattern.lastIndex = 0, e.pattern.test(t) || a.push(z(o.messages.pattern.mismatch, e.fullField, t, e.pattern));
    else if (typeof e.pattern == "string") {
      var i = new RegExp(e.pattern);
      i.test(t) || a.push(z(o.messages.pattern.mismatch, e.fullField, t, e.pattern));
    }
  }
}, T = {
  required: Et,
  whitespace: Yn,
  type: ea,
  range: ta,
  enum: ra,
  pattern: na
}, aa = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t, "string") && !e.required)
      return n();
    T.required(e, t, a, i, o, "string"), B(t, "string") || (T.type(e, t, a, i, o), T.range(e, t, a, i, o), T.pattern(e, t, a, i, o), e.whitespace === !0 && T.whitespace(e, t, a, i, o));
  }
  n(i);
}, ia = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && T.type(e, t, a, i, o);
  }
  n(i);
}, oa = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (t === "" && (t = void 0), B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && (T.type(e, t, a, i, o), T.range(e, t, a, i, o));
  }
  n(i);
}, sa = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && T.type(e, t, a, i, o);
  }
  n(i);
}, la = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), B(t) || T.type(e, t, a, i, o);
  }
  n(i);
}, ua = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && (T.type(e, t, a, i, o), T.range(e, t, a, i, o));
  }
  n(i);
}, fa = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && (T.type(e, t, a, i, o), T.range(e, t, a, i, o));
  }
  n(i);
}, da = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (t == null && !e.required)
      return n();
    T.required(e, t, a, i, o, "array"), t != null && (T.type(e, t, a, i, o), T.range(e, t, a, i, o));
  }
  n(i);
}, ca = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && T.type(e, t, a, i, o);
  }
  n(i);
}, pa = "enum", ma = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o), t !== void 0 && T[pa](e, t, a, i, o);
  }
  n(i);
}, ga = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t, "string") && !e.required)
      return n();
    T.required(e, t, a, i, o), B(t, "string") || T.pattern(e, t, a, i, o);
  }
  n(i);
}, va = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t, "date") && !e.required)
      return n();
    if (T.required(e, t, a, i, o), !B(t, "date")) {
      var p;
      t instanceof Date ? p = t : p = new Date(t), T.type(e, p, a, i, o), p && T.range(e, p.getTime(), a, i, o);
    }
  }
  n(i);
}, ya = function(e, t, n, a, o) {
  var i = [], s = Array.isArray(t) ? "array" : typeof t;
  T.required(e, t, a, i, o, s), n(i);
}, Se = function(e, t, n, a, o) {
  var i = e.type, s = [], p = e.required || !e.required && a.hasOwnProperty(e.field);
  if (p) {
    if (B(t, i) && !e.required)
      return n();
    T.required(e, t, a, s, o, i), B(t, i) || T.type(e, t, a, s, o);
  }
  n(s);
}, ha = function(e, t, n, a, o) {
  var i = [], s = e.required || !e.required && a.hasOwnProperty(e.field);
  if (s) {
    if (B(t) && !e.required)
      return n();
    T.required(e, t, a, i, o);
  }
  n(i);
}, de = {
  string: aa,
  method: ia,
  number: oa,
  boolean: sa,
  regexp: la,
  integer: ua,
  float: fa,
  array: da,
  object: ca,
  enum: ma,
  pattern: ga,
  date: va,
  url: Se,
  hex: Se,
  email: Se,
  required: ya,
  any: ha
};
function Be() {
  return {
    default: "Validation error on field %s",
    required: "%s is required",
    enum: "%s must be one of %s",
    whitespace: "%s cannot be empty",
    date: {
      format: "%s date %s is invalid for format %s",
      parse: "%s date could not be parsed, %s is invalid ",
      invalid: "%s date %s is invalid"
    },
    types: {
      string: "%s is not a %s",
      method: "%s is not a %s (function)",
      array: "%s is not an %s",
      object: "%s is not an %s",
      number: "%s is not a %s",
      date: "%s is not a %s",
      boolean: "%s is not a %s",
      integer: "%s is not an %s",
      float: "%s is not a %s",
      regexp: "%s is not a valid %s",
      email: "%s is not a valid %s",
      url: "%s is not a valid %s",
      hex: "%s is not a valid %s"
    },
    string: {
      len: "%s must be exactly %s characters",
      min: "%s must be at least %s characters",
      max: "%s cannot be longer than %s characters",
      range: "%s must be between %s and %s characters"
    },
    number: {
      len: "%s must equal %s",
      min: "%s cannot be less than %s",
      max: "%s cannot be greater than %s",
      range: "%s must be between %s and %s"
    },
    array: {
      len: "%s must be exactly %s in length",
      min: "%s cannot be less than %s in length",
      max: "%s cannot be greater than %s in length",
      range: "%s must be between %s and %s in length"
    },
    pattern: {
      mismatch: "%s value %s does not match pattern %s"
    },
    clone: function() {
      var e = JSON.parse(JSON.stringify(this));
      return e.clone = this.clone, e;
    }
  };
}
var De = Be(), pe = /* @__PURE__ */ function() {
  function r(t) {
    this.rules = null, this._messages = De, this.define(t);
  }
  var e = r.prototype;
  return e.define = function(n) {
    var a = this;
    if (!n)
      throw new Error("Cannot configure a schema with no rules");
    if (typeof n != "object" || Array.isArray(n))
      throw new Error("Rules must be an object");
    this.rules = {}, Object.keys(n).forEach(function(o) {
      var i = n[o];
      a.rules[o] = Array.isArray(i) ? i : [i];
    });
  }, e.messages = function(n) {
    return n && (this._messages = ot(Be(), n)), this._messages;
  }, e.validate = function(n, a, o) {
    var i = this;
    a === void 0 && (a = {}), o === void 0 && (o = function() {
    });
    var s = n, p = a, v = o;
    if (typeof p == "function" && (v = p, p = {}), !this.rules || Object.keys(this.rules).length === 0)
      return v && v(null, s), Promise.resolve(s);
    function d(u) {
      var g = [], f = {};
      function C(m) {
        if (Array.isArray(m)) {
          var q;
          g = (q = g).concat.apply(q, m);
        } else
          g.push(m);
      }
      for (var l = 0; l < u.length; l++)
        C(u[l]);
      g.length ? (f = Le(g), v(g, f)) : v(null, s);
    }
    if (p.messages) {
      var h = this.messages();
      h === De && (h = Be()), ot(h, p.messages), p.messages = h;
    } else
      p.messages = this.messages();
    var w = {}, A = p.keys || Object.keys(this.rules);
    A.forEach(function(u) {
      var g = i.rules[u], f = s[u];
      g.forEach(function(C) {
        var l = C;
        typeof l.transform == "function" && (s === n && (s = ee({}, s)), f = s[u] = l.transform(f)), typeof l == "function" ? l = {
          validator: l
        } : l = ee({}, l), l.validator = i.getValidationMethod(l), l.validator && (l.field = u, l.fullField = l.fullField || u, l.type = i.getType(l), w[u] = w[u] || [], w[u].push({
          rule: l,
          value: f,
          source: s,
          field: u
        }));
      });
    });
    var O = {};
    return Kn(w, p, function(u, g) {
      var f = u.rule, C = (f.type === "object" || f.type === "array") && (typeof f.fields == "object" || typeof f.defaultField == "object");
      C = C && (f.required || !f.required && u.value), f.field = u.field;
      function l(F, V) {
        return ee({}, V, {
          fullField: f.fullField + "." + F,
          fullFields: f.fullFields ? [].concat(f.fullFields, [F]) : [F]
        });
      }
      function m(F) {
        F === void 0 && (F = []);
        var V = Array.isArray(F) ? F : [F];
        !p.suppressWarning && V.length && r.warning("async-validator:", V), V.length && f.message !== void 0 && (V = [].concat(f.message));
        var D = V.map(it(f, s));
        if (p.first && D.length)
          return O[f.field] = 1, g(D);
        if (!C)
          g(D);
        else {
          if (f.required && !u.value)
            return f.message !== void 0 ? D = [].concat(f.message).map(it(f, s)) : p.error && (D = [p.error(f, z(p.messages.required, f.field))]), g(D);
          var X = {};
          f.defaultField && Object.keys(u.value).map(function(W) {
            X[W] = f.defaultField;
          }), X = ee({}, X, u.rule.fields);
          var te = {};
          Object.keys(X).forEach(function(W) {
            var U = X[W], se = Array.isArray(U) ? U : [U];
            te[W] = se.map(l.bind(null, W));
          });
          var Z = new r(te);
          Z.messages(p.messages), u.rule.options && (u.rule.options.messages = p.messages, u.rule.options.error = p.error), Z.validate(u.value, u.rule.options || p, function(W) {
            var U = [];
            D && D.length && U.push.apply(U, D), W && W.length && U.push.apply(U, W), g(U.length ? U : null);
          });
        }
      }
      var q;
      if (f.asyncValidator)
        q = f.asyncValidator(f, u.value, m, u.source, p);
      else if (f.validator) {
        try {
          q = f.validator(f, u.value, m, u.source, p);
        } catch (F) {
          console.error == null || console.error(F), p.suppressValidatorError || setTimeout(function() {
            throw F;
          }, 0), m(F.message);
        }
        q === !0 ? m() : q === !1 ? m(typeof f.message == "function" ? f.message(f.fullField || f.field) : f.message || (f.fullField || f.field) + " fails") : q instanceof Array ? m(q) : q instanceof Error && m(q.message);
      }
      q && q.then && q.then(function() {
        return m();
      }, function(F) {
        return m(F);
      });
    }, function(u) {
      d(u);
    }, s);
  }, e.getType = function(n) {
    if (n.type === void 0 && n.pattern instanceof RegExp && (n.type = "pattern"), typeof n.validator != "function" && n.type && !de.hasOwnProperty(n.type))
      throw new Error(z("Unknown rule type %s", n.type));
    return n.type || "string";
  }, e.getValidationMethod = function(n) {
    if (typeof n.validator == "function")
      return n.validator;
    var a = Object.keys(n), o = a.indexOf("message");
    return o !== -1 && a.splice(o, 1), a.length === 1 && a[0] === "required" ? de.required : de[this.getType(n)] || void 0;
  }, r;
}();
pe.register = function(e, t) {
  if (typeof t != "function")
    throw new Error("Cannot register a validator by type, validator is not a function");
  de[e] = t;
};
pe.warning = zn;
pe.messages = De;
pe.validators = de;
const ba = [
  "",
  "error",
  "validating",
  "success"
], wa = We({
  label: String,
  labelWidth: {
    type: [String, Number],
    default: ""
  },
  labelPosition: {
    type: String,
    values: ["left", "right", "top", ""],
    default: ""
  },
  prop: {
    type: $e([String, Array])
  },
  required: {
    type: Boolean,
    default: void 0
  },
  rules: {
    type: $e([Object, Array])
  },
  error: String,
  validateStatus: {
    type: String,
    values: ba
  },
  for: String,
  inlineMessage: {
    type: [String, Boolean],
    default: ""
  },
  showMessage: {
    type: Boolean,
    default: !0
  },
  size: {
    type: String,
    values: ct
  }
}), lt = "ElLabelWrap";
var Ta = oe({
  name: lt,
  props: {
    isAutoWidth: Boolean,
    updateAll: Boolean
  },
  setup(r, {
    slots: e
  }) {
    const t = Fe(ke, void 0), n = Fe(Re);
    n || $t(lt, "usage: <el-form-item><label-wrap /></el-form-item>");
    const a = Ue("form"), o = N(), i = N(0), s = () => {
      var d;
      if ((d = o.value) != null && d.firstElementChild) {
        const h = window.getComputedStyle(o.value.firstElementChild).width;
        return Math.ceil(Number.parseFloat(h));
      } else
        return 0;
    }, p = (d = "update") => {
      _t(() => {
        e.default && r.isAutoWidth && (d === "update" ? i.value = s() : d === "remove" && (t == null || t.deregisterLabelWidth(i.value)));
      });
    }, v = () => p("update");
    return He(() => {
      v();
    }), Tt(() => {
      p("remove");
    }), ar(() => v()), ie(i, (d, h) => {
      r.updateAll && (t == null || t.registerLabelWidth(d, h));
    }), Rt(I(() => {
      var d, h;
      return (h = (d = o.value) == null ? void 0 : d.firstElementChild) != null ? h : null;
    }), v), () => {
      var d, h;
      if (!e)
        return null;
      const {
        isAutoWidth: w
      } = r;
      if (w) {
        const A = t == null ? void 0 : t.autoLabelWidth, O = n == null ? void 0 : n.hasLabel, u = {};
        if (O && A && A !== "auto") {
          const g = Math.max(0, Number.parseInt(A, 10) - i.value), C = (n.labelPosition || t.labelPosition) === "left" ? "marginRight" : "marginLeft";
          g && (u[C] = `${g}px`);
        }
        return j("div", {
          ref: o,
          class: [a.be("item", "label-wrap")],
          style: u
        }, [(d = e.default) == null ? void 0 : d.call(e)]);
      } else
        return j(be, {
          ref: o
        }, [(h = e.default) == null ? void 0 : h.call(e)]);
    };
  }
});
const _a = oe({
  name: "ElFormItem"
}), xa = /* @__PURE__ */ oe({
  ..._a,
  props: wa,
  setup(r, { expose: e }) {
    const t = r, n = ir(), a = Fe(ke, void 0), o = Fe(Re, void 0), i = mt(void 0, { formItem: !1 }), s = Ue("form-item"), p = Nt().value, v = N([]), d = N(""), h = Ct(d, 100), w = N(""), A = N();
    let O, u = !1;
    const g = I(() => t.labelPosition || (a == null ? void 0 : a.labelPosition)), f = I(() => {
      if (g.value === "top")
        return {};
      const c = Ge(t.labelWidth || (a == null ? void 0 : a.labelWidth) || "");
      return c ? { width: c } : {};
    }), C = I(() => {
      if (g.value === "top" || a != null && a.inline)
        return {};
      if (!t.label && !t.labelWidth && te)
        return {};
      const c = Ge(t.labelWidth || (a == null ? void 0 : a.labelWidth) || "");
      return !t.label && !n.label ? { marginLeft: c } : {};
    }), l = I(() => [
      s.b(),
      s.m(i.value),
      s.is("error", d.value === "error"),
      s.is("validating", d.value === "validating"),
      s.is("success", d.value === "success"),
      s.is("required", Ee.value || t.required),
      s.is("no-asterisk", a == null ? void 0 : a.hideRequiredAsterisk),
      (a == null ? void 0 : a.requireAsteriskPosition) === "right" ? "asterisk-right" : "asterisk-left",
      {
        [s.m("feedback")]: a == null ? void 0 : a.statusIcon,
        [s.m(`label-${g.value}`)]: g.value
      }
    ]), m = I(() => dt(t.inlineMessage) ? t.inlineMessage : (a == null ? void 0 : a.inlineMessage) || !1), q = I(() => [
      s.e("error"),
      { [s.em("error", "inline")]: m.value }
    ]), F = I(() => t.prop ? Pe(t.prop) ? t.prop : t.prop.join(".") : ""), V = I(() => !!(t.label || n.label)), D = I(() => t.for || (v.value.length === 1 ? v.value[0] : void 0)), X = I(() => !D.value && V.value), te = !!o, Z = I(() => {
      const c = a == null ? void 0 : a.model;
      if (!(!c || !t.prop))
        return Me(c, t.prop).value;
    }), W = I(() => {
      const { required: c } = t, E = [];
      t.rules && E.push(...Ne(t.rules));
      const $ = a == null ? void 0 : a.rules;
      if ($ && t.prop) {
        const R = Me($, t.prop).value;
        R && E.push(...Ne(R));
      }
      if (c !== void 0) {
        const R = E.map((K, re) => [K, re]).filter(([K]) => Object.keys(K).includes("required"));
        if (R.length > 0)
          for (const [K, re] of R)
            K.required !== c && (E[re] = { ...K, required: c });
        else
          E.push({ required: c });
      }
      return E;
    }), U = I(() => W.value.length > 0), se = (c) => W.value.filter(($) => !$.trigger || !c ? !0 : ft($.trigger) ? $.trigger.includes(c) : $.trigger === c).map(({ trigger: $, ...R }) => R), Ee = I(() => W.value.some((c) => c.required)), je = I(() => {
      var c;
      return h.value === "error" && t.showMessage && ((c = a == null ? void 0 : a.showMessage) != null ? c : !0);
    }), b = I(() => `${t.label || ""}${(a == null ? void 0 : a.labelSuffix) || ""}`), _ = (c) => {
      d.value = c;
    }, x = (c) => {
      var E, $;
      const { errors: R, fields: K } = c;
      (!R || !K) && console.error(c), _("error"), w.value = R ? ($ = (E = R == null ? void 0 : R[0]) == null ? void 0 : E.message) != null ? $ : `${t.prop} is required` : "", a == null || a.emit("validate", t.prop, !1, w.value);
    }, M = () => {
      _("success"), a == null || a.emit("validate", t.prop, !0, "");
    }, k = async (c) => {
      const E = F.value;
      return new pe({
        [E]: c
      }).validate({ [E]: Z.value }, { firstFields: !0 }).then(() => (M(), !0)).catch((R) => (x(R), Promise.reject(R)));
    }, J = async (c, E) => {
      if (u || !t.prop)
        return !1;
      const $ = gt(E);
      if (!U.value)
        return E == null || E(!1), !1;
      const R = se(c);
      return R.length === 0 ? (E == null || E(!0), !0) : (_("validating"), k(R).then(() => (E == null || E(!0), !0)).catch((K) => {
        const { fields: re } = K;
        return E == null || E(!1, re), $ ? !1 : Promise.reject(re);
      }));
    }, L = () => {
      _(""), w.value = "", u = !1;
    }, me = async () => {
      const c = a == null ? void 0 : a.model;
      if (!c || !t.prop)
        return;
      const E = Me(c, t.prop);
      u = !0, E.value = rt(O), await _t(), L(), u = !1;
    }, Ae = (c) => {
      v.value.includes(c) || v.value.push(c);
    }, Oe = (c) => {
      v.value = v.value.filter((E) => E !== c);
    };
    ie(() => t.error, (c) => {
      w.value = c || "", _(c ? "error" : "");
    }, { immediate: !0 }), ie(() => t.validateStatus, (c) => _(c || ""));
    const le = bt({
      ...wt(t),
      $el: A,
      size: i,
      validateState: d,
      labelId: p,
      inputIds: v,
      isGroup: X,
      hasLabel: V,
      fieldValue: Z,
      addInputId: Ae,
      removeInputId: Oe,
      resetField: me,
      clearValidate: L,
      validate: J
    });
    return ht(Re, le), He(() => {
      t.prop && (a == null || a.addField(le), O = rt(Z.value));
    }), Tt(() => {
      a == null || a.removeField(le);
    }), e({
      size: i,
      validateMessage: w,
      validateState: d,
      validate: J,
      clearValidate: L,
      resetField: me
    }), (c, E) => {
      var $;
      return G(), Y("div", {
        ref_key: "formItemRef",
        ref: A,
        class: ue(y(l)),
        role: y(X) ? "group" : void 0,
        "aria-labelledby": y(X) ? y(p) : void 0
      }, [
        j(y(Ta), {
          "is-auto-width": y(f).width === "auto",
          "update-all": (($ = y(a)) == null ? void 0 : $.labelWidth) === "auto"
        }, {
          default: S(() => [
            y(V) ? (G(), we(or(y(D) ? "label" : "div"), {
              key: 0,
              id: y(p),
              for: y(D),
              class: ue(y(s).e("label")),
              style: Je(y(f))
            }, {
              default: S(() => [
                he(c.$slots, "label", { label: y(b) }, () => [
                  Ce(Q(y(b)), 1)
                ])
              ]),
              _: 3
            }, 8, ["id", "for", "class", "style"])) : Ke("v-if", !0)
          ]),
          _: 3
        }, 8, ["is-auto-width", "update-all"]),
        ae("div", {
          class: ue(y(s).e("content")),
          style: Je(y(C))
        }, [
          he(c.$slots, "default"),
          j(sr, {
            name: `${y(s).namespace.value}-zoom-in-top`
          }, {
            default: S(() => [
              y(je) ? he(c.$slots, "error", {
                key: 0,
                error: w.value
              }, () => [
                ae("div", {
                  class: ue(y(q))
                }, Q(w.value), 3)
              ]) : Ke("v-if", !0)
            ]),
            _: 3
          }, 8, ["name"])
        ], 6)
      ], 10, ["role", "aria-labelledby"]);
    };
  }
});
var jt = /* @__PURE__ */ pt(xa, [["__file", "form-item.vue"]]);
const Fa = It(Bn, {
  FormItem: jt
}), qa = Vt(jt), Ea = { class: "template-type-manager" }, ja = {
  class: "dialog-content",
  style: { "max-height": "calc(80vh - 120px)", overflow: "hidden" }
}, Aa = {
  class: "action-bar",
  style: { display: "flex", "justify-content": "space-between", "align-items": "center" }
}, Oa = { class: "type-count" }, Ma = { key: 1 }, Sa = { class: "dialog-footer" }, Pa = /* @__PURE__ */ oe({
  __name: "TemplateTypeManager",
  props: {
    visible: {
      type: Boolean,
      default: !1
    }
  },
  emits: ["update:visible", "close"],
  setup(r, { emit: e }) {
    const { t } = hr(), n = r, a = e, o = N(!1);
    ie(() => n.visible, (b) => {
      o.value = b, b && q();
    }), ie(() => o.value, (b) => {
      a("update:visible", b), b || a("close");
    });
    const i = N([]), s = N([]), p = N([]), v = N(!1), d = N("tab-0"), h = N({
      id: 0,
      name: "",
      count: 0
    });
    N(!1);
    const w = N([t("templateTypeManager.fullTemplate"), t("templateTypeManager.intextTemplate"), t("templateTypeManager.intableTemplate")]), A = N([]), O = N(!1), u = N(""), g = N(null), f = N(""), C = N(1), l = () => {
      const b = typeof d.value == "string" ? Number(d.value.split("-")[1] || 0) : 0;
      return [1, 2, 4][b] || 1;
    }, m = () => {
      const b = typeof d.value == "string" ? Number(d.value.split("-")[1] || 0) : 0;
      return w.value[b] || "";
    }, q = async () => {
      v.value = !0;
      try {
        const x = (await ge.get("/?r=template-subtype/list", {
          headers: {
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        })).data;
        if (x && x.status === 1) {
          const J = (x.data || []).map((L) => ({
            id: Number(L.id),
            name: L.name,
            main_type: Number(L.main_type),
            createTime: L.create_time || (/* @__PURE__ */ new Date()).toLocaleString()
          })).filter((L) => L.id !== 0);
          i.value = J.filter((L) => L.main_type === 1), s.value = J.filter((L) => L.main_type === 2), p.value = J.filter((L) => L.main_type === 4), V();
        } else
          H.error(x == null ? void 0 : x.info);
      } catch (b) {
        console.error("获取模板类型数据出错:", b), H.error(b.message);
      } finally {
        v.value = !1;
      }
    }, F = (b) => {
      switch (b) {
        case 0:
          return i.value;
        case 1:
          return s.value;
        case 2:
          return p.value;
        default:
          return [];
      }
    }, V = () => {
      const b = typeof d.value == "string" ? Number(d.value.split("-")[1] || 0) : 0;
      A.value = F(b), C.value = l();
    }, D = () => {
      o.value = !1, je(), a("close");
    }, X = (b) => {
      console.log("切换到标签页:", b.paneName);
      const _ = typeof b.paneName == "string" ? Number(b.paneName.split("-")[1] || 0) : 0;
      A.value = F(_);
      const x = [1, 2, 4];
      C.value = x[_] || 1;
    }, te = (b) => {
      g.value = b.id, f.value = b.name;
    }, Z = () => {
      g.value = null;
    }, W = async (b) => {
      if (!f.value.trim()) {
        H.warning(t("templateTypeManager.typeNameRequired"));
        return;
      }
      v.value = !0;
      try {
        const _ = {
          id: b.id,
          name: f.value,
          main_type: b.main_type
        }, x = await ge.post("/?r=template-subtype/update-subtype", _, {
          headers: {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        });
        if (console.log("更新响应数据:", x.data), !x.data || x.data.data.status !== 1)
          throw H.warning(x.data.data.info), new Error(x.data.data.info || t("templateTypeManager.updateFailure"));
        console.log(t("templateTypeManager.response"), x);
        const M = A.value.findIndex((k) => k.id === b.id);
        M !== -1 && (A.value[M].name = f.value), g.value = null, H({
          message: t("common.updateSuccess"),
          type: "success",
          offset: window.innerHeight / 6
        });
      } catch (_) {
        console.error(t("templateTypeManager.updateError"), _), H.error(`${t("templateTypeManager.updateError")}: ${_.message || t("common.unknownError")}`);
      } finally {
        v.value = !1;
      }
    }, U = async (b) => {
      var _;
      try {
        await cr.confirm(
          t("templateTypeManager.deleteConfirmMessage"),
          {
            confirmButtonText: t("common.delete"),
            cancelButtonText: t("common.cancel"),
            type: "warning"
          }
        ), v.value = !0;
        const x = {
          id: b.id,
          main_type: b.main_type
        }, M = await ge.post("/?r=template-subtype/delete-subtype", x, {
          headers: {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        });
        if (!M.data || M.data.status !== 1)
          throw new Error(((_ = M.data) == null ? void 0 : _.info) || t("templateTypeManager.deleteFailure"));
        q(), H({
          message: t("common.deleteSuccess"),
          type: "success",
          offset: window.innerHeight / 6
        });
      } catch (x) {
        if (x === "cancel" || x.toString().includes("cancel"))
          return;
        console.error(t("templateTypeManager.deleteError"), x), H.error(`${t("templateTypeManager.deleteError")}: ${x.message || t("common.unknownError")}`), v.value = !1;
      }
    }, se = () => {
      O.value = !0, u.value = "";
    }, Ee = async () => {
      var x;
      if (!u.value.trim()) {
        H.warning(t("templateTypeManager.enterTypeName"));
        return;
      }
      const b = u.value.split(`
`).map((M) => M.trim()).filter((M) => M.length > 0);
      if (b.length === 0) {
        H.warning(t("templateTypeManager.enterValidTypeName"));
        return;
      }
      const _ = C.value;
      v.value = !0;
      try {
        const M = {
          names: b,
          main_type: _
        };
        console.log(t("templateTypeManager.batchAddRequestData"), M);
        const k = await ge.post("/?r=template-subtype/batch-add-sub-types", M, {
          headers: {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest"
          }
        });
        if (console.log(t("templateTypeManager.batchAddResponse"), k.data), !k.data || k.data.status !== 1)
          throw new Error(((x = k.data) == null ? void 0 : x.info) || t("templateTypeManager.addFailure"));
        O.value = !1, q(), H({
          message: k.data.data.message,
          type: "success",
          offset: window.innerHeight / 6
        });
      } catch (M) {
        console.error(t("templateTypeManager.batchAddError"), M), H.error(`${t("templateTypeManager.batchAddError")}: ${M.message || t("common.unknownError")}`), v.value = !1;
      }
    }, je = () => {
      h.value = {
        id: 0,
        name: "",
        count: 0
      };
    };
    return He(() => {
      o.value = !0, console.log("模板类型管理组件已挂载，visible:", n.visible), o.value && (console.log("初始化时visible为true，立即获取数据"), q());
    }), (b, _) => {
      const x = Lt, M = tr, k = Bt, J = nr, L = Dt, me = rr, Ae = qa, Oe = Fa, le = Wt;
      return G(), Y("span", Ea, [
        j(y(Ze), {
          modelValue: o.value,
          "onUpdate:modelValue": _[2] || (_[2] = (c) => o.value = c),
          title: y(t)("templateTypeManager.title"),
          width: "600px",
          "before-close": D,
          class: "template-dialog",
          "append-to-body": "",
          "destroy-on-close": "",
          style: { height: "auto", overflow: "hidden" }
        }, {
          default: S(() => [
            ae("div", ja, [
              j(y(fr), {
                modelValue: d.value,
                "onUpdate:modelValue": _[1] || (_[1] = (c) => d.value = c),
                class: "template-tab",
                onTabClick: X,
                "tab-position": "left"
              }, {
                default: S(() => [
                  (G(!0), Y(be, null, lr(w.value, (c, E) => (G(), we(y(dr), {
                    key: E,
                    label: c,
                    name: "tab-" + E
                  }, {
                    default: S(() => [
                      ae("div", Aa, [
                        ae("div", Oa, Q(y(t)("templateTypeManager.existingTypes")) + " : (" + Q(A.value.length) + ")", 1),
                        j(M, {
                          plain: "",
                          class: "add-template-btn",
                          onClick: se
                        }, {
                          default: S(() => [
                            j(x, null, {
                              default: S(() => [
                                j(y(pr))
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        })
                      ]),
                      ur((G(), we(me, {
                        data: A.value,
                        "element-loading-text": y(t)("common.loading"),
                        "row-style": { height: "36px" },
                        "cell-style": { padding: "0", height: "36px" },
                        "empty-text": y(t)("templateTypeManager.noData"),
                        "max-height": "calc(60vh - 200px)",
                        style: { "overflow-y": "auto" }
                      }, {
                        default: S(() => [
                          j(J, { prop: "name" }, {
                            default: S(($) => [
                              g.value === $.row.id ? (G(), we(k, {
                                key: 0,
                                modelValue: f.value,
                                "onUpdate:modelValue": _[0] || (_[0] = (R) => f.value = R),
                                size: "small",
                                placeholder: y(t)("templateTypeManager.typePlaceholder")
                              }, null, 8, ["modelValue", "placeholder"])) : (G(), Y("span", Ma, Q($.row.name), 1))
                            ]),
                            _: 1
                          }),
                          j(J, {
                            width: "100",
                            align: "right"
                          }, {
                            default: S(($) => [
                              g.value === $.row.id ? (G(), Y(be, { key: 0 }, [
                                j(M, {
                                  size: "small",
                                  type: "success",
                                  circle: "",
                                  class: "edit-mode-button",
                                  onClick: (R) => W($.row)
                                }, {
                                  default: S(() => [
                                    j(x, null, {
                                      default: S(() => [
                                        j(y(mr))
                                      ]),
                                      _: 1
                                    })
                                  ]),
                                  _: 2
                                }, 1032, ["onClick"]),
                                j(M, {
                                  size: "small",
                                  type: "info",
                                  circle: "",
                                  class: "edit-mode-button",
                                  onClick: Z
                                }, {
                                  default: S(() => [
                                    j(x, null, {
                                      default: S(() => [
                                        j(y(gr))
                                      ]),
                                      _: 1
                                    })
                                  ]),
                                  _: 1
                                })
                              ], 64)) : (G(), Y(be, { key: 1 }, [
                                j(L, {
                                  content: y(t)("templateTypeManager.rename"),
                                  placement: "top",
                                  effect: "dark"
                                }, {
                                  default: S(() => [
                                    j(M, {
                                      size: "small",
                                      circle: "",
                                      plain: "",
                                      class: "normal-mode-button",
                                      onClick: (R) => te($.row)
                                    }, {
                                      default: S(() => [
                                        j(x, null, {
                                          default: S(() => [
                                            j(y(vr))
                                          ]),
                                          _: 1
                                        })
                                      ]),
                                      _: 2
                                    }, 1032, ["onClick"])
                                  ]),
                                  _: 2
                                }, 1032, ["content"]),
                                j(L, {
                                  content: y(t)("templateTypeManager.delete"),
                                  placement: "top",
                                  effect: "dark"
                                }, {
                                  default: S(() => [
                                    j(M, {
                                      size: "small",
                                      circle: "",
                                      plain: "",
                                      class: "normal-mode-button",
                                      onClick: (R) => U($.row)
                                    }, {
                                      default: S(() => [
                                        j(x, null, {
                                          default: S(() => [
                                            j(y(yr))
                                          ]),
                                          _: 1
                                        })
                                      ]),
                                      _: 2
                                    }, 1032, ["onClick"])
                                  ]),
                                  _: 2
                                }, 1032, ["content"])
                              ], 64))
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }, 8, ["data", "element-loading-text", "empty-text"])), [
                        [le, v.value]
                      ])
                    ]),
                    _: 2
                  }, 1032, ["label", "name"]))), 128))
                ]),
                _: 1
              }, 8, ["modelValue"])
            ])
          ]),
          _: 1
        }, 8, ["modelValue", "title"]),
        j(y(Ze), {
          modelValue: O.value,
          "onUpdate:modelValue": _[5] || (_[5] = (c) => O.value = c),
          title: y(t)("templateTypeManager.createType", { type: m() }),
          width: "300px",
          style: { maxHeight: "50vh", overflow: "hidden" },
          "append-to-body": "",
          "destroy-on-close": "",
          class: "batch-add-dialog"
        }, {
          footer: S(() => [
            ae("span", Sa, [
              j(M, {
                onClick: _[4] || (_[4] = (c) => O.value = !1)
              }, {
                default: S(() => [
                  Ce(Q(y(t)("common.cancel")), 1)
                ]),
                _: 1
              }),
              j(M, {
                type: "primary",
                onClick: Ee,
                loading: v.value
              }, {
                default: S(() => [
                  Ce(Q(y(t)("common.confirm")), 1)
                ]),
                _: 1
              }, 8, ["loading"])
            ])
          ]),
          default: S(() => [
            j(Oe, null, {
              default: S(() => [
                j(Ae, null, {
                  default: S(() => [
                    j(k, {
                      modelValue: u.value,
                      "onUpdate:modelValue": _[3] || (_[3] = (c) => u.value = c),
                      type: "textarea",
                      rows: 10,
                      placeholder: y(t)("templateTypeManager.typeNamePlaceholder")
                    }, null, 8, ["modelValue", "placeholder"])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["modelValue", "title"])
      ]);
    };
  }
}), Ba = /* @__PURE__ */ br(Pa, [["__scopeId", "data-v-b0be3db4"]]);
export {
  Ba as default
};
