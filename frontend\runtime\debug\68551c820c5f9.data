a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:56:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:54:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:48:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:50:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:52:"D:\integle2025\eln_trunk\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:14:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"12";s:6:"pragma";s:8:"no-cache";s:13:"cache-control";s:8:"no-cache";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750408321453";}s:15:"responseHeaders";a:2:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:28:"async-task/create-redis-data";s:6:"action";s:65:"frontend\controllers\AsyncTaskController::actionCreateRedisData()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:12:"user_id=1135";s:17:"Decoded to Params";a:1:{s:7:"user_id";s:4:"1135";}}s:6:"SERVER";a:47:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"12";s:11:"HTTP_PRAGMA";s:8:"no-cache";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:217:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; dataview_id=101; page_type=1; last_active_time=1750408321453";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:37:"D:/integle2025/eln_trunk/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:47:"D:/integle2025/eln_trunk/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"64405";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:30:"r=async-task/create-redis-data";s:11:"REQUEST_URI";s:32:"/?r=async-task/create-redis-data";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1750408321.6670001;s:12:"REQUEST_TIME";i:1750408321;}s:3:"GET";a:1:{s:1:"r";s:28:"async-task/create-redis-data";}s:4:"POST";a:1:{s:7:"user_id";s:4:"1135";}s:6:"COOKIE";a:9:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:15:"center_language";s:2:"CN";s:11:"dataview_id";s:3:"101";s:9:"page_type";s:1:"1";s:16:"last_active_time";s:13:"1750408321453";}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}s:3:"log";a:1:{s:8:"messages";a:15:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750408321.8183119;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750408321.8535969;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750408322.0520179;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750408322.0536489;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1750408322.056109;i:4;a:0:{}}i:5;a:5:{i:0;s:47:"Route requested: 'async-task/create-redis-data'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1750408322.0561421;i:4;a:0:{}}i:6;a:5:{i:0;s:42:"Route to run: async-task/create-redis-data";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1750408322.067683;i:4;a:0:{}}i:7;a:5:{i:0;s:81:"Running action: frontend\controllers\AsyncTaskController::actionCreateRedisData()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1750408322.0687709;i:4;a:0:{}}i:8;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:1750408322.486043;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:595;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408322.5024779;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:595;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408322.5030899;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:595;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408322.5038919;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:2095;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:595;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408323.751138;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1316;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1316;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:598;s:8:"function";s:24:"getGroupsListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408323.9955921;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1820;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1820;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:601;s:8:"function";s:28:"getDepartmentListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750408326.0618789;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1237;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_trunk\frontend\interfaces\CenterInterface.php";s:4:"line";i:1237;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_trunk\frontend\controllers\AsyncTaskController.php";s:4:"line";i:604;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}}}s:9:"profiling";a:3:{s:6:"memory";i:11474296;s:4:"time";d:4.2995729446411133;s:8:"messages";a:0:{}}s:2:"db";a:1:{s:8:"messages";a:0:{}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"68551c820c5f9";s:3:"url";s:58:"http://dev.eln.integle.com/?r=async-task/create-redis-data";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750408326;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:9:"mailCount";i:0;}}