<style>


    .container .main {
        min-height: 790px;
    }

    .instrumentOperationStyle{
        margin：0 auto;
        max-width: 1550px;
    }

    .fixHeaderOuter .thead-fixed {
        position: sticky; /*固定表头*/
        top: 0px;
        z-index: 1
    }

    .go-to {
        margin-right: 6px;
    }

    .page_box {
        margin-right: 6px;
        margin-top: 10px;
    }

    .instrument-more-filter-item {
        width: 240px;
    }
    /*instrument-more-filter-item的width*2加上15的margin*/
    .instrument-more-filter-item-2 {
        width: 495px;
    }
    .instrument-label {
        width: 70px;
        display: inline-block;
        text-align: right;
    }
    .viewBookInstruments {
        margin-right: 15px;
        margin-bottom: 7px;
        height: 35px;
    }

    .instruments-header {
        display: flex;
        margin-top: -58px;
        justify-content: space-between
    }

    .flex-container {
        display: flex !important;
    }
    .book-instrument-display{
        display: flex;
        justify-content: space-between;
    }
</style>


<div class="exp_data_box clear">
    <link href="../css/bin/cropper.min.css?ver=<?= Yii::$app->params['res_version'] ?>" rel="stylesheet"/>
    <link href="../css/bin/new_setting.css?ver=<?= Yii::$app->params['res_version'] ?>" rel="stylesheet"/>

    <?php if ($type == "my_instruments"): ?>
        <div class="top-group-tabs book-instrument-display" style="margin-top: -58px">
            <ul>
                <li class="top-group-tab on eln_setting_btn" data-id="1" method='changeMyInstruments'>
                    <?= Yii::t('base', 'my_instruments'); ?>
                </li>
                <li class="top-group-tab eln_setting_btn" data-id="2" method='changeMyInstruments'>
                    <?= Yii::t('base', 'my_instruments_reservation'); ?>
                </li>
            </ul>
            <!--仪器预约-->
            <div class="iblock exp-open-log paddingTop6 marginRight15">
                <input type="button" value="<?= Yii::t('base', 'booking_instruments') ?>"
                       class="btn blue-btn viewBookInstruments" data-type="<?= $type ?>">
            </div>
        </div>
        <!-- Vue组件挂载点 -->
        <div id="book-app" style="display:none;"></div>
    <?php endif; ?>
    <?php if ($type == "instruments_manage"): ?>
        <div class="top-group-tabs book-instrument-display" style="margin-top: -58px">
            <ul>

                <li class="top-group-tab on eln_setting_btn" data-id="3" method='changeMyInstruments'>
                    <?= Yii::t('base', 'instruments_list'); ?>
                </li>
                <li class="top-group-tab eln_setting_btn" data-id="4" method='changeMyInstruments'>
                    <?= Yii::t('base', 'instruments_booking_record'); ?>
                </li>
            </ul>
        </div>

    <?php endif; ?>
    <div class=" sp clear " style="height: 100%;">

        <div class="clear instruments_manage_style">

            <style>
                .instruments_manage_style {
                    padding: 0 15px;
                }

                .instruments_manage_style .instruments_table .exp_data_box td {
                    line-height: 30px;
                    height: 80px;
                }

                .instruments_manage_style .blue-btn {
                    background: #3499ff;
                    color: #fff;
                    width: auto;
                    height: 32px;
                    border-radius: 3px;
                    border: 1px
                }

                .instruments_manage_style .marginRight20 {
                    margin-right: 20px;
                }

                .instruments_manage_style .marginRight15 {
                    margin-right: 15px;
                }

                .instrumentMoreFilter .fl {
                    line-height: 30px;
                }

                .instruments_manage_style .data-box-ineln .check-search-box .part-right {
                    width: 150px;
                }

                .instruments_manage_style .data-box-ineln .check-search-box .part-right .search-check-key {
                    width: 80px;
                    top: 5px;
                }

                /*bug#31606,修改所属鹰群筛选的搜索框样式*/
                .instruments_manage_style .instrument-more-filter-item .data-box-ineln .check-search-box .part-right .search-check-key {
                    width: 220px;
                    margin-right: 7px;
                }
                /*[Bug]#1493 plug，仪器库管理，所属鹰群下拉框中眼睛icon和搜索框重叠了*/
                .instruments_manage_style .instrument-more-filter-item .data-box-ineln .check-search-box .part-right {
                    width: 250px;
                }

                .instruments_manage_style .insAddStyle {
                    border: 1px solid #3499ff;
                    background: #3499ff;
                    border-radius: 4px;
                    color: white;
                    display: inline-block;
                    text-align: center;
                    margin-top: 6px;
                    cursor: pointer;
                    margin-right: 15px;
                    width: fit-content;
                    padding: 5px 10px;
                }

                .instruments_manage_style .instrument_input_sign {
                    width: 120px;
                }

                .instruments_manage_style .fs-wrap,
                .instruments_manage_style .fs-dropdown,
                .instruments_manage_style .fs-label-wrap {
                    width: 120px;
                }

                .batchAdjust {
                    position: relative;
                }

                .batchAdjust .batch-adjust-menu {
                    display: none;
                    right: 100px;
                    width: 100px;
                    top: 0px;
                    background: #fff;
                    border: 1px solid #ccc;
                    box-shadow: 0 1px 3px #aaa;
                    color: #222;
                }

                .batchAdjust .batch-adjust-menu li:hover {
                    background: #f2f2f2;
                }

                .batchAdjust:hover .batch-adjust-menu {
                    display: block;
                }

                .instruments_manage_style .left-expand {
                    width: 0;
                    height: 0;
                    border: 7px solid transparent;
                    position: absolute;
                    left: 5px;
                    border-right-color: #ccc;
                    bottom: 7px;
                }

            </style>

            <div class="ineln_title1 sp clear"
                 style="margin-bottom:5px;height:auto;padding: 0;">
                <div name="search-instrument-manage" id="search-instrument-manage">

                    <!--仪器名称改成关键字-->
                    <div class="iblock paddingTop6 marginRight15 instrument-more-filter-item fl" >
                        <label class="label mt5 instrument-label"><?= Yii::t('base', 'keywords'); ?>：</label>
                        <input type="text" name="instrument_name" class="iblock instrument_name instrument_input_sign"
                               value="<?=@getVar($filter['name'])?>" autocomplete="off"/>
                    </div>

                    <!--状态-->
                    <div class="iblock paddingTop6 marginRight15 instrument-more-filter-item fl">
                        <label class="label mt5 instrument-label"><?= Yii::t('base', 'status'); ?>：</label>
                        <span class="iblock relative">
                            <select class="angle_input iblock beauty-disabled-select" name="instrument_status"
                                    id="instrument_status"
                                    style="width:120px;overflow: hidden">
                                 <option value=""><?= Yii::t('base', 'all'); ?></option>
                                 <option value="1"><?= Yii::t('base', 'normal'); ?></option>
                                 <option value="2"><?= Yii::t('base', 'suspend_use'); ?></option>
                                 <option value="4"><?= Yii::t('base', 'scrap'); ?></option>
                                 <option value="3"><?= Yii::t('base', 'repairing'); ?></option>
                                 <option value="0"><?= Yii::t('base', 'already_deleted'); ?></option>
                             </select>
                        </span>
                    </div>

                    <!--更多筛选-->
                    <div class="iblock paddingTop6 marginRight15">
                        <input type="button" value="<?= Yii::t('base', 'more_filter'); ?>"
                               class="btn blue-btn instrument_need_more_filter" style="height: 32px;padding: 5px 10px;">

                    </div>

                    <!--搜索-->
                    <div class="iblock exp-open-log paddingTop6 marginRight15">
                        <input type="button" value="<?= Yii::t('base', 'search'); ?>"
                               class="search-instrument-manage  btn blue-btn " data-type="<?= $type ?>">
                    </div>

                    <!--重置-->
                    <div class="iblock paddingTop6">
                        <input type="button" value="<?= Yii::t('base', 'reset'); ?>"
                               class="reset-instrument-manage  btn blue-btn "">
                    </div>

                    <!--工具栏-->
                    <div class="iblock fr together-tools paddingTop6 "
                         style="top: -6px;position: relative;line-height: 20px!important;">

                        <?php if ($type == "instruments_manage" && ($isCanWrite)): ?>
                            <div style="line-height: 20px" class="insAddStyle<?php if ($isCanWrite) {
                                echo " operateInstrument";
                            } ?>" data-type="add"><?= Yii::t('base', 'add_instrument'); ?></div>
                            <div style="line-height: 20px" class="insAddStyle<?php if ($isCanWrite) {
                                echo " addInstrumentCol";
                            } ?>" title="<?= Yii::t('base', 'add_column_tip'); ?>"
                                 data-defineColIndex="1"><?= Yii::t('base', 'add_column'); ?></div>
                        <?php endif; ?>
                        <!-- 更多设置-->
                        <div class="clear relative iblock middle eln_setting_btn" hMethod="hoverToggle"
                             style="vertical-align: bottom;margin-top: 6px">
                            <a class="btn blue-btn mouse fl" href="javascript:void(0)"
                               style="width: 120px;padding-left: 5px;padding-top:7px"><?= Yii::t('common', 'more'); ?></a>
                            <div class="down-box fl" style="width: 0">
                                <div class="absolute">
                                    <div class="opt-more ul">

                                        <span class="block org pointer group-opt-btn operateInstrumentExport <?= $type == "instruments_manage" ? '' : 'my-instruments' ?>"
                                              data-type="export"><?= Yii::t('base', 'export'); ?></span>
                                        <span class="block org pointer group-opt-btn viewUseGraph"><?= Yii::t('base', 'instrument_usage_graph'); ?></span>
                                        <span class="block org pointer group-opt-btn viewBookGraph"><?= Yii::t('base', 'instrument_book_graph'); ?></span>
                                        <span class="block org pointer group-opt-btn viewInscadaSummary <?= $type == "instruments_manage" ? '' : 'my-instruments' ?>" data-type="inscada"><?= Yii::t('base', 'InScada'); ?></span> <!--仪器对接-->

                                        <?php if ($type == "instruments_manage" && ($isCanWrite)): ?>
                                            <span class="block org pointer group-opt-btn <?php if ($isCanWrite) {
                                                echo " operateInstrument";
                                            } ?>" data-type="batchAdd"><?= Yii::t('base', 'batchAdd'); ?></span>
                                            <span class="block org pointer group-opt-btn <?php if ($isCanWrite) {
                                                echo " batchEdit";
                                            } ?>" data-type="batchAdd"><?= Yii::t('base', 'batch_edit'); ?></span>
                                            <span class="block org pointer group-opt-btn batchDelete"><?= Yii::t('base', 'batch_delete'); ?></span>
                                            <span class="block org pointer group-opt-btn batchAdjust">
                                                <i class="left-expand" style="left: -5px;"></i>
                                                <?= Yii::t('base', 'batch_adjust'); ?>
                                                <div class="batch-adjust-menu" style="position: absolute;right: 120px;">
                                                    <ul>
                                                        <li class="batch-change"
                                                            data-type="group"><?= Yii::t('base', 'group'); ?></li>
                                                        <li class="batch-change"
                                                            data-type="department"><?= Yii::t('base', 'department'); ?></li>
                                                        <li class="batch-change-status"
                                                            data-type="status"><?= Yii::t('base', 'status'); ?></li>
                                                        <li class="batch-change" data-type="instrument_type"
                                                            data-typeList="<?= implode(',', array_column($typeList, 'dict_value')); ?>"><?= Yii::t('base', 'instrument_type'); ?></li>
                                                        <li class="batch-change"
                                                            data-type="person_in_charge"><?= Yii::t('base', 'person_in_charge'); ?></li>
                                                        <li class="batch-change"
                                                            data-type="response_person"><?= Yii::t('base', 'response_person'); ?></li>
                                                        <li class="batch-change"
                                                            data-type="maintainer"><?= Yii::t('base', 'maintainer'); ?></li>
                                                    </ul>
                                                </div>
                                            </span>
                                            <span class="block org pointer group-opt-btn <?php if ($isCanWrite) {
                                                echo " fieldConfig";
                                            } ?>"><?= Yii::t('base', 'select_valid_fields'); ?></span> <!--字段配置-->
                                            <span class="block org pointer group-opt-btn <?php if ($isCanWrite) {
                                                echo " recordFieldSetting";
                                            } ?>"><?= Yii::t('base', 'recordFieldConfig'); ?></span> <!--仪器记录字段配置-->
                                            <span class="block org pointer group-opt-btn recordBatchSetting"><?= Yii::t('base', 'checkTimeSetting'); ?></span> <!--仪器记录复核时间配置-->
                                            <span class="block org pointer group-opt-btn reservationRuleSettings"><?= Yii::t('base', 'reservationRuleSettings'); ?></span> <!--预约规则设置-->
                                        <?php endif; ?>


                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="submit_wo_ico_block fr instrumentShowHideColumn" style="margin-left: 5px;margin-top: 6px;"
                             data-tableId="instrument_<?= ($type == "instruments_manage" ? "manage" : "mine") ?>_table_showHideColumn">
                            <a class="display_hidden_column" title="<?= Yii::t('base', 'show_hidden'); ?>" href="javascript:void(0)" style="margin-top: -2px;margin-left: -3px;"></a>
                        </div>
                    </div>
                    <div style="margin-top: 10px;" class="instrumentMoreFilter hide">


                        <!--创建人-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">

                            <label class="label mt5 instrument-label"  title="<?= Yii::t('base', 'create_person'); ?>"><?= Yii::t('base', 'create_by_tips'); ?>：</label>
                            <?php
                            echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_user_select_tree.php', [
                                'selectedUsersIds' => [],
                                'selectedUsersNames' => [],
                                'inputName' => 'instrument_create_user',
                                'width' => '120px',
                                'multiple' => false,
                            ]);
                            ?>
                        </div>

                        <!--分类-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label" ><?= Yii::t('base', 'instrument_type'); ?>：</label>
                            <span class="iblock relative">
                                    <select class="angle_input iblock beauty-disabled-select" name="instrument_type"
                                            id="instrument_type"
                                            style="width:120px;overflow: hidden">
                                        <option value="0"><?= Yii::t('base', 'all'); ?></option>
                                        <?php foreach ($typeList as $insType): ?>
                                            <option value="<?= $insType['dict_value'] ?>"><?= $insType['dict_value'] ?></option>
                                        <?php endforeach; ?>

                                     </select>
                                </span>
                        </div>

                        <!--维护人-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">

                            <label class="label mt5 instrument-label"
                                   ><?= Yii::t('base', 'person_in_charge'); ?>：</label>
                            <?php
                            echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_user_select_tree.php', [
                                'selectedUsersIds' => [],
                                'selectedUsersNames' => [],
                                'inputName' => 'instrument_in_charge_user',
                                'width' => '120px',
                                'multiple' => false,
                            ]);
                            ?>
                        </div>

                        <!--所属鹰群-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">

                            <label class="label mt5 instrument-label" ><?= Yii::t('base', 'group_search'); ?>：</label>
                            <?php
                            echo $this->renderFile(Yii::$app->getViewPath() . '/components/multi_group_select.php', [
                                'selectedGroupIds' => [],
                                'selectedGroupNames' => [],
                                'inputName' => 'instrument_belong_group',
                                'width' => '120px',
                                'searchWidth' => ''
                            ]);
                            ?>
                        </div>

                        <!--维修人-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">

                            <label class="label mt5 instrument-label" ><?= Yii::t('base', 'maintainer'); ?>：</label>
                            <?php
                            echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_user_select_tree.php', [
                                'selectedUsersIds' => [],
                                'selectedUsersNames' => [],
                                'inputName' => 'instrument_maintenance_user',
                                'width' => '120px',
                                'multiple' => false,
                            ]);
                            ?>
                        </div>

                        <!--位置-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'position'); ?>：</label>
                            <input type="text" class="iblock instrument_position instrument_input_sign"
                                   name="instrument_position"/>
                        </div>

                        <!--责任人-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">


                            <label class="label mt5 instrument-label" ><?= Yii::t('base', 'response_person_1'); ?>：</label>
                            <?php
                            echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_user_select_tree.php', [
                                'selectedUsersIds' => [],
                                'selectedUsersNames' => [],
                                'inputName' => 'instrument_responsible_user',
                                'width' => '120px',
                                'multiple' => false,
                            ]);
                            ?>
                        </div>

                        <!--所属部门-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">

                            <label class="label mt5 instrument-label"
                                   ><?= Yii::t('base', 'belong_department'); ?>：</label>

                            <?php echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_dept_select_tree.php', [
                                'selectedDeptIds' => [],
                                'width' => '120px',
                                'name' => 'instrument_belong_department',
                                'multiple' => true,
                                'showHistory' => true,
                                'status' => '1,2',
                                'independentTreeNode' => true,
                            ]);
                            ?>
                        </div>

                        <!--供应商-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'supplier'); ?>：</label>
                            <input type="text" class="iblock instrument_supplier instrument_input_sign"
                                   name="instrument_supplier"/>
                        </div>

                        <!--制造商-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label" title="<?= Yii::t('base', 'manufacturer'); ?>"><?= Yii::t('base', 'manufacturer_tips'); ?>：</label>
                            <input type="text" class="iblock instrument_manufacturer instrument_input_sign"
                                   name="instrument_manufacturer"/>
                        </div>

                        <!--备注-->
                        <div class="fl paddingTop6 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'remark'); ?>：</label>
                            <input type="text" class="iblock instrument_remark instrument_input_sign"
                                   name="instrument_remark"/>
                        </div>

                        <!--校验情况-->
                        <div class="fl paddingTop6 marginRight10 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'check_situation'); ?>：</label>
                            <span class="iblock relative search-btn-box">
                            <select class="angle_input iblock beauty-disabled-select" name="instrument_check"
                                    id="instrument_check"
                                    style="width:120px;overflow: hidden">
                                <option value=""><?= Yii::t('base', 'all'); ?></option>
                                <option value="1"><?= Yii::t('base', 'checked'); ?></option>
                                <option value="2"><?= Yii::t('base', 'unchecked'); ?></option>
                                <option value="0"><?= Yii::t('base', 'noNeedCheck'); ?></option>
                            </select>
                        </span>
                        </div>

                        <!--对接类型-->
                        <div class="fl paddingTop6 marginRight10 marginRight15 instrument-more-filter-item">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'data_type'); ?>：</label>
                            <span class="iblock relative search-btn-box">
                            <select class="angle_input iblock beauty-disabled-select" name="instrument_data_type"
                                    id="instrument_data_type"
                                    style="width:120px;overflow: hidden">
                                <option value=""><?= Yii::t('base', 'all'); ?></option>
                                <option value="1"><?= Yii::t('base', 'value'); ?></option>
                                <option value="2"><?= Yii::t('base', 'file'); ?></option>
                                <option value="0"><?= Yii::t('base', 'none'); ?></option>
                            </select>
                        </span>
                        </div>

                        <!--创建时间-->
                        <div class="fl paddingTop6 instrument_manage marginRight15 instrument-more-filter-item-2">
                            <label class="label mt5 instrument-label"><?= Yii::t('base', 'create_time'); ?>：</label>
                            <input type="text" id="start_time" class="angle_input time_input iblock" autocomplete="off"
                                   name="start_time" maxlength="20" value="" style="width: 112px">
                            <i class="date-picker-ico font-ico"></i>
                            <span class="time_to">-</span>
                            <input type="text" id="end_time" class="angle_input time_input iblock" autocomplete="off"
                                   name="end_time" maxlength="20" value="" style="width: 112px">
                            <i class="date-picker-ico font-ico "></i>
                        </div>


                    </div>

                </div>

            </div>


            <div class="instruments_table">
                <?= $this->renderFile(Yii::$app->getViewPath() . '/setting/instruments_manage_page.php', [
                    'instrumentsList' => $instrumentsList,
                    'defineFields' => $defineFields,
                    'limit' => $limit,
                    'totalCount' => $totalCount,
                    'type' => $type,
                    'isCanWrite' => $isCanWrite,
                    'page' => $page, // add by hkk 2020/8/3
                    'fieldConfigShowFields' => $fieldConfigShowFields, // add by hkk 2022/8/8 字段配置
                    'allGroups' => $allGroups,
                    'allDepartments' => $allDepartments,
                    'unCheckedIds' => $unCheckedIds,
                    'checkedIds' => $checkedIds,
                ]); ?>
            </div>
        </div>

    </div>

</div>

<script>

    // 监听关键字回车搜索事件
    var searchKeywords = $('#search-instrument-manage input[name=instrument_name]');
    searchKeywords.on('keyup', function (event) {
        if (event.keyCode === 13) {
            $('.exp_conetnt.active .search-instrument-manage').trigger('click');
        }
    });


</script>
