/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BookedDayDetailsTable: typeof import('./components/bookedDayDetailsTable.vue')['default']
    BookedWeekDetailsTable: typeof import('./components/bookedWeekDetailsTable.vue')['default']
    BookInstruments: typeof import('./components/BookInstruments.vue')['default']
    DeleteBookingDialog: typeof import('./components/DeleteBookingDialog.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    InFormContainer: typeof import('./components/in-form/InFormContainer.vue')['default']
    InFormDemo: typeof import('./components/in-form/InFormDemo.vue')['default']
    InFormLayout: typeof import('./components/in-form/InFormLayout.vue')['default']
    InstrumentBookingConfigDialog: typeof import('./components/InstrumentBookingConfigDialog.vue')['default']
    InstrumentBookingCreateDialog: typeof import('./components/InstrumentBookingCreateDialog.vue')['default']
    InstrumentsBookManage: typeof import('./components/instrumentsBookManage/instrumentsBookManage.vue')['default']
    InstrumentsBookManageRightMenu: typeof import('./components/instrumentsBookManage/instrumentsBookManageRightMenu.vue')['default']
    InstrumentsBookManageToolBar: typeof import('./components/instrumentsBookManage/InstrumentsBookManageToolBar.vue')['default']
    InstrumentsBookMine: typeof import('./components/instrumentsBookMine/instrumentsBookMine.vue')['default']
    InstrumentsBookMineRightMenu: typeof import('./components/instrumentsBookMine/instrumentsBookMineRightMenu.vue')['default']
    InstrumentsBookMineToolBar: typeof import('./components/instrumentsBookMine/InstrumentsBookMineToolBar.vue')['default']
    Loading: typeof import('./components/loading/Loading.vue')['default']
    MenuItem: typeof import('./components/context-menu/MenuItem.vue')['default']
    ModalA: typeof import('./components/ModalA.vue')['default']
    ModalB: typeof import('./components/ModalB.vue')['default']
    OperationButton: typeof import('./components/in-form/operation-tool-bar/OperationButton.vue')['default']
    OperationToolBar: typeof import('./components/in-form/operation-tool-bar/OperationToolBar.vue')['default']
    TemplateGeneralSettings: typeof import('./components/template/TemplateGeneralSettings.vue')['default']
    TemplateHistoryDialog: typeof import('./components/template/TemplateHistoryDialog.vue')['default']
    TemplateTypeManager: typeof import('./components/template/TemplateTypeManager.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
