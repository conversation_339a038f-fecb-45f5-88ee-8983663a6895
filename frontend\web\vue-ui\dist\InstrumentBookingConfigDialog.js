import { ref as p, createElementBlock as g, openBlock as m, createVNode as n, unref as o, withCtx as i, createElementVNode as N, toDisplayString as v, Fragment as b, renderList as V, createBlock as c, createCommentVNode as S, createTextVNode as f } from "vue";
/* empty css      */
import { ElDialog as W, ElForm as X, ElFormItem as _, ElTooltip as Y, ElSelect as h, ElOption as w, ElIcon as j, ElRadioGroup as U, ElRadio as k, ElInput as T, ElButton as R, ElMessage as x } from "element-plus";
import { p as Z, m as ee } from "./index2.js";
import { u as le } from "./vue-i18n.js";
import { _ as oe } from "./_plugin-vue_export-helper.js";
const te = { id: "isInstrumentBookingConfigDialogDialogDiv" }, ae = { class: "isInstrumentBookingConfigDialog_firstP" }, ne = {
  key: 2,
  style: { "min-width": "22px", "margin-top": "11px" }
}, ie = {
  __name: "InstrumentBookingConfigDialog",
  props: {
    instrument_booking_config_chooseIds: {
      type: Array,
      default: []
    },
    instrument_booking_config_chooseName: {
      type: Array,
      default: []
    },
    cancel: {
      type: Function,
      default: null
    }
  },
  setup(F) {
    const { t: r } = le(), E = p(!0), I = p(!1), y = p(!1), A = p([
      { value: "min", label: r("InstrumentBookingConfigDialog.min") },
      { value: "hour", label: r("InstrumentBookingConfigDialog.hour") },
      { value: "day", label: r("InstrumentBookingConfigDialog.day") }
    ]), C = p(!1), t = p({
      chooseName: F.instrument_booking_config_chooseName,
      instrumentIds: F.instrument_booking_config_chooseIds,
      available_slots: [["00:00", "23:59"]],
      max_advance_day: 1,
      min_advance: { value: "1", unit: "" },
      max_booking_duration: { value: "1", unit: "" }
    }), B = (() => {
      const a = [];
      for (let e = 0; e < 24; e++)
        for (let l = 0; l < 60; l += 30) {
          const u = `${e.toString().padStart(2, "0")}:${l.toString().padStart(2, "0")}`;
          a.push({
            value: u,
            label: u
          });
        }
      return a.push({
        value: "23:59",
        label: "23:59"
      }), a;
    })(), O = p([B]), z = (a) => {
      if (!a) return [];
      const e = B.findIndex((l) => l.value === a);
      return e === -1 ? [] : (console.log(B.slice(e + 1)), B.slice(e + 1));
    }, P = (a) => {
      console.log(t.value.available_slots, a), O.value[a] = z(t.value.available_slots[a][0]);
    };
    p("");
    const L = () => {
      var s, D;
      const a = t.value;
      let e = "";
      const l = (d) => !/^\d+(\.\d+)?$/.test(d) && Number(d) <= 0;
      if (I.value && l(a.max_advance_day) && (e = r("InstrumentBookingConfigDialog.tips1")), y.value) {
        const d = a.min_advance;
        (s = d.unit) != null && s.trim() || (e = r("InstrumentBookingConfigDialog.tips2")), l(d.value) && (e = r("InstrumentBookingConfigDialog.tips3"));
      }
      if (C.value) {
        const d = a.max_booking_duration;
        (D = d.unit) != null && D.trim() || (e = r("InstrumentBookingConfigDialog.tips2")), l(d.value) && (e = r("InstrumentBookingConfigDialog.tips4"));
      }
      let u = t.value.available_slots.length;
      return t.value.available_slots = t.value.available_slots.filter((d) => d[0] !== d[1]), e = u !== t.value.available_slots.length ? r("InstrumentBookingConfigDialog.tips5") : e, console.log(e), e.length > 0 && x({
        showClose: !0,
        message: e,
        type: "error",
        offset: window.innerHeight / 8
      }), e;
    }, q = (a) => {
      const e = a.some((l) => l.includes(""));
      return e && x({
        showClose: !0,
        message: r("InstrumentBookingConfigDialog.tips6"),
        type: "warning",
        offset: window.innerHeight / 8
      }), !e;
    }, G = (a) => {
      const e = a.map((l) => {
        const u = M(l[0]);
        let s = M(l[1]);
        return l[1] === "00:00" && (s = 1439), [u, s];
      });
      e.sort((l, u) => l[0] - u[0]);
      for (let l = 0; l < e.length - 1; l++) {
        const [u, s] = e[l], [D, d] = e[l + 1];
        if (s > D)
          return x({
            showClose: !0,
            message: r("InstrumentBookingConfigDialog.tips7"),
            type: "error",
            offset: window.innerHeight / 8
          }), !1;
      }
      return !0;
    }, M = (a) => {
      const [e, l] = a.split(":").map(Number);
      return e * 60 + l;
    }, J = () => {
      t.value.available_slots.push(["23:30", "23:30"]);
    }, K = () => {
      t.value.available_slots.pop();
    }, Q = () => {
      if (console.log(t.value.available_slots), !L().length && G(t.value.available_slots) && q(t.value.available_slots)) {
        const { instrumentIds: a, available_slots: e, max_advance_day: l, min_advance: u, max_booking_duration: s } = t.value;
        $.ajaxFn({
          url: ELN_URL + "?r=instrument/set-instrument-booking-config",
          data: {
            instrumentIds: a,
            available_slots: e,
            max_advance_day: I ? l : null,
            min_advance: y ? u : { value: "", unit: "" },
            max_booking_duration: C ? s : { value: "1", unit: "" }
          },
          type: "POST",
          success: function(D) {
            x({
              showClose: !0,
              message: r("InstrumentBookingConfigDialog.tips8"),
              type: "success",
              offset: window.innerHeight / 8
            }), require("tab").reloadActiveTag(), H();
          }
        });
        return;
      }
    }, H = () => {
      E.value = !1, F.cancel();
    };
    return (a, e) => (m(), g("div", te, [
      n(o(W), {
        modelValue: E.value,
        "onUpdate:modelValue": e[10] || (e[10] = (l) => E.value = l),
        title: a.$t("InstrumentBookingConfigDialog.title"),
        width: "480",
        id: "isInstrumentBookingConfigDialogDialog"
      }, {
        default: i(() => [
          n(o(X), {
            "label-position": "top",
            rules: L,
            model: t.value,
            id: "isInstrumentBookingConfigDialogForm"
          }, {
            default: i(() => [
              n(o(_), { class: "instrumentBookingFormName" }, {
                default: i(() => {
                  var l;
                  return [
                    n(o(Y), {
                      content: (l = t.value.chooseName) == null ? void 0 : l.join("、"),
                      placement: "top",
                      effect: "light"
                    }, {
                      default: i(() => {
                        var u;
                        return [
                          N("p", ae, v((u = t.value.chooseName) == null ? void 0 : u.join("、")), 1)
                        ];
                      }),
                      _: 1
                    }, 8, ["content"])
                  ];
                }),
                _: 1
              }),
              n(o(_), {
                label: a.$t("InstrumentBookingConfigDialog.available_slots")
              }, {
                default: i(() => [
                  (m(!0), g(b, null, V(t.value.available_slots, (l, u) => (m(), g("div", {
                    key: u,
                    class: "demo-time-range"
                  }, [
                    n(o(h), {
                      modelValue: t.value.available_slots[u][0],
                      "onUpdate:modelValue": (s) => t.value.available_slots[u][0] = s,
                      placeholder: a.$t("InstrumentBookingConfigDialog.start_time"),
                      onChange: (s) => P(u),
                      style: { "min-width": "190px" }
                    }, {
                      default: i(() => [
                        (m(!0), g(b, null, V(o(B), (s) => (m(), c(o(w), {
                          key: s.value,
                          label: s.label,
                          value: s.value
                        }, null, 8, ["label", "value"]))), 128))
                      ]),
                      _: 2
                    }, 1032, ["modelValue", "onUpdate:modelValue", "placeholder", "onChange"]),
                    e[11] || (e[11] = N("span", null, "-", -1)),
                    n(o(h), {
                      modelValue: t.value.available_slots[u][1],
                      "onUpdate:modelValue": (s) => t.value.available_slots[u][1] = s,
                      placeholder: a.$t("InstrumentBookingConfigDialog.end_time"),
                      disabled: !t.value.available_slots[u][0],
                      style: { "min-width": "190px" }
                    }, {
                      default: i(() => [
                        (m(!0), g(b, null, V(O.value[u], (s) => (m(), c(o(w), {
                          key: s.value,
                          label: s.label,
                          value: s.value
                        }, null, 8, ["label", "value"]))), 128))
                      ]),
                      _: 2
                    }, 1032, ["modelValue", "onUpdate:modelValue", "placeholder", "disabled"]),
                    u === 0 ? (m(), c(o(j), {
                      key: 0,
                      size: 12,
                      color: "rgb(106, 106, 115)",
                      onClick: J,
                      style: { cursor: "pointer", "margin-left": "10px" }
                    }, {
                      default: i(() => [
                        n(o(Z))
                      ]),
                      _: 1
                    })) : t.value.available_slots.length - 1 === u ? (m(), c(o(j), {
                      key: 1,
                      size: 12,
                      color: "rgb(106, 106, 115)",
                      onClick: K,
                      style: { cursor: "pointer", "margin-left": "10px" }
                    }, {
                      default: i(() => [
                        n(o(ee))
                      ]),
                      _: 1
                    })) : (m(), g("div", ne))
                  ]))), 128))
                ]),
                _: 1
              }, 8, ["label"]),
              e[13] || (e[13] = N("hr", { style: { "border-color": "rgba(235, 238, 245, .2)", "margin-top": "-2px" } }, null, -1)),
              n(o(_), {
                label: a.$t("InstrumentBookingConfigDialog.max_advance_day")
              }, {
                default: i(() => [
                  n(o(U), {
                    modelValue: I.value,
                    "onUpdate:modelValue": e[0] || (e[0] = (l) => I.value = l)
                  }, {
                    default: i(() => [
                      n(o(k), { label: !1 }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.noLimited")), 1)
                        ]),
                        _: 1
                      }),
                      n(o(k), { label: !0 }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.limited")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, ["modelValue"]),
                  I.value ? (m(), g(b, { key: 0 }, [
                    n(o(T), {
                      class: "instrumentFormItemInput",
                      type: "number",
                      style: { width: "80px", margin: "0 8px" },
                      modelValue: t.value.max_advance_day,
                      "onUpdate:modelValue": e[1] || (e[1] = (l) => t.value.max_advance_day = l)
                    }, null, 8, ["modelValue"]),
                    e[12] || (e[12] = f("天 "))
                  ], 64)) : S("", !0)
                ]),
                _: 1
              }, 8, ["label"]),
              n(o(_), {
                class: "instrumentFormItem",
                label: a.$t("InstrumentBookingConfigDialog.min_advance")
              }, {
                default: i(() => [
                  n(o(U), {
                    modelValue: y.value,
                    "onUpdate:modelValue": e[2] || (e[2] = (l) => y.value = l)
                  }, {
                    default: i(() => [
                      n(o(k), { label: !1 }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.noLimited")), 1)
                        ]),
                        _: 1
                      }),
                      n(o(k), { label: !0 }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.limited")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, ["modelValue"]),
                  y.value ? (m(), g(b, { key: 0 }, [
                    n(o(T), {
                      class: "instrumentFormItemInput",
                      type: "number",
                      style: { width: "80px", margin: "0 8px" },
                      modelValue: t.value.min_advance.value,
                      "onUpdate:modelValue": e[3] || (e[3] = (l) => t.value.min_advance.value = l)
                    }, null, 8, ["modelValue"]),
                    n(o(h), {
                      class: "instrumentFormItemSelect",
                      modelValue: t.value.min_advance.unit,
                      "onUpdate:modelValue": e[4] || (e[4] = (l) => t.value.min_advance.unit = l),
                      placeholder: a.$t("InstrumentBookingConfigDialog.select"),
                      style: { width: "120px" }
                    }, {
                      default: i(() => [
                        (m(!0), g(b, null, V(A.value, (l) => (m(), c(o(w), {
                          key: l.value,
                          label: l.label,
                          value: l.value
                        }, null, 8, ["label", "value"]))), 128))
                      ]),
                      _: 1
                    }, 8, ["modelValue", "placeholder"])
                  ], 64)) : S("", !0)
                ]),
                _: 1
              }, 8, ["label"]),
              n(o(_), {
                label: a.$t("InstrumentBookingConfigDialog.max_booking_duration")
              }, {
                default: i(() => [
                  n(o(U), {
                    modelValue: C.value,
                    "onUpdate:modelValue": e[5] || (e[5] = (l) => C.value = l)
                  }, {
                    default: i(() => [
                      n(o(k), {
                        label: !1,
                        name: "isMaxBookingDurationFalse"
                      }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.noLimited")), 1)
                        ]),
                        _: 1
                      }),
                      n(o(k), {
                        label: !0,
                        name: "isMaxBookingDurationTrue"
                      }, {
                        default: i(() => [
                          f(v(o(r)("InstrumentBookingConfigDialog.limited")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, ["modelValue"]),
                  C.value ? (m(), g(b, { key: 0 }, [
                    n(o(T), {
                      class: "instrumentFormItemInput",
                      type: "number",
                      style: { width: "80px", margin: "0 8px" },
                      modelValue: t.value.max_booking_duration.value,
                      "onUpdate:modelValue": e[6] || (e[6] = (l) => t.value.max_booking_duration.value = l)
                    }, null, 8, ["modelValue"]),
                    n(o(h), {
                      class: "instrumentFormItemSelect",
                      modelValue: t.value.max_booking_duration.unit,
                      "onUpdate:modelValue": e[7] || (e[7] = (l) => t.value.max_booking_duration.unit = l),
                      placeholder: a.$t("InstrumentBookingConfigDialog.select"),
                      style: { width: "120px" }
                    }, {
                      default: i(() => [
                        (m(!0), g(b, null, V(A.value, (l) => (m(), c(o(w), {
                          key: l.value,
                          label: l.label,
                          value: l.value
                        }, null, 8, ["label", "value"]))), 128))
                      ]),
                      _: 1
                    }, 8, ["modelValue", "placeholder"])
                  ], 64)) : S("", !0)
                ]),
                _: 1
              }, 8, ["label"]),
              n(o(_), { id: "instrument-btn" }, {
                default: i(() => [
                  n(o(R), {
                    onClick: e[8] || (e[8] = (l) => H())
                  }, {
                    default: i(() => [
                      f(v(o(r)("InstrumentBookingConfigDialog.cancel")), 1)
                    ]),
                    _: 1
                  }),
                  n(o(R), {
                    type: "primary",
                    onClick: e[9] || (e[9] = (l) => Q())
                  }, {
                    default: i(() => [
                      f(v(o(r)("InstrumentBookingConfigDialog.sure")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["model"])
        ]),
        _: 1
      }, 8, ["modelValue", "title"])
    ]));
  }
}, fe = /* @__PURE__ */ oe(ie, [["__scopeId", "data-v-706e2e0c"]]);
export {
  fe as default
};
